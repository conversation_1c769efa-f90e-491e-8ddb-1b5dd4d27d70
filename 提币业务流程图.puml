@startuml 提币业务流程图

!define PRIMARY_COLOR #4A90E2
!define SUCCESS_COLOR #7ED321
!define WARNING_COLOR #F5A623
!define DANGER_COLOR #D0021B

title 社交App提币功能业务流程图

actor "用户" as User
participant "移动端App" as App PRIMARY_COLOR
participant "API网关" as Gateway SUCCESS_COLOR
participant "提币服务" as WithdrawalService PRIMARY_COLOR
participant "基础风控" as RiskService WARNING_COLOR
participant "用户绑定服务" as BindingService PRIMARY_COLOR
participant "Point服务" as PointService PRIMARY_COLOR
participant "交易所对接" as ExchangeService SUCCESS_COLOR
participant "三方交易所" as Exchange DANGER_COLOR
database "MySQL" as DB PRIMARY_COLOR
queue "Kafka" as MQ WARNING_COLOR

== 用户绑定阶段（一次性） ==

User -> App: 点击"开启提币功能"
App -> Gateway: GET /api/v1/withdrawal/binding/status
Gateway -> BindingService: 检查绑定状态
BindingService -> DB: 查询绑定记录
BindingService --> Gateway: 返回未绑定状态
Gateway --> App: 需要绑定交易所
App --> User: 显示绑定引导页面

User -> App: 选择交易所并确认绑定
App -> Gateway: POST /api/v1/withdrawal/binding/initiate
Gateway -> BindingService: 发起绑定流程
BindingService -> DB: 创建绑定记录
BindingService -> Exchange: 生成OAuth授权链接
BindingService --> Gateway: 返回授权URL
Gateway --> App: 返回跳转链接
App --> User: 跳转到交易所页面

note over Exchange
用户在交易所完成：
1. 注册/登录
2. KYC身份认证
3. 授权绑定确认
end note

Exchange -> BindingService: 绑定成功回调
BindingService -> DB: 更新绑定状态
BindingService -> Exchange: 同步KYC状态
BindingService --> Exchange: 确认回调处理
Exchange --> User: 跳转回我们的App

User -> App: 返回App查看绑定状态
App -> Gateway: GET /api/v1/withdrawal/binding/status
Gateway -> BindingService: 查询最新状态
BindingService -> DB: 获取绑定信息
BindingService --> Gateway: 返回已绑定状态
Gateway --> App: 绑定成功，可以提币
App --> User: 显示提币功能已开启

== 提币申请阶段 ==

User -> App: 发起提币申请
App -> Gateway: GET /api/v1/withdrawal/info
Gateway -> WithdrawalService: 获取提币信息
WithdrawalService -> PointService: 查询用户余额
WithdrawalService -> BindingService: 验证绑定状态
WithdrawalService --> Gateway: 返回提币信息
Gateway --> App: 显示余额和限额
App --> User: 展示提币表单

User -> App: 填写提币信息并提交
App -> Gateway: POST /api/v1/withdrawal/apply
Gateway -> WithdrawalService: 处理提币申请

WithdrawalService -> BindingService: 验证用户绑定状态
alt 用户未绑定或绑定失效
    BindingService --> WithdrawalService: 绑定状态异常
    WithdrawalService --> Gateway: 返回绑定错误
    Gateway --> App: 提示重新绑定
    App --> User: 引导重新绑定
else 绑定状态正常
    BindingService --> WithdrawalService: 绑定验证通过
    
    WithdrawalService -> PointService: 检查账户余额
    alt 余额不足
        PointService --> WithdrawalService: 余额不足
        WithdrawalService --> Gateway: 返回余额不足错误
        Gateway --> App: 提示余额不足
        App --> User: 显示余额不足
    else 余额充足
        PointService --> WithdrawalService: 余额验证通过
        
        WithdrawalService -> RiskService: 基础风控检查
        RiskService -> DB: 查询风控记录
        RiskService -> DB: 检查黑名单
        
        alt 风控拦截
            RiskService --> WithdrawalService: 风控拦截
            WithdrawalService --> Gateway: 返回风控错误
            Gateway --> App: 提示风控拦截
            App --> User: 显示风控提示
        else 风控通过
            RiskService --> WithdrawalService: 风控检查通过
            
            WithdrawalService -> DB: 创建提币订单
            WithdrawalService -> PointService: 冻结用户资产
            PointService -> DB: 更新资产状态
            PointService --> WithdrawalService: 资产冻结成功
            
            WithdrawalService -> ExchangeService: 提交订单到交易所
            ExchangeService -> Exchange: 调用提币API
            
            alt 交易所接受订单
                Exchange --> ExchangeService: 订单创建成功
                ExchangeService -> DB: 更新订单状态为已提交
                ExchangeService --> WithdrawalService: 提交成功
                WithdrawalService --> Gateway: 返回申请成功
                Gateway --> App: 提币申请成功
                App --> User: 显示申请成功，等待处理
            else 交易所拒绝订单
                Exchange --> ExchangeService: 订单创建失败
                ExchangeService -> DB: 更新订单状态为失败
                ExchangeService -> PointService: 解冻用户资产
                ExchangeService --> WithdrawalService: 提交失败
                WithdrawalService --> Gateway: 返回提交失败
                Gateway --> App: 提币申请失败
                App --> User: 显示申请失败原因
            end
        end
    end
end

== 订单处理阶段（交易所负责） ==

note over Exchange
交易所处理流程：
1. KYC合规检查
2. 高级风控验证
3. 钱包地址验证
4. 区块链交易执行
5. 交易确认等待
end note

Exchange -> ExchangeService: 状态更新回调
ExchangeService -> DB: 更新订单状态
ExchangeService -> MQ: 发送状态变更消息

alt 提币成功
    MQ -> WithdrawalService: 消费成功消息
    WithdrawalService -> PointService: 确认扣减用户余额
    WithdrawalService -> DB: 记录交易完成
    WithdrawalService -> App: 推送成功通知
    App -> User: 通知提币成功
else 提币失败
    MQ -> WithdrawalService: 消费失败消息
    WithdrawalService -> PointService: 解冻并退回资产
    WithdrawalService -> DB: 记录交易失败
    WithdrawalService -> App: 推送失败通知
    App -> User: 通知提币失败
end

== 状态查询阶段 ==

User -> App: 查询提币状态
App -> Gateway: GET /api/v1/withdrawal/orders/{order_no}
Gateway -> WithdrawalService: 查询订单状态
WithdrawalService -> DB: 获取订单信息
WithdrawalService --> Gateway: 返回订单详情
Gateway --> App: 订单状态信息
App --> User: 显示详细状态

@enduml
