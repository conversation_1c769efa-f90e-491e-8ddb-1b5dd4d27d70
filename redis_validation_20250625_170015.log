2025-06-25 17:00:15,506 - INFO - 开始执行Redis能量数据校验
2025-06-25 17:00:15,506 - ERROR - Redis Cluster连接失败: 'dict' object has no attribute 'name'
2025-06-25 17:00:15,506 - INFO - 尝试使用单节点Redis连接...
2025-06-25 17:00:16,144 - INFO - Redis单节点连接成功
2025-06-25 17:00:16,144 - INFO - 开始校验用户基础能量点数据...
2025-06-25 17:00:18,144 - INFO - 数据库中基础能量点记录数: 129784
2025-06-25 17:00:18,224 - ERROR - 检查用户 13124337819589 的Redis数据时出错: 12871 172.31.41.108:6379
2025-06-25 17:00:18,314 - ERROR - 检查用户 18126489888909 的Redis数据时出错: 8126 172.31.37.161:6379
2025-06-25 17:00:18,401 - ERROR - 检查用户 16125790078116 的Redis数据时出错: 10463 172.31.37.161:6379
2025-06-25 17:00:18,483 - ERROR - 检查用户 13126201915718 的Redis数据时出错: 8437 172.31.37.161:6379
2025-06-25 17:00:18,584 - ERROR - 检查用户 12126347655471 的Redis数据时出错: 3623 172.31.13.215:6379
2025-06-25 17:00:18,674 - ERROR - 检查用户 12124438467656 的Redis数据时出错: 7400 172.31.37.161:6379
2025-06-25 17:00:18,770 - ERROR - 检查用户 17122457144509 的Redis数据时出错: 11951 172.31.41.108:6379
2025-06-25 17:00:18,857 - ERROR - 检查用户 12126646038580 的Redis数据时出错: 3911 172.31.13.215:6379
2025-06-25 17:00:18,938 - ERROR - 检查用户 19123456959898 的Redis数据时出错: 3015 172.31.13.215:6379
2025-06-25 17:00:19,023 - ERROR - 检查用户 14126626153934 的Redis数据时出错: 16322 172.31.41.108:6379
2025-06-25 17:00:19,116 - ERROR - 检查用户 18121968885850 的Redis数据时出错: 7021 172.31.37.161:6379
2025-06-25 17:00:19,222 - ERROR - 检查用户 11126673527981 的Redis数据时出错: 10702 172.31.37.161:6379
2025-06-25 17:00:19,310 - ERROR - 检查用户 17121980617163 的Redis数据时出错: 10645 172.31.37.161:6379
2025-06-25 17:00:19,397 - ERROR - 检查用户 11125960302986 的Redis数据时出错: 6852 172.31.37.161:6379
2025-06-25 17:00:19,481 - ERROR - 检查用户 15126664472777 的Redis数据时出错: 959 172.31.13.215:6379
2025-06-25 17:00:19,564 - ERROR - 检查用户 10126405151731 的Redis数据时出错: 3552 172.31.13.215:6379
2025-06-25 17:00:19,660 - ERROR - 检查用户 16126684754411 的Redis数据时出错: 11457 172.31.41.108:6379
2025-06-25 17:00:19,754 - ERROR - 检查用户 16121911320405 的Redis数据时出错: 4336 172.31.13.215:6379
2025-06-25 17:00:19,832 - ERROR - 检查用户 15126386620565 的Redis数据时出错: 13037 172.31.41.108:6379
2025-06-25 17:00:19,914 - ERROR - 检查用户 15126691179187 的Redis数据时出错: 7258 172.31.37.161:6379
2025-06-25 17:00:19,998 - ERROR - 检查用户 11121994291877 的Redis数据时出错: 11994 172.31.41.108:6379
2025-06-25 17:00:20,089 - ERROR - 检查用户 17126621330821 的Redis数据时出错: 8350 172.31.37.161:6379
2025-06-25 17:00:20,171 - ERROR - 检查用户 15125805643936 的Redis数据时出错: 11631 172.31.41.108:6379
2025-06-25 17:00:20,253 - ERROR - 检查用户 10126338056065 的Redis数据时出错: 12376 172.31.41.108:6379
2025-06-25 17:00:20,335 - ERROR - 检查用户 16126036860578 的Redis数据时出错: 11674 172.31.41.108:6379
2025-06-25 17:00:20,415 - ERROR - 检查用户 17126622140229 的Redis数据时出错: 4673 172.31.13.215:6379
2025-06-25 17:00:20,497 - ERROR - 检查用户 19125657636200 的Redis数据时出错: 9799 172.31.37.161:6379
2025-06-25 17:00:20,581 - ERROR - 检查用户 10126481808453 的Redis数据时出错: 6538 172.31.37.161:6379
2025-06-25 17:00:20,670 - ERROR - 检查用户 11125245512959 的Redis数据时出错: 2173 172.31.13.215:6379
2025-06-25 17:00:20,758 - ERROR - 检查用户 15126677147453 的Redis数据时出错: 13006 172.31.41.108:6379
2025-06-25 17:00:20,837 - ERROR - 检查用户 16126499759868 的Redis数据时出错: 806 172.31.13.215:6379
2025-06-25 17:00:20,944 - ERROR - 检查用户 15125349692532 的Redis数据时出错: 5881 172.31.37.161:6379
2025-06-25 17:00:21,030 - ERROR - 检查用户 18126374122579 的Redis数据时出错: 6092 172.31.37.161:6379
