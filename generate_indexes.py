#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成push_message表索引创建语句（0-1023）
"""

def generate_index_sql():
    """生成1024张表的索引创建语句"""
    
    # 索引定义
    indexes = [
        ("uid_index", "uid", "用户ID索引"),
        ("type_index", "type", "类型索引"), 
        ("category_index", "category_id", "类型索引"),
        ("from_uid_index", "from_uid", "互动来源用户ID索引"),
        ("idempotent_index", "idempotent", "幂等索引")
    ]
    
    # 生成SQL语句
    sql_statements = []
    
    for table_num in range(1024):
        table_name = f"media_message.push_message{table_num}"
        
        for index_name, column, comment in indexes:
            sql = f"alter table {table_name} add KEY `{index_name}` (`{column}`) USING BTREE COMMENT '{comment}';"
            sql_statements.append(sql)
        
        # 每个表后面添加空行分隔
        sql_statements.append("")
    
    return sql_statements

def main():
    """主函数"""
    print("📊 生成push_message表索引创建语句")
    print("=" * 50)
    
    # 生成SQL语句
    sql_statements = generate_index_sql()
    
    # 写入文件
    output_file = "push_message_indexes.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- push_message表索引创建语句 (0-1023)\n")
        f.write("-- 生成时间: " + str(__import__('datetime').datetime.now()) + "\n\n")
        
        for sql in sql_statements:
            f.write(sql + "\n")
    
    print(f"✅ 已生成 {len([s for s in sql_statements if s.strip()])} 条索引创建语句")
    print(f"✅ 文件保存至: {output_file}")
    
    # 显示前几个表的示例
    print("\n📋 前3个表的示例:")
    print("-" * 40)
    count = 0
    for sql in sql_statements:
        if sql.strip():
            print(sql)
            count += 1
            if count >= 15:  # 显示前3个表的索引 (3*5=15)
                break

if __name__ == "__main__":
    main() 