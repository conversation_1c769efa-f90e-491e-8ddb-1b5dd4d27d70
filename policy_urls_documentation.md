# 多语言政策文档URL参考

本文档提供了应用中各种政策文档的多语言URL列表，包括社区公约、钱包公告、账号封禁和申诉URL。

## 政策文档URL表格

| 语言代码 | 语言名称 | 社区公约URL | 钱包公告URL | 账号封禁URL | 申诉URL |
|----------|----------|------------|------------|------------|---------|
| en-US | English (US) | https://envtest-api.x.me/policy/Community_en.html | https://envtest-api.x.me/policy/Extraction_en.html | https://envtest-api.x.me/policy/BanningAccount_en.html | https://test-outer-webapp.x.me/webview/appeal?local=en-US |
| ar-SA | Arabic | https://envtest-api.x.me/policy/Community_ar.html | https://envtest-api.x.me/policy/Extraction_ar.html | https://envtest-api.x.me/policy/BanningAccount_ar.html | https://test-outer-webapp.x.me/webview/appeal?local=ar-SA |
| bn-BD | Bengali | https://envtest-api.x.me/policy/Community_bn.html | https://envtest-api.x.me/policy/Extraction_bn.html | https://envtest-api.x.me/policy/BanningAccount_bn.html | https://test-outer-webapp.x.me/webview/appeal?local=bn-BD |
| de-DE | German | https://envtest-api.x.me/policy/Community_de.html | https://envtest-api.x.me/policy/Extraction_de.html | https://envtest-api.x.me/policy/BanningAccount_de.html | https://test-outer-webapp.x.me/webview/appeal?local=de-DE |
| es-ES | Spanish | https://envtest-api.x.me/policy/Community_es.html | https://envtest-api.x.me/policy/Extraction_es.html | https://envtest-api.x.me/policy/BanningAccount_es.html | https://test-outer-webapp.x.me/webview/appeal?local=es-ES |
| fa-IR | Persian | https://envtest-api.x.me/policy/Community_fa.html | https://envtest-api.x.me/policy/Extraction_fa.html | https://envtest-api.x.me/policy/BanningAccount_fa.html | https://test-outer-webapp.x.me/webview/appeal?local=fa-IR |
| fr-FR | French | https://envtest-api.x.me/policy/Community_fr.html | https://envtest-api.x.me/policy/Extraction_fr.html | https://envtest-api.x.me/policy/BanningAccount_fr.html | https://test-outer-webapp.x.me/webview/appeal?local=fr-FR |
| hi-IN | Hindi | https://envtest-api.x.me/policy/Community_hi.html | https://envtest-api.x.me/policy/Extraction_hi.html | https://envtest-api.x.me/policy/BanningAccount_hi.html | https://test-outer-webapp.x.me/webview/appeal?local=hi-IN |
| id-ID | Indonesian | https://envtest-api.x.me/policy/Community_id.html | https://envtest-api.x.me/policy/Extraction_id.html | https://envtest-api.x.me/policy/BanningAccount_id.html | https://test-outer-webapp.x.me/webview/appeal?local=id-ID |
| ja-JP | Japanese | https://envtest-api.x.me/policy/Community_ja.html | https://envtest-api.x.me/policy/Extraction_ja.html | https://envtest-api.x.me/policy/BanningAccount_ja.html | https://test-outer-webapp.x.me/webview/appeal?local=ja-JP |
| ko-KR | Korean | https://envtest-api.x.me/policy/Community_ko.html | https://envtest-api.x.me/policy/Extraction_ko.html | https://envtest-api.x.me/policy/BanningAccount_ko.html | https://test-outer-webapp.x.me/webview/appeal?local=ko-KR |
| pt-PT | Portuguese | https://envtest-api.x.me/policy/Community_pt.html | https://envtest-api.x.me/policy/Extraction_pt.html | https://envtest-api.x.me/policy/BanningAccount_pt.html | https://test-outer-webapp.x.me/webview/appeal?local=pt-PT |
| ru-RU | Russian | https://envtest-api.x.me/policy/Community_ru.html | https://envtest-api.x.me/policy/Extraction_ru.html | https://envtest-api.x.me/policy/BanningAccount_ru.html | https://test-outer-webapp.x.me/webview/appeal?local=ru-RU |
| th-TH | Thai | https://envtest-api.x.me/policy/Community_th.html | https://envtest-api.x.me/policy/Extraction_th.html | https://envtest-api.x.me/policy/BanningAccount_th.html | https://test-outer-webapp.x.me/webview/appeal?local=th-TH |
| tr-TH | Turkish | https://envtest-api.x.me/policy/Community_tr.html | https://envtest-api.x.me/policy/Extraction_tr.html | https://envtest-api.x.me/policy/BanningAccount_tr.html | https://test-outer-webapp.x.me/webview/appeal?local=tr-TH |
| ur-PK | Urdu | https://envtest-api.x.me/policy/Community_ur.html | https://envtest-api.x.me/policy/Extraction_ur.html | https://envtest-api.x.me/policy/BanningAccount_ur.html | https://test-outer-webapp.x.me/webview/appeal?local=ur-PK |
| vi-VN | Vietnamese | https://envtest-api.x.me/policy/Community_vi.html | https://envtest-api.x.me/policy/Extraction_vi.html | https://envtest-api.x.me/policy/BanningAccount_vi.html | https://test-outer-webapp.x.me/webview/appeal?local=vi-VN |
| zh-CN | Chinese (Simplified) | https://envtest-api.x.me/policy/Community_zh-CN.html | https://envtest-api.x.me/policy/Extraction_zh-CN.html | https://envtest-api.x.me/policy/BanningAccount_zh-CN.html | https://test-outer-webapp.x.me/webview/appeal?local=zh-CN |
| zh-TW | Chinese (Traditional) | https://envtest-api.x.me/policy/Community_zh-TW.html | https://envtest-api.x.me/policy/Extraction_zh-TW.html | https://envtest-api.x.me/policy/BanningAccount_zh-TW.html | https://test-outer-webapp.x.me/webview/appeal?local=zh-TW |

## URL模式说明

1. **社区公约URL**: `https://envtest-api.x.me/policy/Community_{language_code}.html`
2. **钱包公告URL**: `https://envtest-api.x.me/policy/Extraction_{language_code}.html`
3. **账号封禁URL**: `https://envtest-api.x.me/policy/BanningAccount_{language_code}.html`
4. **申诉URL**: `https://test-outer-webapp.x.me/webview/appeal?local={language_code}`

## 使用说明

- 根据用户的语言设置，从上表中选择相应的URL
- 对于申诉URL，需要保持与feedback页面一致的local参数格式
- 所有URL均为测试环境URL，正式环境需要替换相应的域名

## 注意事项

- 确保所有语言的政策文档内容已经准备好并上传到相应的路径
- 账号封禁URL格式与社区公约和钱包公告保持一致
- 申诉URL使用webview形式，通过local参数指定语言
