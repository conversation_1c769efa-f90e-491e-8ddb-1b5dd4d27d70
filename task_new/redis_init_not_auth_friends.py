#!/usr/bin/env python3
import pymysql
import logging
from datetime import datetime
from redis.cluster import RedisCluster

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("redis_init_not_auth_friends.log"),
        logging.StreamHandler()
    ]
)

# 媒体用户数据库配置
USER_DB = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_user'
}

def get_db_config(**kwargs):
    """
    Helper function to prepare database configuration.
    """
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

def get_redis_connection():
    """
    Get Redis cluster connection
    """
    try:
        host = "xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com"
        port = 6379
        
        redis_conn = RedisCluster(
            host=host,
            port=port,
            decode_responses=True,
            ssl=False,
            max_connections=20,
            retry_on_timeout=True
        )
        
        redis_conn.ping()
        logging.info("成功连接到Redis集群")
        return redis_conn
    except Exception as e:
        logging.error(f"连接Redis时出错: {str(e)}")
        return None

def get_not_auth_invited_users(batch_size=5000, last_relation_id=0):
    """
    批量获取未认证的被邀请用户
    """
    user_db_config = get_db_config(**USER_DB)
    
    try:
        conn = pymysql.connect(**user_db_config)
        cursor = conn.cursor()
        
        # 查询邀请关系和用户认证状态
        query = """
        SELECT 
            r.invite_uid,
            r.invited_uid,
            r.created_time,
            r.relation_id,
            u.email_verify,
            u.phone_verify,
            u.face_liveness_status
        FROM user_invite_relation r
        INNER JOIN client_user u ON r.invited_uid = u.uid
        WHERE r.relation_id > %s
        AND u.status = 1
        AND (
            u.email_verify = 0 
            AND u.phone_verify = 0 
            AND u.face_liveness_status = 0
        )
        ORDER BY r.relation_id
        LIMIT %s
        """
        
        cursor.execute(query, (last_relation_id, batch_size))
        results = cursor.fetchall()
        
        logging.info(f"查询到 {len(results)} 条未认证的邀请关系记录")
        
        cursor.close()
        conn.close()
        
        return results
        
    except Exception as e:
        logging.error(f"查询数据库时出错: {str(e)}")
        return []

def batch_store_to_redis(redis_conn, invite_relations):
    """
    批量存储未认证好友关系到Redis
    """
    try:
        # 按邀请人分组
        invite_groups = {}
        for relation in invite_relations:
            invite_uid = relation['invite_uid']
            invited_uid = relation['invited_uid']
            created_time = relation['created_time']
            
            if invite_uid not in invite_groups:
                invite_groups[invite_uid] = []
            
            # 转换时间戳为毫秒
            if isinstance(created_time, datetime):
                timestamp = int(created_time.timestamp() * 1000)
            else:
                timestamp = int(datetime.now().timestamp() * 1000)
                
            invite_groups[invite_uid].append({
                'invited_uid': invited_uid,
                'timestamp': timestamp
            })
        
        # 批量存储到Redis
        success_count = 0
        for invite_uid, invited_list in invite_groups.items():
            try:
                redis_key = f"friend:not:auth:{invite_uid}"
                
                # 准备zadd的数据
                zadd_data = {}
                for item in invited_list:
                    zadd_data[item['invited_uid']] = item['timestamp']
                
                # 批量添加到zset
                if zadd_data:
                    redis_conn.zadd(redis_key, zadd_data)
                    success_count += len(zadd_data)
                    
            except Exception as e:
                logging.error(f"存储邀请人 {invite_uid} 的数据时出错: {str(e)}")
                continue
        
        logging.info(f"成功存储 {success_count} 条未认证好友关系到Redis")
        return success_count
        
    except Exception as e:
        logging.error(f"批量存储Redis数据时出错: {str(e)}")
        return 0

def verify_redis_data(redis_conn, sample_relations):
    """
    验证Redis数据存储是否正确
    """
    try:
        verification_count = 0
        sample_size = min(5, len(sample_relations))
        
        logging.info(f"验证样本数据 {sample_size} 条:")
        
        for i in range(sample_size):
            relation = sample_relations[i]
            invite_uid = relation['invite_uid']
            invited_uid = relation['invited_uid']
            
            redis_key = f"friend:not:auth:{invite_uid}"
            score = redis_conn.zscore(redis_key, invited_uid)
            
            if score:
                logging.info(f"验证成功: 邀请人 {invite_uid} 的未认证好友 {invited_uid}, 时间戳 {int(score)}")
                verification_count += 1
            else:
                logging.warning(f"验证失败: 邀请人 {invite_uid} 的未认证好友 {invited_uid}")
        
        logging.info(f"验证完成，成功率: {verification_count}/{sample_size}")
        
    except Exception as e:
        logging.error(f"验证Redis数据时出错: {str(e)}")

def initialize_not_auth_friends():
    """
    初始化未认证好友关系到Redis
    """
    redis_conn = get_redis_connection()
    if not redis_conn:
        logging.error("无法连接Redis，程序退出")
        return
    
    try:
        batch_size = 5000
        last_relation_id = 0
        total_processed = 0
        total_stored = 0
        
        while True:
            # 批量获取数据
            relations = get_not_auth_invited_users(batch_size, last_relation_id)
            
            if not relations:
                break
            
            # 更新last_relation_id
            last_relation_id = relations[-1]['relation_id']
            
            # 批量存储到Redis
            stored_count = batch_store_to_redis(redis_conn, relations)
            total_stored += stored_count
            total_processed += len(relations)
            
            logging.info(f"处理进度: 已处理 {total_processed} 条记录, 已存储 {total_stored} 条到Redis")
            
            # 验证第一批数据
            if total_processed == len(relations):
                verify_redis_data(redis_conn, relations[:5])
            
            # 如果返回的记录数少于批次大小，说明已经处理完了
            if len(relations) < batch_size:
                break
        
        logging.info(f"初始化完成: 总共处理 {total_processed} 条邀请关系, 存储 {total_stored} 条未认证好友关系到Redis")
        
        # 统计Redis中的数据
        try:
            # 随机检查几个key的数据量
            sample_keys = []
            for relation in relations[-5:] if relations else []:
                sample_keys.append(f"friend:not:auth:{relation['invite_uid']}")
            
            for key in sample_keys:
                count = redis_conn.zcard(key)
                logging.info(f"Redis key {key} 包含 {count} 个未认证好友")
                
        except Exception as e:
            logging.error(f"统计Redis数据时出错: {str(e)}")
            
    except Exception as e:
        logging.error(f"初始化过程出错: {str(e)}")
    finally:
        if redis_conn:
            redis_conn.close()

def main():
    """
    主函数
    """
    logging.info("开始初始化未认证好友关系到Redis")
    initialize_not_auth_friends()
    logging.info("未认证好友关系初始化完成")

if __name__ == "__main__":
    main() 