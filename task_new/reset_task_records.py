import pymysql
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("reset_task_records.log"),
        logging.StreamHandler()
    ]
)

# 数据库配置
DB_CONFIG = {
    'host': 'xme-envtest-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_task',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': True
}

def reset_task_record_table(shard_num):
    """
    重置单个分表的delete_status
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        task_table = f"user_task_record_{shard_num}"
        
        # 重置delete_status为0
        reset_query = f"""
        UPDATE {task_table}
        SET delete_status = 0,
            delete_time = '1000-01-01 00:00:00'
        WHERE delete_status = 1
        """
        
        cursor.execute(reset_query)
        updated_rows = cursor.rowcount
        
        cursor.close()
        conn.close()
        
        if updated_rows > 0:
            logging.info(f"分表 {task_table}: 重置了 {updated_rows} 条记录")
        
        return updated_rows
        
    except Exception as e:
        logging.error(f"处理分表 {shard_num} 时出错: {str(e)}")
        return 0

def check_task_records():
    """
    检查模式：统计有多少条记录需要重置
    """
    try:
        total_records = 0
        affected_tables = 0
        
        for shard_num in range(1024):
            try:
                conn = pymysql.connect(**DB_CONFIG)
                cursor = conn.cursor()
                
                task_table = f"user_task_record_{shard_num}"
                
                # 统计需要重置的记录
                check_query = f"""
                SELECT COUNT(*) as record_count
                FROM {task_table}
                WHERE delete_status = 1
                """
                
                cursor.execute(check_query)
                result = cursor.fetchone()
                
                if result and result['record_count'] > 0:
                    total_records += result['record_count']
                    affected_tables += 1
                    logging.info(f"分表 {task_table}: {result['record_count']} 条记录需要重置")
                
                cursor.close()
                conn.close()
                
            except Exception as e:
                logging.error(f"检查分表 {shard_num} 时出错: {str(e)}")
                continue
        
        logging.info(f"=== 检查汇总 ===")
        logging.info(f"需要重置的表数: {affected_tables}")
        logging.info(f"需要重置的记录数: {total_records}")
        
    except Exception as e:
        logging.error(f"检查过程出错: {str(e)}")

def reset_all_task_records():
    """
    重置所有1024张user_task_record表的delete_status
    """
    try:
        total_updated = 0
        processed_tables = 0
        affected_tables = 0
        
        logging.info("开始重置所有user_task_record表...")
        
        # 处理所有1024张分表
        for shard_num in range(1024):
            updated_rows = reset_task_record_table(shard_num)
            total_updated += updated_rows
            processed_tables += 1
            
            if updated_rows > 0:
                affected_tables += 1
            
            if processed_tables % 100 == 0:
                logging.info(f"已处理 {processed_tables}/1024 张分表")
        
        logging.info(f"=== 重置完成 ===")
        logging.info(f"处理表数: {processed_tables}")
        logging.info(f"受影响表数: {affected_tables}")
        logging.info(f"重置记录数: {total_updated}")
        
        # 记录详细信息到文件
        with open('reset_task_records_summary.log', 'w') as f:
            f.write(f"重置时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"处理表数: {processed_tables}\n")
            f.write(f"受影响表数: {affected_tables}\n")
            f.write(f"重置记录数: {total_updated}\n")
        
    except Exception as e:
        logging.error(f"重置过程出错: {str(e)}")

def main():
    import argparse
    parser = argparse.ArgumentParser(description='重置user_task_record表的delete_status')
    parser.add_argument('--check', action='store_true', help='只检查数据，不做重置')
    args = parser.parse_args()

    if args.check:
        logging.info("开始检查任务...")
        check_task_records()
        logging.info("检查任务完成")
    else:
        logging.info("开始重置任务...")
        reset_all_task_records()
        logging.info("重置任务完成")

if __name__ == "__main__":
    main() 