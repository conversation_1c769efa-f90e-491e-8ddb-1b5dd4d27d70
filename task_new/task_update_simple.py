import pymysql
import logging
from datetime import datetime
from redis.cluster import RedisCluster
from collections import defaultdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("task_update_simple.log"),
        logging.StreamHandler()
    ]
)

# 数据库配置
DB_CONFIG = {
    'host': 'xme-envtest-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_task',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': True
}

def get_redis_connection():
    """获取Redis集群连接"""
    try:
        host = "xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com"
        port = 6379
        
        redis_conn = RedisCluster(
            host=host,
            port=port,
            decode_responses=True,
            ssl=False,
            max_connections=20,
            retry_on_timeout=True
        )
        
        redis_conn.ping()
        logging.info("成功连接到Redis集群")
        return redis_conn
    except Exception as e:
        logging.error(f"连接Redis时出错: {str(e)}")
        return None

def update_base_energy_table_batch(user_deductions, now):
    """
    批量更新基础能量表 - 设置为总的有效能量点
    """
    if not user_deductions:
        return []
        
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 准备批量更新的数据
        update_data = []
        for user_data in user_deductions:
            user_id = user_data['user_id']
            valid_ep = 0
            # 限制最大值950
            limited_ep = min(valid_ep, 950)
            update_data.append((limited_ep, now, user_id))
        
        # 批量更新
        update_base_ep = """
        UPDATE user_base_ep
        SET total_base_ep = %s,
            update_time = %s
        WHERE user_id = %s
        """
        
        cursor.executemany(update_base_ep, update_data)
        updated_count = cursor.rowcount
        
        cursor.close()
        conn.close()
        
        logging.info(f"批量更新基础能量表: {updated_count} 个用户")
        
        # 返回更新的用户信息
        updated_users = []
        for i, (ep, _, user_id) in enumerate(update_data):
            updated_users.append({
                'user_id': user_id,
                'total_ep': ep
            })
        
        return updated_users
        
    except Exception as e:
        logging.error(f"批量更新基础能量表时出错: {str(e)}")
        return []

def process_shard_table(shard_num, now, redis_conn):
    """
    处理单个分表 - 立即更新基础能量表
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        task_table = f"user_task_record_{shard_num}"
        ep_table = f"user_ep_records_{shard_num}"
        
        # 1. 直接更新 user_ep_records 表，不更新 task_records
        update_ep_query = f"""
        UPDATE {ep_table} e
        SET e.delete_status = 1, e.delete_time = %s
        WHERE e.ep_type = 1 
        AND e.delete_status = 0
        """
        
        cursor.execute(update_ep_query, (now,))
        updated_rows = cursor.rowcount
        
        # 2. 统计每个用户剩余有效的能量点 (delete_status = 0)
        summary_query = f"""
        SELECT 
            e.user_id,
            SUM(e.ep_amount) as total_valid_ep
        FROM {ep_table} e
        WHERE e.ep_type = 1
        GROUP BY e.user_id
        """
        
        cursor.execute(summary_query)
        user_deductions = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        # 3. 立即批量更新基础能量表
        updated_users = update_base_energy_table_batch(user_deductions, now)
        
        # 4. 删除Redis缓存
        if redis_conn and updated_users:
            user_ids = [user['user_id'] for user in updated_users]
            delete_redis_cache_batch(redis_conn, user_ids)
        
        logging.info(f"分表 {shard_num}: 更新了 {updated_rows} 条 ep 记录, 影响 {len(user_deductions)} 个用户")
        
        return updated_users
        
    except Exception as e:
        logging.error(f"处理分表 {shard_num} 时出错: {str(e)}")
        return []

def delete_redis_cache_batch(redis_conn, user_ids):
    """
    批量删除Redis缓存
    """
    try:
        if not redis_conn or not user_ids:
            return
            
        # 分批删除Redis缓存
        batch_size = 100
        for i in range(0, len(user_ids), batch_size):
            batch_ids = user_ids[i:i + batch_size]
            
            pipeline = redis_conn.pipeline()
            for uid in batch_ids:
                pipeline.delete(f"user:base:ep:{uid}")
            pipeline.execute()
        
        logging.info(f"删除了 {len(user_ids)} 个用户的Redis缓存")
        
    except Exception as e:
        logging.error(f"删除Redis缓存时出错: {str(e)}")

def simple_update_all_tables():
    """
    简化版本：直接处理所有1024张表 - 每个分表立即更新
    """
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 连接Redis
    redis_conn = get_redis_connection()
    
    all_updated_users = []
    processed_shards = 0
    
    try:
        # 处理所有1024张分表
        for shard_num in range(1024):
            updated_users = process_shard_table(shard_num, now, redis_conn)
            all_updated_users.extend(updated_users)
            processed_shards += 1
            
            if processed_shards % 100 == 0:
                logging.info(f"已处理 {processed_shards}/1024 张分表")
        
        logging.info(f"所有分表处理完成: 共处理 {processed_shards} 张表")
        
        # 记录最终汇总
        if all_updated_users:
            # 记录详细信息
            with open('simple_update_details.log', 'w') as f:
                total_energy = 0
                for user in all_updated_users:
                    f.write(f"用户ID: {user['user_id']}, 设置能量点: {user['total_ep']}\n")
                    total_energy += user['total_ep']
                
                f.write(f"\n=== 汇总 ===\n")
                f.write(f"总用户数: {len(all_updated_users)}\n")
                f.write(f"总能量点: {total_energy}\n")
            
            logging.info(f"=== 最终汇总 ===")
            logging.info(f"受影响用户数: {len(all_updated_users)}")
            logging.info(f"总能量点: {sum(u['total_ep'] for u in all_updated_users)}")
        else:
            logging.info("没有需要更新的数据")
            
    except Exception as e:
        logging.error(f"处理过程出错: {str(e)}")
    finally:
        if redis_conn:
            redis_conn.close()

def check_simple():
    """
    检查模式：统计会影响多少数据
    """
    try:
        total_affected_records = 0
        total_affected_users = 0
        
        for shard_num in range(1024):
            try:
                conn = pymysql.connect(**DB_CONFIG)
                cursor = conn.cursor()
                
                task_table = f"user_task_record_{shard_num}"
                ep_table = f"user_ep_records_{shard_num}"
                
                # 统计会被影响的记录
                check_query = f"""
                SELECT 
                    COUNT(*) as record_count,
                    COUNT(DISTINCT e.user_id) as user_count,
                    SUM(e.ep_amount) as total_ep
                FROM {ep_table} e
                INNER JOIN {task_table} t ON e.task_record_id = t.id
                WHERE e.ep_type = 1 
                AND e.delete_status = 0
                AND t.task_type = 1
                AND t.task_code NOT IN ('10002','10003','10011')
                AND t.delete_status = 0
                """
                
                cursor.execute(check_query)
                result = cursor.fetchone()
                
                if result and result['record_count'] > 0:
                    total_affected_records += result['record_count']
                    total_affected_users += result['user_count']
                    
                    logging.info(f"分表 {shard_num}: {result['record_count']} 条记录, "
                               f"{result['user_count']} 个用户, {result['total_ep']} 能量点")
                
                cursor.close()
                conn.close()
                
            except Exception as e:
                logging.error(f"检查分表 {shard_num} 时出错: {str(e)}")
                continue
        
        logging.info(f"=== 检查汇总 ===")
        logging.info(f"总影响记录数: {total_affected_records}")
        logging.info(f"总影响用户数: {total_affected_users}")
        
    except Exception as e:
        logging.error(f"检查过程出错: {str(e)}")

def main():
    import argparse
    parser = argparse.ArgumentParser(description='用户能量点更新工具 - 简化版本')
    parser.add_argument('--check', action='store_true', help='只检查数据，不做更新')
    args = parser.parse_args()

    if args.check:
        logging.info("开始检查任务...")
        check_simple()
        logging.info("检查任务完成")
    else:
        logging.info("开始更新任务...")
        simple_update_all_tables()
        logging.info("更新任务完成")

if __name__ == "__main__":
    main() 