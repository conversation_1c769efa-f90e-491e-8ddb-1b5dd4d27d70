#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis能量数据校验脚本
校验数据库与Redis中的能量数据一致性
"""

import pymysql
import redis
from rediscluster import RedisCluster
import logging
from datetime import datetime
from collections import defaultdict
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"redis_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)

# 任务数据库配置
TASK_DB = {
    'host': 'xme-prod-media-task-readonly.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task'
}

# Redis Cluster配置
REDIS_CLUSTER_CONFIG = {
    'startup_nodes': [
        {'host': 'xme-prod-task.otyftu.clustercfg.apse1.cache.amazonaws.com', 'port': 6379},
    ],
    'decode_responses': True,
    'skip_full_coverage_check': True,
}

def get_db_config(**kwargs):
    """准备数据库配置"""
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

class RedisEnergyValidator:
    def __init__(self):
        self.redis_client = None
        self.inconsistencies = {
            'user_base_ep': [],
            'total_base_ep': None,
            'user_hourly_ep': [],
            'total_network_ep': None
        }
        
    def connect_redis(self):
        """连接Redis Cluster"""
        try:
            # 尝试使用Redis Cluster
            self.redis_client = RedisCluster(
                startup_nodes=REDIS_CLUSTER_CONFIG['startup_nodes'],
                decode_responses=REDIS_CLUSTER_CONFIG['decode_responses'],
                skip_full_coverage_check=REDIS_CLUSTER_CONFIG['skip_full_coverage_check']
            )
            # 测试连接
            self.redis_client.ping()
            logging.info("Redis Cluster连接成功")
            return True
        except Exception as e:
            logging.error(f"Redis Cluster连接失败: {e}")
            # 如果cluster连接失败，尝试使用单节点连接
            try:
                logging.info("尝试使用单节点Redis连接...")
                first_node = REDIS_CLUSTER_CONFIG['startup_nodes'][0]
                self.redis_client = redis.Redis(
                    host=first_node['host'], 
                    port=first_node['port'],
                    decode_responses=True,
                    socket_timeout=60,
                    socket_connect_timeout=60,
                    retry_on_timeout=True
                )
                self.redis_client.ping()
                logging.info("Redis单节点连接成功")
                return True
            except Exception as fallback_e:
                logging.error(f"Redis单节点连接也失败: {fallback_e}")
                # 最后尝试不带cluster功能的连接
                try:
                    logging.info("尝试基础Redis连接...")
                    first_node = REDIS_CLUSTER_CONFIG['startup_nodes'][0]
                    self.redis_client = redis.StrictRedis(
                        host=first_node['host'], 
                        port=first_node['port'],
                        decode_responses=True
                    )
                    self.redis_client.ping()
                    logging.info("基础Redis连接成功")
                    return True
                except Exception as basic_e:
                    logging.error(f"基础Redis连接失败: {basic_e}")
                    return False

    def validate_user_base_ep(self):
        """校验用户基础能量点数据"""
        logging.info("开始校验用户基础能量点数据...")
        
        task_conn = pymysql.connect(**get_db_config(**TASK_DB))
        
        try:
            with task_conn.cursor() as cursor:
                # 获取当前时间在远征周期内的用户基础能量点数据
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                query = """
                SELECT ube.user_id, ube.total_base_ep 
                FROM user_base_ep ube
                INNER JOIN user_expedition_status ues ON ube.user_id = ues.user_id
                WHERE ube.delete_status = 0 
                AND ues.delete_status = 0
                AND %s BETWEEN ues.expedition_start_time AND ues.expedition_end_time
                """
                cursor.execute(query, (current_time,))
                db_records = cursor.fetchall()
                
                logging.info(f"数据库中当前远征周期内的基础能量点记录数: {len(db_records)}")
                
                inconsistent_count = 0
                missing_redis_count = 0
                db_total = 0
                redis_total = 0
                
                for record in db_records:
                    user_id = record['user_id']
                    db_value = record['total_base_ep']
                    db_total += db_value
                    
                    # 检查Redis中对应的值
                    redis_key = f"user:base:ep:{user_id}"
                    
                    try:
                        redis_value = self.redis_client.get(redis_key)
                        
                        if redis_value is None:
                            missing_redis_count += 1
                            if missing_redis_count <= 10:  # 只记录前10个缺失的
                                self.inconsistencies['user_base_ep'].append({
                                    'user_id': user_id,
                                    'db_value': db_value,
                                    'redis_value': None,
                                    'issue': 'Redis键不存在'
                                })
                        else:
                            # 解析JSON格式的Redis数据
                            try:
                                redis_data = json.loads(redis_value)
                                redis_total_base_ep = redis_data.get('totalBaseEp', 0)
                                redis_total += redis_total_base_ep
                                
                                if db_value != redis_total_base_ep:
                                    inconsistent_count += 1
                                    if len(self.inconsistencies['user_base_ep']) < 20:  # 只记录前20个不一致的
                                        self.inconsistencies['user_base_ep'].append({
                                            'user_id': user_id,
                                            'db_value': db_value,
                                            'redis_value': redis_total_base_ep,
                                            'issue': '数值不一致'
                                        })
                            except json.JSONDecodeError as json_e:
                                logging.error(f"用户 {user_id} 的Redis数据JSON解析失败: {json_e}, 原始数据: {redis_value}")
                                inconsistent_count += 1
                                if len(self.inconsistencies['user_base_ep']) < 20:
                                    self.inconsistencies['user_base_ep'].append({
                                        'user_id': user_id,
                                        'db_value': db_value,
                                        'redis_value': redis_value,
                                        'issue': 'JSON解析失败'
                                    })
                    
                    except Exception as e:
                        logging.error(f"检查用户 {user_id} 的Redis数据时出错: {e}")
                        continue
                
                logging.info(f"基础能量点校验完成:")
                logging.info(f"  - 远征周期内用户记录数: {len(db_records)}")
                logging.info(f"  - Redis缺失键数量: {missing_redis_count}")
                logging.info(f"  - 数值不一致数量: {inconsistent_count}")
                logging.info(f"  - 数据库总和: {db_total}")
                logging.info(f"  - Redis总和: {redis_total}")
                
                return db_total
                
        finally:
            task_conn.close()
    
    def validate_total_base_ep(self, db_total_base):
        """校验总基础能量点"""
        logging.info("开始校验总基础能量点...")
        
        try:
            redis_total = self.redis_client.get("total:base:ep")
            
            if redis_total is None:
                self.inconsistencies['total_base_ep'] = {
                    'db_total': db_total_base,
                    'redis_total': None,
                    'issue': 'Redis键total:base:ep不存在'
                }
                logging.warning("Redis键 total:base:ep 不存在")
            else:
                # 检查total:base:ep是否也是JSON格式
                try:
                    # 先尝试作为JSON解析
                    redis_data = json.loads(redis_total)
                    if isinstance(redis_data, dict) and 'totalBaseEp' in redis_data:
                        redis_total_value = redis_data['totalBaseEp']
                    else:
                        # 如果JSON中没有totalBaseEp字段，尝试直接取值
                        redis_total_value = int(redis_total)
                except json.JSONDecodeError:
                    # 如果不是JSON格式，直接作为数字处理
                    redis_total_value = int(redis_total)
                
                if db_total_base != redis_total_value:
                    self.inconsistencies['total_base_ep'] = {
                        'db_total': db_total_base,
                        'redis_total': redis_total_value,
                        'issue': f'总基础能量不一致，差值: {db_total_base - redis_total_value}'
                    }
                    logging.warning(f"总基础能量不一致: DB={db_total_base}, Redis={redis_total_value}")
                else:
                    logging.info("总基础能量校验通过")
                    
        except Exception as e:
            logging.error(f"校验总基础能量时出错: {e}")
    
    def validate_user_hourly_ep(self):
        """校验用户每小时影响力能量点"""
        logging.info("开始校验用户每小时影响力能量点...")
        
        task_conn = pymysql.connect(**get_db_config(**TASK_DB))
        
        try:
            with task_conn.cursor() as cursor:
                # 获取当前小时的时间戳
                current_hour = datetime.now().strftime('%Y-%m-%d %H:00:00')
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # 获取当前小时且在远征周期内的每小时能量点数据
                query = """
                SELECT uhe.user_id, uhe.expedition_id, uhe.hourly, uhe.hour_ep, uhe.bonus_ep,
                       (uhe.hour_ep + uhe.bonus_ep) as total_hourly_ep
                FROM user_hourly_ep uhe
                INNER JOIN user_expedition_status ues ON uhe.user_id = ues.user_id 
                    AND uhe.expedition_id = ues.expedition_id
                WHERE uhe.delete_status = 0
                AND ues.delete_status = 0
                AND uhe.hourly = %s
                AND %s BETWEEN ues.expedition_start_time AND ues.expedition_end_time
                ORDER BY uhe.create_time DESC
                LIMIT 1000
                """
                cursor.execute(query, (current_hour, current_time))
                db_records = cursor.fetchall()
                
                logging.info(f"当前小时 {current_hour} 且在远征周期内的能量点记录数: {len(db_records)}")
                
                inconsistent_count = 0
                missing_redis_count = 0
                
                for record in db_records:
                    user_id = record['user_id']
                    expedition_id = record['expedition_id']
                    hourly = record['hourly'].strftime('%Y%m%d%H')  # 格式化为小时字符串
                    db_total = record['total_hourly_ep']
                    
                    # 构造Redis键
                    redis_key = f"user_hourly_influence_ep:{user_id}:{expedition_id}:{hourly}"
                    
                    try:
                        redis_value = self.redis_client.get(redis_key)
                        
                        if redis_value is None:
                            missing_redis_count += 1
                            # 跳过Redis键不存在的情况（按需求要求）
                            continue
                        else:
                            redis_data = json.loads(redis_value)
                            if isinstance(redis_data, dict) and 'hourEp' in redis_data:
                                redis_value = int(redis_data['hourEp']) + int(redis_data['bonusEp'])
                        
 
                            if db_total != redis_value:
                                inconsistent_count += 1
                                if len(self.inconsistencies['user_hourly_ep']) < 20:
                                    self.inconsistencies['user_hourly_ep'].append({
                                        'user_id': user_id,
                                        'expedition_id': expedition_id,
                                        'hourly': hourly,
                                        'db_value': db_total,
                                        'redis_value': redis_value,
                                        'issue': '数值不一致'
                                    })
                    
                    except Exception as e:
                        logging.error(f"检查用户 {user_id} 小时 {hourly} 的Redis数据时出错: {e}")
                        continue
                
                logging.info(f"当前小时能量点校验完成:")
                logging.info(f"  - 远征周期内检查记录数: {len(db_records)}")
                logging.info(f"  - Redis缺失键数量: {missing_redis_count} (已跳过)")
                logging.info(f"  - 数值不一致数量: {inconsistent_count}")
                
        finally:
            task_conn.close()
    
    def calculate_total_network_energy(self):
        """计算全网总能量并校验"""
        logging.info("开始计算全网总能量...")
        
        task_conn = pymysql.connect(**get_db_config(**TASK_DB))
        
        try:
            with task_conn.cursor() as cursor:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # 计算所有在远征周期内的基础能量总和
                cursor.execute("""
                    SELECT COALESCE(SUM(ube.total_base_ep), 0) as total_base
                    FROM user_base_ep ube
                    INNER JOIN user_expedition_status ues ON ube.user_id = ues.user_id
                    WHERE ube.delete_status = 0 
                    AND ues.delete_status = 0
                    AND %s BETWEEN ues.expedition_start_time AND ues.expedition_end_time
                """, (current_time,))
                total_base = cursor.fetchone()['total_base']
                
                # 计算所有在远征周期内的小时能量总和
                cursor.execute("""
                    SELECT COALESCE(SUM(uhe.hour_ep + uhe.bonus_ep), 0) as total_hourly
                    FROM user_hourly_ep uhe
                    INNER JOIN user_expedition_status ues ON uhe.user_id = ues.user_id 
                        AND uhe.expedition_id = ues.expedition_id
                    WHERE uhe.delete_status = 0
                    AND ues.delete_status = 0
                    AND %s BETWEEN ues.expedition_start_time AND ues.expedition_end_time
                """, (current_time,))
                total_hourly = cursor.fetchone()['total_hourly']
                
                db_total_network = total_base + total_hourly
                
                logging.info(f"数据库计算结果(仅远征周期内用户):")
                logging.info(f"  - 基础能量总和: {total_base}")
                logging.info(f"  - 小时能量总和: {total_hourly}")
                logging.info(f"  - 全网总能量: {db_total_network}")
                
                # 检查Redis中的总基础能量
                try:
                    redis_total_base = self.redis_client.get("total:base:ep")
                    if redis_total_base:
                        redis_total_base = int(redis_total_base)
                        logging.info(f"Redis总基础能量: {redis_total_base}")
                        
                        # 这里可以根据业务逻辑校验全网总能量
                        # 由于没有明确的Redis全网总能量键，仅记录计算结果
                        self.inconsistencies['total_network_ep'] = {
                            'db_total_base': total_base,
                            'db_total_hourly': total_hourly,
                            'db_total_network': db_total_network,
                            'redis_total_base': redis_total_base,
                            'base_diff': total_base - redis_total_base if redis_total_base else None
                        }
                    else:
                        logging.warning("Redis total:base:ep 键不存在")
                        
                except Exception as e:
                    logging.error(f"获取Redis总基础能量时出错: {e}")
                
        finally:
            task_conn.close()
    
    def generate_validation_report(self):
        """生成校验报告"""
        logging.info("生成Redis数据校验报告...")
        
        report_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"\n{'='*80}")
        print(f"Redis能量数据校验报告 - {report_time}")
        print(f"{'='*80}")
        
        # 基础能量点校验结果
        print(f"\n🔍 用户基础能量点校验 (user_base_ep vs user:base:ep:*)")
        print("-" * 60)
        if not self.inconsistencies['user_base_ep']:
            print("✅ 抽样检查无不一致数据")
        else:
            print(f"❌ 发现 {len(self.inconsistencies['user_base_ep'])} 个不一致记录 (显示前20个):")
            for item in self.inconsistencies['user_base_ep']:
                print(f"   用户ID: {item['user_id']}, DB: {item['db_value']}, Redis: {item['redis_value']}, 问题: {item['issue']}")
        
        # 总基础能量校验结果
        print(f"\n🔍 总基础能量校验 (total:base:ep)")
        print("-" * 60)
        if self.inconsistencies['total_base_ep'] is None:
            print("✅ 总基础能量一致")
        else:
            total_info = self.inconsistencies['total_base_ep']
            print(f"❌ {total_info['issue']}")
            print(f"   数据库总和: {total_info['db_total']}")
            print(f"   Redis值: {total_info['redis_total']}")
        
        # 每小时能量点校验结果
        print(f"\n🔍 用户每小时影响力能量点校验 (user_hourly_influence_ep:*)")
        print("-" * 60)
        if not self.inconsistencies['user_hourly_ep']:
            print("✅ 抽样检查无不一致数据")
        else:
            print(f"❌ 发现 {len(self.inconsistencies['user_hourly_ep'])} 个不一致记录 (显示前20个):")
            for item in self.inconsistencies['user_hourly_ep'][:20]:
                print(f"   用户ID: {item['user_id']}, 远征ID: {item['expedition_id']}, 小时: {item['hourly']}")
                print(f"   DB: {item['db_value']}, Redis: {item['redis_value']}, 问题: {item['issue']}")
        
        # 全网总能量计算结果
        print(f"\n🔍 全网总能量计算")
        print("-" * 60)
        if self.inconsistencies['total_network_ep']:
            network_info = self.inconsistencies['total_network_ep']
            print(f"📊 数据库计算结果:")
            print(f"   基础能量总和: {network_info['db_total_base']:,}")
            print(f"   小时能量总和: {network_info['db_total_hourly']:,}")
            print(f"   全网总能量: {network_info['db_total_network']:,}")
            if network_info['redis_total_base'] is not None:
                print(f"📊 Redis total:base:ep: {network_info['redis_total_base']:,}")
                if network_info['base_diff'] is not None:
                    if network_info['base_diff'] == 0:
                        print("✅ 基础能量总和一致")
                    else:
                        print(f"❌ 基础能量差值: {network_info['base_diff']:,}")
        
        # 导出详细报告
        self.export_validation_report()
    
    def export_validation_report(self):
        """导出详细校验报告到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"redis_validation_report_{timestamp}.json"
        
        report_data = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'inconsistencies': self.inconsistencies,
            'summary': {
                'user_base_ep_issues': len(self.inconsistencies['user_base_ep']),
                'total_base_ep_consistent': self.inconsistencies['total_base_ep'] is None,
                'user_hourly_ep_issues': len(self.inconsistencies['user_hourly_ep'])
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
        
        logging.info(f"详细校验报告已导出到文件: {filename}")

def main():
    """主函数"""
    logging.info("开始执行Redis能量数据校验")
    
    validator = RedisEnergyValidator()
    
    # 连接Redis Cluster
    if not validator.connect_redis():
        logging.error("无法连接Redis Cluster，退出校验")
        return
    
    try:
        # 校验用户基础能量点
        db_total_base = validator.validate_user_base_ep()
        
        # 校验总基础能量点
        validator.validate_total_base_ep(db_total_base)
        
        # 校验用户每小时能量点
        validator.validate_user_hourly_ep()
        
        # 计算全网总能量
        validator.calculate_total_network_energy()
        
        # 生成报告
        validator.generate_validation_report()
        
        logging.info("Redis能量数据校验完成")
        
    except Exception as e:
        logging.error(f"校验过程中发生错误: {e}")
    finally:
        if validator.redis_client:
            try:
                validator.redis_client.close()
            except:
                pass

if __name__ == "__main__":
    main() 