# XME Mining System - Technical Design Document

## 1. System Overview

### 1.1 Purpose
This document outlines the technical implementation of the XME mining system for X.ME platform, which rewards users with XME tokens based on their platform engagement through an hourly mining mechanism.

### 1.2 Scope
The system encompasses:
- Hourly mining calculations and token distribution
- Energy point accumulation mechanisms
- Task completion tracking
- User interface for mining activities
- Data storage and processing architecture

### 1.3 Key Concepts
- **XME Token**: Platform's incentive token distributed to users
- **Energy Points (EP)**: Quantified user contribution metrics
- **Base Energy Points**: Long-term points affecting all hourly distributions
- **Hourly Energy Points**: Short-term points affecting only current hour distribution
- **Mining Pool**: Daily allocation of XME tokens (15M real, 97.5M displayed)

## 2. System Architecture

### 2.1 High-Level Architecture
```
┌───────────────────┐    ┌───────────────────┐    ┌───────────────────┐
│                   │    │                   │    │                   │
│  Client Apps      │◄───┤  API Gateway      │◄───┤  Mining Service   │
│  (iOS/Android/Web)│    │                   │    │                   │
│                   │    │                   │    │                   │
└───────────────────┘    └───────────────────┘    └───────────────────┘
                                                          ▲
                                                          │
                                                          ▼
┌───────────────────┐    ┌───────────────────┐    ┌───────────────────┐
│                   │    │                   │    │                   │
│  User Service     │◄───┤  Task Service     │◄───┤  Calculation      │
│                   │    │                   │    │  Engine           │
│                   │    │                   │    │                   │
└───────────────────┘    └───────────────────┘    └───────────────────┘
        ▲                        ▲                        ▲
        │                        │                        │
        ▼                        ▼                        ▼
┌───────────────────────────────────────────────────────────────────────┐
│                                                                       │
│                          Database Layer                               │
│                                                                       │
└───────────────────────────────────────────────────────────────────────┘
```

### 2.2 Component Description

#### 2.2.1 Client Applications
- Displays mining interface with space expedition theme
- Shows real-time energy points and estimated XME earnings
- Provides task completion interface
- Visualizes mining status and countdown timers

#### 2.2.2 API Gateway
- Routes requests to appropriate microservices
- Handles authentication and rate limiting
- Provides unified API endpoints for client applications

#### 2.2.3 Mining Service
- Core service managing the mining lifecycle
- Processes hourly mining calculations
- Manages mining pool distribution
- Coordinates with other services

#### 2.2.4 User Service
- Manages user profiles and authentication
- Tracks user registration dates for mining eligibility
- Handles interest selection and profile completion

#### 2.2.5 Task Service
- Defines and manages available tasks
- Tracks task completion status
- Validates task completion requirements
- Awards energy points upon task completion

#### 2.2.6 Calculation Engine
- Performs complex calculations for mining distribution
- Implements the mining formulas and algorithms
- Handles real-time estimation of XME earnings
- Manages the display multiplier (6.5x) calculations

#### 2.2.7 Database Layer
- Stores all persistent data for the system
- Includes user data, mining history, task completion records
- Optimized for high-frequency read/write operations

## 3. Database Design

### 3.1 Schema Design

#### 3.1.1 User Mining Table
```sql
CREATE TABLE user_mining (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    registration_time TIMESTAMP NOT NULL,
    first_login_time TIMESTAMP,
    total_xme_earned DECIMAL(20,8) DEFAULT 0,
    daily_xme_earned DECIMAL(20,8) DEFAULT 0,
    base_energy_points INT DEFAULT 0,
    last_mining_date DATE,
    popup_count INT DEFAULT 0,
    last_popup_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
);
```

#### 3.1.2 User Interest Table
```sql
CREATE TABLE user_interests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    interest_category_id BIGINT NOT NULL,
    interest_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_category_id (interest_category_id)
);
```

#### 3.1.3 Interest Category Table
```sql
CREATE TABLE interest_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    display_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3.1.4 Interest Table
```sql
CREATE TABLE interests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    display_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_id (category_id)
);
```

#### 3.1.5 Energy Points Record Table
```sql
CREATE TABLE energy_points_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    points_type ENUM('base', 'hourly') NOT NULL,
    points_amount INT NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    source_id BIGINT,
    task_id BIGINT,
    hour_timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_hour_timestamp (hour_timestamp)
);
```

#### 3.1.6 XME Distribution Record Table
```sql
CREATE TABLE xme_distribution_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    hour_timestamp TIMESTAMP NOT NULL,
    user_energy_points INT NOT NULL,
    network_energy_points BIGINT NOT NULL,
    xme_amount DECIMAL(20,8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_hour_timestamp (hour_timestamp)
);
```

#### 3.1.7 Task Definition Table
```sql
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    task_type ENUM('base', 'hourly', 'growth', 'invitation') NOT NULL,
    points_type ENUM('base', 'hourly') NOT NULL,
    points_amount INT NOT NULL,
    daily_limit INT DEFAULT 1,
    hourly_limit INT DEFAULT 1,
    lifetime_limit INT DEFAULT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3.1.8 User Task Completion Table
```sql
CREATE TABLE user_task_completions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    task_id BIGINT NOT NULL,
    completion_time TIMESTAMP NOT NULL,
    points_awarded INT NOT NULL,
    is_claimed BOOLEAN DEFAULT FALSE,
    claim_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_task_id (task_id),
    INDEX idx_completion_time (completion_time)
);
```

#### 3.1.9 Mining Popup Record Table
```sql
CREATE TABLE mining_popup_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    popup_time TIMESTAMP NOT NULL,
    popup_type ENUM('first_login', 'zero_interests', 'few_interests', 'scheduled') NOT NULL,
    user_action ENUM('completed', 'skipped', 'closed') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
);
```

### 3.2 Database Optimization Strategies

#### 3.2.1 Partitioning
- Partition `energy_points_records` and `xme_distribution_records` by hour_timestamp
- Use RANGE partitioning by month for historical data

#### 3.2.2 Indexing Strategy
- Compound indexes for frequent query patterns
- Covering indexes for high-volume queries

#### 3.2.3 Caching Layer
- Redis cache for frequently accessed data:
  - Current hour network energy points
  - User's current energy points
  - Task completion status

## 4. API Design

### 4.1 Mining Service APIs

#### 4.1.1 Check Mining Status
```
GET /api/v1/mining/status
```
Response:
```json
{
  "hourly_countdown_seconds": 1800,
  "daily_countdown_seconds": 38400,
  "user_base_energy_points": 500,
  "user_hourly_energy_points": 350,
  "user_total_energy_points": 850,
  "network_energy_points": 5525000,
  "displayed_network_energy_points": 35912500,
  "estimated_hourly_xme": 245.8765,
  "daily_xme_earned": 1250.4321,
  "mining_active": true
}
```

#### 4.1.2 Start Mining
```
POST /api/v1/mining/start
```
Response:
```json
{
  "success": true,
  "mining_active": true,
  "energy_points_awarded": 50,
  "message": "Mining started successfully"
}
```

#### 4.1.3 Claim Hourly XME
```
POST /api/v1/mining/claim
```
Response:
```json
{
  "success": true,
  "xme_amount": 245.8765,
  "total_daily_xme": 1496.3086,
  "message": "XME claimed successfully"
}
```

### 4.2 Task Service APIs

#### 4.2.1 Get Available Tasks
```
GET /api/v1/tasks
```
Response:
```json
{
  "base_tasks": [
    {
      "id": 1,
      "name": "Daily Sign-in",
      "description": "Start your expedition for the day",
      "points_amount": 50,
      "points_type": "base",
      "status": "completed",
      "daily_limit": 1,
      "completion_count": 1
    },
    // More tasks...
  ],
  "hourly_tasks": [
    {
      "id": 101,
      "name": "Like Challenge",
      "description": "Like 5 posts in the next hour",
      "points_amount": 50,
      "points_type": "hourly",
      "status": "in_progress",
      "hourly_limit": 1,
      "completion_count": 0,
      "progress": 3,
      "target": 5
    },
    // More tasks...
  ],
  "growth_tasks": [
    // Growth tasks...
  ]
}
```

#### 4.2.2 Complete Task
```
POST /api/v1/tasks/{taskId}/complete
```
Response:
```json
{
  "success": true,
  "task_id": 101,
  "points_awarded": 50,
  "points_type": "hourly",
  "message": "Task completed successfully"
}
```

#### 4.2.3 Claim Task Reward
```
POST /api/v1/tasks/{taskId}/claim
```
Response:
```json
{
  "success": true,
  "task_id": 101,
  "points_awarded": 50,
  "points_type": "hourly",
  "message": "Reward claimed successfully"
}
```

### 4.3 Interest Selection APIs

#### 4.3.1 Get Interest Categories
```
GET /api/v1/interests/categories
```
Response:
```json
{
  "categories": [
    {
      "id": 1,
      "name": "Sports",
      "description": "Sports related interests",
      "interests": [
        {
          "id": 101,
          "name": "Football",
          "description": "Football/Soccer",
          "selected": false
        },
        // More interests...
      ]
    },
    // More categories...
  ]
}
```

#### 4.3.2 Save User Interests
```
POST /api/v1/interests/save
```
Request:
```json
{
  "interest_ids": [101, 102, 205, 301]
}
```
Response:
```json
{
  "success": true,
  "points_awarded": 80,
  "message": "Interests saved successfully",
  "category_count": 3
}
```

#### 4.3.3 Check Interest Popup Status
```
GET /api/v1/interests/popup/status
```
Response:
```json
{
  "should_show_popup": true,
  "popup_type": "few_interests",
  "force_show": false,
  "remaining_popups": 2
}
```

## 5. Core Algorithms

### 5.1 Hourly XME Distribution Algorithm

```python
def calculate_user_hourly_xme(user_id, hour_timestamp):
    # Get user's energy points for this hour
    user_base_points = get_user_base_energy_points(user_id)
    user_hourly_points = get_user_hourly_energy_points(user_id, hour_timestamp)
    user_total_points = user_base_points + user_hourly_points
    
    # Get network total energy points for this hour
    network_total_points = get_network_total_energy_points(hour_timestamp)
    
    # Calculate user's share of the hourly pool
    daily_pool = 15000000  # 15M XME
    hourly_pool = daily_pool / 24
    
    user_share = user_total_points / network_total_points
    user_xme = user_share * hourly_pool
    
    # Apply user limits
    user_daily_limit = get_user_daily_limit(user_id)
    user_daily_earned = get_user_daily_earned(user_id, get_day_from_timestamp(hour_timestamp))
    
    max_hourly_xme = min(user_xme, user_daily_limit - user_daily_earned)
    
    return max_hourly_xme
```

### 5.2 Display Multiplier Algorithm

```python
def calculate_display_values(real_value, display_multiplier=6.5):
    """
    Convert real backend values to display values for frontend
    """
    return real_value * display_multiplier

def calculate_real_time_network_energy_display(hour_timestamp, current_minute):
    """
    Calculate the displayed network energy that smoothly increases over the hour
    """
    real_network_energy = get_real_network_energy_points(hour_timestamp)
    target_display_energy = real_network_energy * 6.5
    
    # For the first 55 minutes, smoothly increase
    if current_minute < 55:
        # Start from a lower base value (e.g., 20% of target)
        base_value = target_display_energy * 0.2
        # Calculate progress percentage (0 to 1)
        progress = current_minute / 55
        # Apply easing function for smooth animation
        eased_progress = ease_out_cubic(progress)
        # Calculate current display value
        current_display = base_value + (target_display_energy - base_value) * eased_progress
    else:
        # For the last 5 minutes, show the target value
        current_display = target_display_energy
    
    return current_display

def ease_out_cubic(x):
    """
    Cubic easing function for smooth animation
    """
    return 1 - pow(1 - x, 3)
```

### 5.3 Interest Popup Logic

```python
def should_show_interest_popup(user_id):
    user = get_user(user_id)
    interest_count = get_user_interest_category_count(user_id)
    popup_count = get_user_popup_count(user_id)
    last_popup_time = get_user_last_popup_time(user_id)
    current_time = get_current_time()
    
    # First login after registration
    if user.first_login and not user.has_seen_interest_popup:
        return {
            "should_show_popup": True,
            "popup_type": "first_login",
            "force_show": True,
            "remaining_popups": None
        }
    
    # Zero interest categories
    if interest_count == 0:
        # Check if we've shown less than 3 popups
        if popup_count < 3:
            # Check if it's been at least 2 days since last popup
            if last_popup_time is None or (current_time - last_popup_time).days >= 2:
                return {
                    "should_show_popup": True,
                    "popup_type": "zero_interests",
                    "force_show": True,
                    "remaining_popups": 3 - popup_count
                }
    
    # User skipped selection and has less than 2 categories
    if user.skipped_interest_selection and interest_count < 2:
        # Check if it's been at least 3 days since last popup
        if last_popup_time is None or (current_time - last_popup_time).days >= 3:
            return {
                "should_show_popup": True,
                "popup_type": "few_interests",
                "force_show": False,
                "remaining_popups": None
            }
    
    # Less than 3 categories
    if interest_count < 3:
        # Check if we've shown less than 3 popups
        if popup_count < 3:
            # Check if it's been at least 7 days since last popup
            if last_popup_time is None or (current_time - last_popup_time).days >= 7:
                return {
                    "should_show_popup": True,
                    "popup_type": "few_interests",
                    "force_show": False,
                    "remaining_popups": 3 - popup_count
                }
    
    # Default: don't show popup
    return {
        "should_show_popup": False,
        "popup_type": None,
        "force_show": False,
        "remaining_popups": None
    }
```

## 6. Implementation Plan

### 6.1 Phase 1: Core Infrastructure (Week 1-2)
- Database schema setup
- Basic API endpoints
- Mining service core logic
- Task service foundation

### 6.2 Phase 2: Mining Mechanics (Week 3-4)
- Energy point calculation implementation
- Hourly mining distribution algorithm
- Real-time estimation logic
- Display multiplier implementation

### 6.3 Phase 3: Task System (Week 5-6)
- Complete task definition system
- Task validation logic
- Task completion tracking
- Task reward distribution

### 6.4 Phase 4: User Interface (Week 7-8)
- Mining main interface
- Task list and completion UI
- Energy visualization
- Countdown timers and animations

### 6.5 Phase 5: Interest System (Week 9-10)
- Interest categories and selection UI
- Interest popup logic
- Interest-based task recommendations
- Interest statistics

### 6.6 Phase 6: Testing and Optimization (Week 11-12)
- Load testing
- Performance optimization
- Security review
- Final adjustments

## 7. Monitoring and Analytics

### 7.1 Key Metrics to Track
- Daily active miners
- Average energy points per user
- XME distribution statistics
- Task completion rates
- Interest selection patterns
- Popup conversion rates

### 7.2 Logging Strategy
- User mining actions
- Energy point transactions
- XME distributions
- Task completions
- System performance metrics

### 7.3 Alerting Mechanisms
- Abnormal mining patterns
- System performance degradation
- Database load thresholds
- API error rates

## 8. Security Considerations

### 8.1 Anti-Cheating Measures
- Device fingerprinting
- IP address monitoring
- Behavioral analysis
- Task completion validation
- Rate limiting

### 8.2 Data Protection
- Encryption of sensitive data
- Access control for admin operations
- Audit logging for all modifications

### 8.3 Rate Limiting
- API endpoint rate limits
- Task completion rate limits
- Mining action rate limits

## 9. Scalability Considerations

### 9.1 Database Scaling
- Read replicas for high-volume queries
- Sharding strategy for user data
- Time-series optimization for mining records

### 9.2 Service Scaling
- Horizontal scaling for API services
- Dedicated calculation nodes for mining distribution
- Caching layer for frequently accessed data

### 9.3 Load Handling
- Peak hour capacity planning
- Graceful degradation strategy
- Queue-based processing for non-critical operations

## 10. Appendix

### 10.1 Energy Point Values Reference
| Task Type | Energy Points | Points Type | Daily/Hourly Limit |
|-----------|--------------|-------------|-------------------|
| Daily Sign-in | 50 | Base | 1/day |
| Browse Content | 10 | Base | 10/day |
| Like Content | 5 | Base | 20/day |
| Comment | 20 | Base | 5/day |
| Follow User | 15 | Base | 5/day |
| Share Content | 30 | Base | 3/day |
| Hourly Challenge | 50 | Hourly | 1/hour |
| Complete Profile | 100 | Base | 1/lifetime |
| Select Interests | 80 | Base | 1/lifetime |
| First Post | 150 | Base | 1/lifetime |
| First Invitation | 200 | Base | 1/lifetime |

### 10.2 Configuration Parameters
| Parameter | Default Value | Description |
|-----------|--------------|-------------|
| DAILY_XME_POOL | 15000000 | Real daily XME distribution |
| DISPLAY_MULTIPLIER | 6.5 | Frontend display multiplier |
| HOURLY_ANIMATION_MINUTES | 55 | Minutes for smooth animation |
| HOURLY_STABLE_MINUTES | 5 | Minutes for stable display |
| MAX_DAILY_XME_PER_USER | 2000 | Maximum XME per user per day |
| ZERO_INTERESTS_MAX_POPUPS | 3 | Maximum popups for zero interests |
| ZERO_INTERESTS_POPUP_INTERVAL_DAYS | 2 | Days between zero interest popups |
| FEW_INTERESTS_POPUP_INTERVAL_DAYS | 7 | Days between few interest popups |
| SKIPPED_INTERESTS_POPUP_INTERVAL_DAYS | 3 | Days between popups for skipped users |
