import pymysql
import logging
from datetime import datetime

def award_energy_points(cursor, user_id, task_code, energy_points, source_type, task_description):
    """
    Common function to award energy points to a user:
    - Checks if the task has already been awarded
    - Creates a task record in user_task_record
    - Creates an energy record in ep_records
    - Updates the user_base_energy table if it exists
    
    Args:
        cursor: Database cursor for media_task database
        user_id: User ID to award points to
        task_code: Task code identifier
        energy_points: Number of energy points to award
        source_type: Source type for the energy record
        task_description: Description for logging
        
    Returns:
        bool: True if points were awarded, False if user already had the task
        int: Task record ID if created, None otherwise
    """
    # Check if task record already exists for this user
    check_query = """
    SELECT id FROM user_task_record 
    WHERE user_id = %s AND task_code = %s AND delete_status = 0
    """
    cursor.execute(check_query, (user_id, task_code))
    existing_record = cursor.fetchone()
    
    if existing_record:
        logging.info(f"User {user_id} already has {task_description} bonus. Skipping.")
        return False, None
    
    # Insert task record
    now = datetime.now()
    task_insert_query = """
    INSERT INTO user_task_record 
    (user_id, task_code, completion_time) 
    VALUES (%s, %s, %s)
    """
    cursor.execute(task_insert_query, (user_id, task_code, now))
    task_record_id = cursor.lastrowid
    
    # Insert energy record
    energy_insert_query = """
    INSERT INTO ep_records 
    (user_id, ep_type, ep_amount, ep_source_type, task_record_id, hourly) 
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    cursor.execute(
        energy_insert_query, 
        (user_id, 'base', energy_points, source_type, task_record_id, now)
    )
    
    # Update user_base_energy table if it exists
    try:
        base_energy_query = """
        INSERT INTO user_base_energy (user_id, total_energy) 
        VALUES (%s, %s) 
        ON DUPLICATE KEY UPDATE total_energy = total_energy + %s
        """
        cursor.execute(base_energy_query, (user_id, energy_points, energy_points))
    except pymysql.err.ProgrammingError:
        logging.warning("user_base_energy table doesn't exist. Skipping base energy update.")
    
    logging.info(f"Successfully processed user {user_id}: Added {energy_points} base energy points for {task_description}")
    return True, task_record_id

def get_db_config():
    """
    Returns a standard database configuration dictionary
    """
    return {
        'host': 'localhost',
        'user': 'root',
        'password': 'password',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }
