#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查client_user表结构的脚本
"""

import pymysql

USER_DB = {
    'host': 'xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user'
}

def get_db_config(**kwargs):
    """准备数据库配置"""
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

def check_user_structure():
    """检查用户表结构"""
    print("🔍 检查client_user表结构...")
    
    user_conn = pymysql.connect(**get_db_config(**USER_DB))
    
    try:
        with user_conn.cursor() as cursor:
            # 检查表结构
            cursor.execute("DESCRIBE client_user")
            columns = cursor.fetchall()
            
            print("📋 client_user表结构:")
            for col in columns:
                print(f"   {col['Field']}: {col['Type']} - {col['Null']} - {col['Key']} - {col['Default']}")
            
            print("\n" + "=" * 60)
            
            # 查看前几条记录
            cursor.execute("SELECT * FROM client_user WHERE phone_verify = 1 LIMIT 3")
            users = cursor.fetchall()
            
            print("📋 手机验证用户样本:")
            for i, user in enumerate(users):
                print(f"   用户 {i+1}: {user}")
            
    finally:
        user_conn.close()

if __name__ == "__main__":
    check_user_structure() 