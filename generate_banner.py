#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LOOP Space Banner 生成器
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_loop_banner():
    """创建LOOP Space Dynamic Rewards Program banner"""
    
    # 设置尺寸
    width = 800
    height = 400
    
    # 创建画布
    image = Image.new('RGB', (width, height), color='#0a0a0a')
    draw = ImageDraw.Draw(image)
    
    # 渐变背景
    for y in range(height):
        # 从深蓝到青色的渐变
        ratio = y / height
        r = int(10 + (0 - 10) * ratio)
        g = int(20 + (150 - 20) * ratio)
        b = int(40 + (200 - 40) * ratio)
        draw.rectangle([(0, y), (width, y+1)], fill=(r, g, b))
    
    # 添加几何图形装饰
    # 左上角三角形
    triangle1 = [(50, 50), (150, 50), (100, 150)]
    draw.polygon(triangle1, fill=(0, 255, 255, 100))
    
    # 右下角圆形
    draw.ellipse([width-150, height-150, width-50, height-50], 
                 fill=(255, 0, 255, 80))
    
    # 中间的六边形
    center_x, center_y = width//2 - 100, height//2
    size = 60
    hexagon = []
    import math
    for i in range(6):
        angle = i * math.pi / 3
        x = center_x + size * math.cos(angle)
        y = center_y + size * math.sin(angle)
        hexagon.append((x, y))
    draw.polygon(hexagon, fill=(0, 255, 255, 150))
    
    # 尝试加载字体
    try:
        title_font = ImageFont.truetype("arial.ttf", 36)
        subtitle_font = ImageFont.truetype("arial.ttf", 24)
        phase_font = ImageFont.truetype("arial.ttf", 20)
        text_font = ImageFont.truetype("arial.ttf", 14)
    except:
        # 如果没有找到字体，使用默认字体
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        phase_font = ImageFont.load_default()
        text_font = ImageFont.load_default()
    
    # 主标题
    title = "LOOP Space"
    title_bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    draw.text((title_x, 80), title, fill=(0, 255, 255), font=title_font)
    
    # 副标题
    subtitle = "Dynamic Rewards Program"
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    draw.text((subtitle_x, 130), subtitle, fill=(255, 255, 255), font=subtitle_font)
    
    # Phase 2 标签
    phase_bg = [(width//2 + 100, 170), (width//2 + 200, 200)]
    draw.rounded_rectangle(phase_bg, radius=15, fill=(255, 0, 150))
    draw.text((width//2 + 120, 175), "Phase 2", fill=(255, 255, 255), font=phase_font)
    
    # 描述文字
    description = [
        "LOOP Space has officially launched its exclusive rewards",
        "and benefits campaign. All users can enjoy platform",
        "privileges by completing designated tasks."
    ]
    
    start_y = 220
    for i, line in enumerate(description):
        line_bbox = draw.textbbox((0, 0), line, font=text_font)
        line_width = line_bbox[2] - line_bbox[0]
        line_x = (width - line_width) // 2
        draw.text((line_x, start_y + i * 20), line, fill=(200, 200, 200), font=text_font)
    
    # 底部装饰线
    draw.rectangle([(50, height-30), (width-50, height-25)], fill=(0, 255, 255))
    
    return image

def create_simple_banner():
    """创建简单版本的banner"""
    width = 600
    height = 200
    
    # 创建画布
    image = Image.new('RGB', (width, height), color='#1a1a2e')
    draw = ImageDraw.Draw(image)
    
    # 背景渐变
    for x in range(width):
        ratio = x / width
        r = int(26 + (0 - 26) * ratio)
        g = int(26 + (100 - 26) * ratio) 
        b = int(46 + (200 - 46) * ratio)
        draw.rectangle([(x, 0), (x+1, height)], fill=(r, g, b))
    
    # 加载字体
    try:
        font_large = ImageFont.truetype("arial.ttf", 32)
        font_small = ImageFont.truetype("arial.ttf", 16)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # 主标题
    title = "LOOP Space"
    title_bbox = draw.textbbox((0, 0), title, font=font_large)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    draw.text((title_x, 60), title, fill=(0, 255, 255), font=font_large)
    
    # 副标题
    subtitle = "Dynamic Rewards Program - Phase 2"
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=font_small)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    draw.text((subtitle_x, 110), subtitle, fill=(255, 255, 255), font=font_small)
    
    # 装饰元素
    draw.ellipse([50, 50, 100, 100], outline=(0, 255, 255), width=3)
    draw.ellipse([width-100, height-100, width-50, height-50], outline=(255, 0, 255), width=3)
    
    return image

def create_icon():
    """创建小图标"""
    size = 128
    image = Image.new('RGBA', (size, size), color=(0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # 圆形背景
    margin = 10
    draw.ellipse([margin, margin, size-margin, size-margin], 
                 fill=(26, 26, 46))
    
    # 内部图形
    center = size // 2
    # 绘制"LOOP"的抽象表示
    draw.ellipse([center-20, center-20, center+20, center+20], 
                 outline=(0, 255, 255), width=4)
    
    # 添加小点
    for i in range(0, 360, 60):
        import math
        angle = math.radians(i)
        x = center + 30 * math.cos(angle)
        y = center + 30 * math.sin(angle)
        draw.ellipse([x-3, y-3, x+3, y+3], fill=(255, 0, 255))
    
    return image

def main():
    """主函数"""
    print("正在生成LOOP Space banner...")
    
    # 创建输出目录
    os.makedirs("banners", exist_ok=True)
    
    # 生成主banner
    banner = create_loop_banner()
    banner.save("banners/loop_space_banner.png")
    print("✅ 主banner已保存: banners/loop_space_banner.png")
    
    # 生成简单版banner
    simple_banner = create_simple_banner()
    simple_banner.save("banners/loop_space_simple.png")
    print("✅ 简单版banner已保存: banners/loop_space_simple.png")
    
    # 生成图标
    icon = create_icon()
    icon.save("banners/loop_space_icon.png")
    print("✅ 图标已保存: banners/loop_space_icon.png")
    
    print("\n所有banner和图标生成完成！")
    print("文件保存在 banners/ 目录下")

if __name__ == "__main__":
    main() 