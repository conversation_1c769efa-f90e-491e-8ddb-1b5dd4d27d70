import boto3
import os
import random
import pymysql
import re
from botocore.exceptions import ClientError

# AWS 凭证配置
aws_access_key_id = '********************'
aws_secret_access_key = '/sUIsBW30jEZAXUE3BqbzK3kx5a5c25Wi/2ADjP4'
aws_region = 'ap-southeast-1'  # 根据您的 S3 存储桶区域调整

# S3 configuration
s3_client = boto3.client(
    's3',
    aws_access_key_id=aws_access_key_id,
    aws_secret_access_key=aws_secret_access_key,
    region_name=aws_region
)
bucket_name = 'xme-face-bucket'
failed_faces_prefix = 'prod/failed_faces/'
faces_prefix = 'prod/faces/'

# Database configuration
db_config = {
    'host': 'xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user',
    'port': 3306,
    'charset': 'utf8mb4'
}

# Local directory for downloaded images
local_dir = 'downloaded_samples'
os.makedirs(local_dir, exist_ok=True)

# Directory for face images
face_images_dir = os.path.join(local_dir, 'face_images')
os.makedirs(face_images_dir, exist_ok=True)

# Directory for low quality images
low_images_dir = os.path.join(local_dir, 'low_images')
os.makedirs(low_images_dir, exist_ok=True)

# Function to list all UID folders
def list_uid_folders():
    paginator = s3_client.get_paginator('list_objects_v2')
    result = paginator.paginate(Bucket=bucket_name, Prefix=failed_faces_prefix, Delimiter='/')
    
    uid_folders = []
    for page in result:
        if 'CommonPrefixes' in page:
            for prefix in page['CommonPrefixes']:
                uid_folder = prefix['Prefix']
                uid_folders.append(uid_folder)
    
    return uid_folders

# Function to get all low quality and duplicate images
def get_low_quality_images(uid_folders):
    problem_images = []
    
    for folder in uid_folders:
        response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=folder)
        
        if 'Contents' in response:
            for obj in response['Contents']:
                key = obj['Key']
                filename = os.path.basename(key)
                
                if filename.startswith('low') or filename.startswith('dup'):
                    image_type = 'low' if filename.startswith('low') else 'dup'
                    face_id = None
                    if image_type == 'dup':
                        match = re.match(r'dup(.*?)\.jpg', filename)
                        if match:
                            face_id = match.group(1)
                    if image_type == 'low':
                        match = re.match(r'low(.*?)\.jpg', filename)
                        if match:
                            face_id = match.group(1)
                    
                    # 更可靠地提取UID
                    # 路径格式应为: prod/failed_faces/<uid>/...
                    path_parts = folder.rstrip('/').split('/')
                    uid = None
                    if len(path_parts) >= 3 and path_parts[-2] == 'failed_faces':
                        uid = path_parts[-1]  # UID在failed_faces后面
                    elif len(path_parts) >= 2:
                        uid = path_parts[-1]  # 假设最后一个部分是UID
                    
                    # 尝试获取时间戳信息，如果有的话
                    timestamp = None
                    if 'LastModified' in obj:
                        timestamp = obj['LastModified']
                        print(f"Found timestamp for {key}: {timestamp}")
                    
                    problem_images.append({
                        'key': key,
                        'uid': uid,
                        'folder_path': folder,  # 保存原始文件夹路径以便调试
                        'type': image_type,
                        'face_id': face_id,
                        'timestamp': timestamp,  # 添加图片的时间戳
                        'filename': filename  # 添加文件名以便调试
                    })
    
    return problem_images

# Function to get all dup images and extract face_ids
def get_dup_images_face_ids(uid_folders):
    face_ids = []
    
    for folder in uid_folders:
        response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=folder)
        
        if 'Contents' in response:
            for obj in response['Contents']:
                key = obj['Key']
                filename = os.path.basename(key)
                
                if filename.startswith('dup'):
                    # Extract face_id from filename (remove 'dup' prefix and '.jpg' suffix)
                    face_id = re.match(r'dup(.*?)\.jpg', filename)
                    if face_id:
                        face_ids.append(face_id.group(1))
    
    return face_ids

# Function to download random samples of problem images (low quality and duplicates)
def download_random_samples(problem_images, sample_size=10):
    # 确保每种类型的图片都有样本
    low_images = [img for img in problem_images if img['type'] == 'low']
    dup_images = [img for img in problem_images if img['type'] == 'dup']
    
    # 计算每种类型要下载的数量
    low_sample_size = min(len(low_images), sample_size // 2)
    dup_sample_size = min(len(dup_images), sample_size - low_sample_size)
    
    # 如果一种类型的图片不足，增加另一种类型的数量
    if low_sample_size < sample_size // 2 and len(dup_images) > dup_sample_size:
        dup_sample_size = min(len(dup_images), sample_size - low_sample_size)
    elif dup_sample_size < (sample_size - sample_size // 2) and len(low_images) > low_sample_size:
        low_sample_size = min(len(low_images), sample_size - dup_sample_size)
    
    # 随机选择样本
    low_samples = random.sample(low_images, low_sample_size) if low_images else []
    dup_samples = random.sample(dup_images, dup_sample_size) if dup_images else []
    samples = low_samples + dup_samples
    
    # 创建目录
    low_dir = os.path.join(low_images_dir, 'low')
    dup_dir = os.path.join(low_images_dir, 'dup')
    os.makedirs(low_dir, exist_ok=True)
    os.makedirs(dup_dir, exist_ok=True)
    
    # 下载样本
    for sample in samples:
        if sample['type'] == 'low':
            target_dir = low_dir
        else:  # dup
            target_dir = dup_dir
            
        local_path = os.path.join(target_dir, os.path.basename(sample['key']))
        try:
            s3_client.download_file(bucket_name, sample['key'], local_path)
            print(f"Downloaded {sample['type']} image: {sample['key']} to {local_path}")
        except ClientError as e:
            print(f"Error downloading {sample['key']}: {e}")
    
    return len(samples)

# Function to query database for UIDs with matching face_liveness_id
def query_uids_by_face_ids(face_ids):
    uid_face_id_map = {}
    
    try:
        connection = pymysql.connect(**db_config)
        with connection.cursor() as cursor:
            # Create a parameterized query with placeholders
            placeholders = ', '.join(['%s'] * len(face_ids))
            query = f"SELECT uid, face_liveness_id FROM client_user WHERE face_liveness_id IN ({placeholders})"
            
            cursor.execute(query, face_ids)
            results = cursor.fetchall()
            
            for uid, face_id in results:
                uid_face_id_map[uid] = face_id
                
    except Exception as e:
        print(f"Database error: {e}")
    finally:
        if connection:
            connection.close()
    
    return uid_face_id_map

# Function to get face recognition confidence from logs
def get_face_recognition_confidence(uids):
    uid_confidence_map = {}
    
    if not uids:
        return uid_confidence_map
    
    try:
        connection = pymysql.connect(**db_config)
        with connection.cursor() as cursor:
            # Create a parameterized query with placeholders
            placeholders = ', '.join(['%s'] * len(uids))
            
            # Query to get all recognition logs for each UID
            query = f"""
            SELECT uid, confidence, result, fail_reason, fail_detail, created_time 
            FROM user_face_recognition_log 
            WHERE uid IN ({placeholders}) 
            ORDER BY created_time DESC
            """
            
            cursor.execute(query, list(uids))
            results = cursor.fetchall()
            
            # Group by UID, store all records
            for uid, confidence, result, fail_reason, fail_detail, created_time in results:
                if uid not in uid_confidence_map:
                    uid_confidence_map[uid] = []
                
                uid_confidence_map[uid].append({
                    'confidence': confidence,
                    'result': result,
                    'fail_reason': fail_reason,
                    'fail_detail': fail_detail,
                    'created_time': created_time
                })
                
    except Exception as e:
        print(f"Database error when querying face recognition logs: {e}")
    finally:
        if connection:
            connection.close()
    
    return uid_confidence_map

# Function to download face images for matching UIDs and problem image UIDs
def download_face_images(uid_face_id_map, problem_images=None):
    downloaded_count = 0
    face_image_paths = {}  # 存储UID和对应的图片路径，用于HTML报告
    
    # 收集所有需要下载人脸图片的UID
    all_uids = set(uid_face_id_map.keys())
    
    # 如果提供了problem_images，添加其中的UID
    if problem_images:
        for img in problem_images:
            if img.get('uid'):
                all_uids.add(img['uid'])
    
    for uid in all_uids:
        # Create a directory for each UID
        uid_dir = os.path.join(face_images_dir, str(uid))
        os.makedirs(uid_dir, exist_ok=True)
        
        # List objects in the user's face directory
        prefix = f"{faces_prefix}{uid}/"
        try:
            response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)
            
            if 'Contents' in response:
                uid_images = []
                for obj in response['Contents']:
                    key = obj['Key']
                    filename = os.path.basename(key)
                    
                    # 只下载成功的人脸图片（非low或dup开头的）
                    if not filename.startswith('low') and not filename.startswith('dup'):
                        # Download the face image
                        local_path = os.path.join(uid_dir, filename)
                        s3_client.download_file(bucket_name, key, local_path)
                        downloaded_count += 1
                        uid_images.append(local_path)
                        print(f"Downloaded face image: {key} to {local_path}")
                
                if uid_images:
                    face_image_paths[uid] = uid_images
        except ClientError as e:
            print(f"Error accessing or downloading from {prefix}: {e}")
    
    return downloaded_count, face_image_paths

# Function to find the confidence record closest to the image timestamp or matching face_id
def find_closest_confidence_record(confidence_records, image_timestamp, face_id=None):
    # 添加日志信息
    print(f"\n[DEBUG] Finding confidence record for timestamp: {image_timestamp}, face_id: {face_id}")
    print(f"[DEBUG] Total confidence records available: {len(confidence_records) if confidence_records else 0}")
    
    # 如果没有记录或时间戳，直接返回空
    if not confidence_records:
        print("[DEBUG] No confidence records available, returning None")
        return None
    
    # 打印前三条记录的时间和置信度信息供参考
    for i, record in enumerate(confidence_records[:3]):
        print(f"[DEBUG] Record {i+1}: time={record.get('created_time')}, confidence={record.get('confidence')}, result={record.get('result')}, fail_reason={record.get('fail_reason')}")
    
    # 如果提供了face_id，先尝试根据face_id匹配
    if face_id:
        print(f"[DEBUG] Trying to match by face_id: {face_id}")
        for record in confidence_records:
            # 检查fail_detail中是否包含该face_id
            fail_detail = record.get('fail_detail', '')
            if fail_detail and face_id in str(fail_detail):
                print(f"[DEBUG] Found matching record by face_id: {face_id}")
                return record
    
    # 如果没有根据face_id匹配到或没有提供face_id，尝试根据时间戳匹配
    if not image_timestamp or image_timestamp == 'unknown':
        print("[DEBUG] No valid image timestamp available, returning first record")
        return confidence_records[0] if confidence_records else None
        
    print(f"[DEBUG] Trying to match by timestamp: {image_timestamp}")
    
    # Convert image_timestamp to datetime if it's not already
    if not isinstance(image_timestamp, datetime.datetime):
        try:
            # Try to parse the timestamp if it's a string
            if isinstance(image_timestamp, str):
                image_timestamp = datetime.datetime.fromisoformat(image_timestamp.replace('Z', '+00:00'))
                print(f"[DEBUG] Converted string timestamp to datetime: {image_timestamp}")
        except Exception as e:
            print(f"[DEBUG] Error parsing image timestamp: {e}")
            return confidence_records[0] if confidence_records else None
    
    # Find the record with the closest timestamp
    closest_record = None
    min_time_diff = float('inf')
    
    for i, record in enumerate(confidence_records):
        record_time = record.get('created_time')
        if record_time:
            # Calculate time difference in seconds
            time_diff = abs((record_time - image_timestamp).total_seconds())
            if i < 3:  # 只打印前三条记录的时间差供参考
                print(f"[DEBUG] Record {i+1} time diff: {time_diff} seconds")
            if time_diff < min_time_diff:
                min_time_diff = time_diff
                closest_record = record
    
    # If no record with valid timestamp found, return the first record
    if closest_record is None and confidence_records:
        print("[DEBUG] No record with valid timestamp found, returning first record")
        closest_record = confidence_records[0]
    else:
        print(f"[DEBUG] Found closest record with time diff: {min_time_diff} seconds")
        print(f"[DEBUG] Closest record: time={closest_record.get('created_time')}, confidence={closest_record.get('confidence')}, result={closest_record.get('result')}")
    
    return closest_record

# Function to generate HTML report
def get_failure_reason_html(confidence_info):
    """根据置信度记录生成失败原因的HTML"""
    if not confidence_info:
        return ''
    
    fail_reason = confidence_info.get('fail_reason', '')
    fail_detail = confidence_info.get('fail_detail', '')
    
    reason_html = '<p class="failure-reason">未通过原因: '
    
    # 检查是否是重复人脸
    is_duplicate = False
    
    # 如果fail_reason等于1，则表示重复人脸
    if fail_reason == 1:
        is_duplicate = True
        reason_html += '重复人脸'
    elif fail_reason:
        reason_html += f'{fail_reason}'
        
        # 检查文本中是否含有重复人脸的关键词
        if isinstance(fail_reason, str) and ('duplicate' in fail_reason.lower() or 'dup' in fail_reason.lower() or '重复' in fail_reason):
            is_duplicate = True
    
    # 检查fail_detail中是否含有重复人脸的关键词
    if not is_duplicate and fail_detail and isinstance(fail_detail, str):
        if 'duplicate' in fail_detail.lower() or 'dup' in fail_detail.lower() or '重复' in fail_detail:
            is_duplicate = True
    
    # 如果是重复人脸但还没有添加注释，则添加注释
    if is_duplicate and fail_reason != 1:
        reason_html += ' (可能是重复人脸)'
    
    reason_html += '</p>'
    return reason_html

def generate_html_report(problem_images, face_image_paths, face_ids, uid_confidence_map=None, uid_face_id_map=None):
    html_file_path = os.path.join(local_dir, 'face_report.html')
    
    # 获取问题图片的本地路径
    problem_image_paths = []
    for img in problem_images:
        if img['type'] == 'low':
            local_path = os.path.join(low_images_dir, 'low', os.path.basename(img['key']))
        else:  # dup
            local_path = os.path.join(low_images_dir, 'dup', os.path.basename(img['key']))
            
        if os.path.exists(local_path):
            problem_image_paths.append({
                'path': os.path.relpath(local_path, local_dir),
                'uid': img['uid'],
                'key': img['key'],
                'filename': os.path.basename(img['key']),
                'type': img['type'],
                'face_id': img.get('face_id')
            })
    
    # 分类问题图片
    low_quality_paths = [img for img in problem_image_paths if img['type'] == 'low']
    dup_image_paths = [img for img in problem_image_paths if img['type'] == 'dup']
    
    # 创建UID到问题图片的映射
    uid_to_problem_images = {}
    for img in problem_image_paths:
        uid = str(img['uid'])  # 确保UID是字符串类型
        if uid not in uid_to_problem_images:
            uid_to_problem_images[uid] = []
        uid_to_problem_images[uid].append(img)
    
    # 生成HTML内容
    html_content = f'''
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>人脸图片分析报告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3 {{ color: #333; }}
            .section {{ margin-bottom: 30px; }}
            .image-container {{ display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px; }}
            .image-card {{ border: 1px solid #ddd; padding: 10px; border-radius: 5px; width: 220px; }}
            .image-card img {{ max-width: 200px; max-height: 200px; display: block; margin: 0 auto; }}
            .image-info {{ margin-top: 10px; font-size: 12px; }}
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .summary {{ background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            .comparison-container {{ display: flex; flex-direction: column; margin-bottom: 30px; border: 1px solid #ccc; padding: 15px; border-radius: 5px; }}
            .comparison-title {{ margin-bottom: 10px; font-weight: bold; }}
            .comparison-row {{ display: flex; flex-wrap: wrap; margin-bottom: 20px; }}
            .comparison-item {{ flex: 1; min-width: 300px; margin: 5px; }}
            .comparison-label {{ font-weight: bold; margin-bottom: 5px; }}
            .toggle-button {{ background-color: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 10px; }}
            .toggle-button:hover {{ background-color: #45a049; }}
            .low-quality {{ background-color: #ffeeee; }}
            .dup {{ background-color: #eeeeff; }}
            .badge {{ display: inline-block; padding: 3px 8px; border-radius: 3px; font-size: 12px; margin-right: 5px; }}
            .badge-low {{ background-color: #ff9999; color: #fff; }}
            .badge-dup {{ background-color: #9999ff; color: #fff; }}
            .confidence {{ font-weight: bold; }}
            .confidence-high {{ color: green; }}
            .confidence-medium {{ color: orange; }}
            .confidence-low {{ color: red; }}
            .info-box {{ background-color: #f8f9fa; border: 1px solid #ddd; padding: 10px; margin-top: 10px; border-radius: 5px; }}
        </style>
        <script>
            function toggleSection(sectionId) {{
                const section = document.getElementById(sectionId);
                if (section.style.display === "none") {{
                    section.style.display = "block";
                }} else {{
                    section.style.display = "none";
                }}
            }}
        </script>
    </head>
    <body>
        <h1>人脸图片分析报告</h1>
        
        <div class="summary">
            <h2>摘要</h2>
            <p>问题图片总数: {len(problem_images)}</p>
            <p>下载的样本图片数量: {len(problem_image_paths)}</p>
            <p> - 下载的低质量图片样本: {len(low_quality_paths)}</p>
            <p> - 下载的重复图片样本: {len(dup_image_paths)}</p>
        </div>
        
        <button class="toggle-button" onclick="toggleSection('lowQualitySection')">显示/隐藏低质量图片样本</button>
        <div id="lowQualitySection" class="section">
            <h2>低质量图片样本</h2>
            <div class="image-container">
    '''
    
    # 添加低质量图片
    for i, img_info in enumerate(low_quality_paths):  # 显示所有低质量图片
        print(f"\n[DEBUG] Processing low quality image {i+1}: {img_info.get('filename', 'unknown')}")
        print(f"[DEBUG] Image info: uid={img_info.get('uid', 'unknown')}, timestamp={img_info.get('timestamp', 'unknown')}, face_id={img_info.get('face_id', 'unknown')}")
        # Check if timestamp exists in img_info
        if 'timestamp' not in img_info:
            print("[DEBUG] Warning: No timestamp found in image info")
        
        # 获取用户的置信度信息
        uid_str = str(img_info.get('uid', ''))
        if not uid_str or uid_str == 'unknown' or uid_str == 'None':
            print(f"[DEBUG] Invalid UID: {uid_str}")
            confidence_records = []
        else:
            try:
                uid_int = int(uid_str)
                confidence_records = uid_confidence_map.get(uid_int, []) if uid_confidence_map else []
                print(f"[DEBUG] Found {len(confidence_records)} confidence records for UID {uid_int}")
            except ValueError:
                print(f"[DEBUG] Error converting UID to int: {uid_str}")
                confidence_records = []
        
        # 低质量图片使用最大置信度作为失败置信度
        confidence_info = None
        max_confidence = -1
        
        # 遍历所有记录，找出最大置信度的记录
        for record in confidence_records:
            if record.get('result') == 0 and record.get('confidence') is not None:  # 只考虑失败的记录
                if record.get('confidence') > max_confidence:
                    max_confidence = record.get('confidence')
                    confidence_info = record
        
        # 如果没有找到失败记录，则使用第一条记录（如果有的话）
        if confidence_info is None and confidence_records:
            confidence_info = confidence_records[0]
            
        print(f"[DEBUG] Selected confidence record with max confidence: {max_confidence if max_confidence >= 0 else 'N/A'}")
        
        # 准备置信度显示
        confidence_html = ''
        if confidence_info and confidence_info['confidence'] is not None:
            confidence_value = confidence_info['confidence']
            confidence_class = ''
            if confidence_value >= 0.7:
                confidence_class = 'confidence-high'
            elif confidence_value >= 0.5:
                confidence_class = 'confidence-medium'
            else:
                confidence_class = 'confidence-low'
            confidence_html = f'<p>最终置信度: <span class="confidence {confidence_class}">{confidence_value:.4f}</span></p>'
        
        # 检查该用户是否有成功的人脸图片
        uid = img_info['uid']
        has_successful_image = False
        successful_image_path = None
        
        if uid and uid in face_image_paths and face_image_paths[uid]:
            has_successful_image = True
            successful_image_path = face_image_paths[uid][0]  # 使用第一张成功的图片
            successful_image_rel_path = os.path.relpath(successful_image_path, local_dir)
            successful_image_filename = os.path.basename(successful_image_path)
        
        # 检查用户是否最终通过验证
        passed_verification = False
        if confidence_records:
            for record in confidence_records:
                if record.get('result') == 1:  # 假设结果为1表示成功
                    passed_verification = True
                    break
        
        # 根据是否有成功图片决定显示方式
        if has_successful_image:
            html_content += f'''
                <div class="image-card low-quality" style="width: 450px;">
                    <div style="display: flex; justify-content: space-between;">
                        <div style="flex: 1; margin-right: 5px;">
                            <img src="{img_info['path']}" alt="Low quality image {i+1}" style="max-width: 100%; max-height: 200px;">
                            <div class="image-info">
                                <span class="badge badge-low">低质量</span>
                                <p>UID: {img_info['uid']}</p>
                                <p>文件: {img_info['filename']}</p>
                                <p>文件状态: <span style="color: {'green' if passed_verification else 'red'};">{'有效' if passed_verification else '无效'}</span></p>
                            </div>
                        </div>
                        <div style="flex: 1; margin-left: 5px;">
                            <img src="{successful_image_rel_path}" alt="Successful face" style="max-width: 100%; max-height: 200px;">
                            <div class="image-info">
                                <p>成功图片: {successful_image_filename}</p>
                            </div>
                        </div>
                    </div>
                </div>
            '''
        else:
            html_content += f'''
                <div class="image-card low-quality">
                    <img src="{img_info['path']}" alt="Low quality image {i+1}">
                    <div class="image-info">
                        <span class="badge badge-low">低质量</span>
                        <p>UID: {img_info['uid']}</p>
                        <p>文件: {img_info['filename']}</p>
                        <p>文件状态: <span style="color: {'green' if passed_verification else 'red'};">{'有效' if passed_verification else '无效'}</span></p>
                        <p>无成功图片</p>
                    </div>
                </div>
            '''
    
    html_content += '''
            </div>
        </div>
        
        <button class="toggle-button" onclick="toggleSection('dupSection')">显示/隐藏重复图片样本</button>
        <div id="dupSection" class="section">
            <h2>重复图片样本</h2>
            <div class="image-container">
    '''
    
    # 添加重复图片对比分析
    html_content += '''
            </div>
        </div>
        
        <button class="toggle-button" onclick="toggleSection('dupPairsSection')">显示/隐藏重复图片对比</button>
        <div id="dupPairsSection" class="section">
            <h2>重复图片对比分析</h2>
            <div class="image-container">
    '''
    
    # 创建face_id到用户图片的映射
    face_id_to_user_image = {}
    if uid_face_id_map:  # 检查uid_face_id_map是否为None
        for uid, face_id in uid_face_id_map.items():
            if face_id and uid in face_image_paths and face_image_paths[uid]:
                face_id_to_user_image[face_id] = {
                    'uid': uid,
                    'image_path': face_image_paths[uid][0]  # 使用第一张用户人脸图片
                }
    
    # 筛选出有匹配的重复图片
    matched_dup_images = []
    for img_info in dup_image_paths:
        face_id = img_info.get('face_id')
        if face_id and face_id in face_id_to_user_image:
            matched_dup_images.append({
                'dup_img': img_info,
                'match_img': face_id_to_user_image[face_id]
            })
    
    # 如果没有匹配的重复图片，显示提示信息
    if not matched_dup_images:
        html_content += '''
            <p>未找到有匹配的重复图片</p>
        '''
    else:
        # 以紧凑形式展示有匹配的重复图片
        for i, match_pair in enumerate(matched_dup_images):
            dup_img = match_pair['dup_img']
            match_img = match_pair['match_img']
            face_id = dup_img.get('face_id')
            matching_uid = match_img['uid']
            matching_path = os.path.relpath(match_img['image_path'], local_dir)
            matching_filename = os.path.basename(match_img['image_path'])
            
            # 获取用户的置信度信息
            confidence_records = uid_confidence_map.get(int(matching_uid), []) if uid_confidence_map else []
            # 对于重复图片，查找成功的识别记录
            confidence_info = None
            # 先尝试查找成功的记录
            for record in confidence_records:
                if record.get('result') == 1:  # 假设结果为1表示成功
                    confidence_info = record
                    break
            # 如果没有成功的记录，使用第一个记录
            if confidence_info is None and confidence_records:
                confidence_info = confidence_records[0]
            
            # 准备置信度显示
            confidence_html = ''
            if confidence_info and confidence_info['confidence'] is not None:
                confidence_value = confidence_info['confidence']
                confidence_class = ''
                if confidence_value >= 0.7:
                    confidence_class = 'confidence-high'
                elif confidence_value >= 0.5:
                    confidence_class = 'confidence-medium'
                else:
                    confidence_class = 'confidence-low'
                confidence_html = f'<p>置信度: <span class="confidence {confidence_class}">{confidence_value:.4f}</span></p>'
            
            html_content += f'''
                <div class="image-card dup" style="width: 450px;">
                    <div style="display: flex; justify-content: space-between;">
                        <div style="flex: 1; margin-right: 5px;">
                            <img src="{dup_img['path']}" alt="Duplicate image {i+1}" style="max-width: 100%; max-height: 200px;">
                            <div class="image-info">
                                <span class="badge badge-dup">重复</span>
                                <p>UID: {dup_img['uid']}</p>
                                <p>Face ID: {face_id}</p>
                            </div>
                        </div>
                        <div style="flex: 1; margin-left: 5px;">
                            <img src="{matching_path}" alt="Matching face" style="max-width: 100%; max-height: 200px;">
                            <div class="image-info">
                                <p>UID: {matching_uid}</p>
                                {confidence_html}
                            </div>
                        </div>
                    </div>
                </div>
            '''
    
    html_content += '''
            </div>
        </div>
    '''

    
    html_content += '''
        </div>
    </body>
    </html>
    '''
    
    # 写入HTML文件
    with open(html_file_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return html_file_path

def main():
    print("Starting the script...")
    
    # Step 1: List all UID folders
    print("Listing UID folders...")
    uid_folders = list_uid_folders()
    print(f"Found {len(uid_folders)} UID folders")
    
    # Step 2: Get all problem images (low quality and duplicates)
    print("Finding problem images (low quality and duplicates)...")
    problem_images = get_low_quality_images(uid_folders)
    low_count = len([img for img in problem_images if img['type'] == 'low'])
    dup_count = len([img for img in problem_images if img['type'] == 'dup'])
    print(f"Found {len(problem_images)} problem images ({low_count} low quality, {dup_count} duplicates)")
    
    # Step 3: Download random samples of problem images
    sample_size = min(200, len(problem_images))  # Adjust sample size as needed
    print(f"Downloading {sample_size} random samples of problem images...")
    downloaded_samples = download_random_samples(problem_images, sample_size)
    print(f"Downloaded {downloaded_samples} problem image samples")
    
    # Step 4: Get face_ids from dup images
    print("Extracting face_ids from dup images...")
    face_ids = get_dup_images_face_ids(uid_folders)
    print(f"Found {len(face_ids)} face_ids from dup images")
    
    # Step 5: Query database for UIDs with matching face_liveness_id
    print("Querying database for matching UIDs...")
    uid_face_id_map = query_uids_by_face_ids(face_ids)
    print(f"Found {len(uid_face_id_map)} UIDs with matching face_liveness_id")
    
    # Step 6: Get face recognition confidence from logs
    print("Querying face recognition confidence from logs...")
    # Collect UIDs from both uid_face_id_map and problem_images
    all_uids = set(uid_face_id_map.keys())
    # Add UIDs from problem images
    for img in problem_images:
        if img['uid']:
            all_uids.add(img['uid'])
    print(f"Querying confidence data for {len(all_uids)} UIDs (from both matching face_ids and problem images)")
    uid_confidence_map = get_face_recognition_confidence(all_uids)
    print(f"Found confidence data for {len(uid_confidence_map)} UIDs")
    
    # Step 7: Download face images for matching UIDs and problem image UIDs
    print("Downloading face images for matching UIDs and problem image UIDs...")
    downloaded_face_images, face_image_paths = download_face_images(uid_face_id_map, problem_images)
    print(f"Downloaded {downloaded_face_images} face images")
    
    # Step 8: Generate HTML report
    print("Generating HTML report...")
    html_file_path = generate_html_report(problem_images, face_image_paths, face_ids, uid_confidence_map, uid_face_id_map)
    print(f"HTML report generated: {html_file_path}")
    
    print("\nSummary:")
    print(f"Total UID folders: {len(uid_folders)}")
    print(f"Total problem images: {len(problem_images)}")
    print(f"  - Low quality images: {low_count}")
    print(f"  - Duplicate images: {dup_count}")
    print(f"Downloaded problem image samples: {downloaded_samples}")
    print(f"Total face_ids from dup images: {len(face_ids)}")
    print(f"Total UIDs with matching face_liveness_id: {len(uid_face_id_map)}")
    print(f"Total downloaded face images: {downloaded_face_images}")
    print(f"All downloaded images are stored in: {os.path.abspath(local_dir)}")
    print(f"HTML report available at: {os.path.abspath(html_file_path)}")
    
    # 尝试在浏览器中打开HTML报告
    try:
        import webbrowser
        webbrowser.open('file://' + os.path.abspath(html_file_path))
        print("HTML report opened in browser")
    except Exception as e:
        print(f"Could not open HTML report in browser: {e}")
        print("Please open the HTML file manually.")


if __name__ == "__main__":
    main()