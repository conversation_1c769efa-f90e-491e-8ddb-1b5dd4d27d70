import pymysql
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ugc_energy_init.log"),
        logging.StreamHandler()
    ]
)

def initialize_ugc_energy():
    """
    Initialize user base energy for original content (text + image) posts.
    - Fetches approved posts with images from ugc_hub.posts
    - Awards 150 base energy points for each user's first approved post
    - Inserts records into media_task.user_task_record and media_task.ep_records
    - Ensures no duplicate entries
    """
    # Database configuration
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'password',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    # Connect to ugc_hub database
    ugc_db_config = db_config.copy()
    ugc_db_config['database'] = 'ugc_hub'
    
    # Connect to media_task database
    task_db_config = db_config.copy()
    task_db_config['database'] = 'media_task'
    
    # Task configuration
    task_code = 'first_ugc_post'
    base_energy_amount = 150  # Base energy points for first UGC post
    
    try:
        # Step 1: Fetch users with approved posts containing images from ugc_hub database
        ugc_conn = pymysql.connect(**ugc_db_config)
        eligible_users = []
        
        try:
            with ugc_conn.cursor() as cursor:
                # Get users with approved posts containing images
                # Status 2 = approved, content_type 2 or 4 = image or mixed content, post_type 1 = original
                query = """
                SELECT DISTINCT user_id
                FROM posts 
                WHERE status = 2 
                AND (content_type = 2 OR content_type = 4)
                AND post_type = 1
                AND is_del = 0
                """
                cursor.execute(query)
                eligible_users = cursor.fetchall()
                logging.info(f"Found {len(eligible_users)} users with approved image posts")
        finally:
            ugc_conn.close()
        
        if not eligible_users:
            logging.info("No eligible users found. Exiting.")
            return
        
        # Step 2: Process eligible users and update media_task database
        task_conn = pymysql.connect(**task_db_config)
        
        try:
            with task_conn.cursor() as cursor:
                for user in eligible_users:
                    user_id = user['user_id']
                    
                    # Check if task record already exists for this user
                    check_query = """
                    SELECT id FROM user_task_record 
                    WHERE user_id = %s AND task_code = %s AND delete_status = 0
                    """
                    cursor.execute(check_query, (user_id, task_code))
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                        logging.info(f"User {user_id} already has first UGC post bonus. Skipping.")
                        continue
                    
                    # Begin transaction
                    try:
                        # Insert task record
                        now = datetime.now()
                        task_insert_query = """
                        INSERT INTO user_task_record 
                        (user_id, task_code, completion_time) 
                        VALUES (%s, %s, %s)
                        """
                        cursor.execute(task_insert_query, (user_id, task_code, now))
                        task_record_id = cursor.lastrowid
                        
                        # Insert energy record
                        energy_insert_query = """
                        INSERT INTO ep_records 
                        (user_id, ep_type, ep_amount, ep_source_type, task_record_id, hourly) 
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(
                            energy_insert_query, 
                            (user_id, 'base', base_energy_amount, 'task_ugc', task_record_id, now)
                        )
                        
                        # Update user_base_energy table if it exists
                        try:
                            base_energy_query = """
                            INSERT INTO user_base_energy (user_id, total_energy) 
                            VALUES (%s, %s) 
                            ON DUPLICATE KEY UPDATE total_energy = total_energy + %s
                            """
                            cursor.execute(base_energy_query, (user_id, base_energy_amount, base_energy_amount))
                        except pymysql.err.ProgrammingError:
                            logging.warning("user_base_energy table doesn't exist. Skipping base energy update.")
                        
                        task_conn.commit()
                        logging.info(f"Successfully processed user {user_id}: Added {base_energy_amount} base energy points for first UGC post")
                        
                    except Exception as e:
                        task_conn.rollback()
                        logging.error(f"Error processing user {user_id}: {str(e)}")
        finally:
            task_conn.close()
            
        logging.info("UGC energy initialization completed")
        
    except Exception as e:
        logging.error(f"Error in initialize_ugc_energy: {str(e)}")

if __name__ == "__main__":
    logging.info("Starting UGC energy initialization script")
    initialize_ugc_energy()
    logging.info("Script execution completed")
