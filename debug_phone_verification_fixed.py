#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版手机验证用户对账调试脚本
使用正确的字段名和更详细的日志
"""

import pymysql
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"phone_verification_debug_fixed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)

# Database configurations - 生产环境
USER_DB = {
    'host': 'xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user'
}

TASK_DB = {
    'host': 'xme-prod-media-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task'
}

def get_db_config(**kwargs):
    """准备数据库配置"""
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

def debug_phone_verification_differences():
    """详细分析手机验证用户差异"""
    print("🔍 开始详细分析手机验证用户差异 (修正版)...")
    print("=" * 80)
    
    # 连接数据库
    user_conn = pymysql.connect(**get_db_config(**USER_DB))
    task_conn = pymysql.connect(**get_db_config(**TASK_DB))
    
    try:
        # 1. 获取所有手机验证用户
        print("📱 正在获取手机验证用户...")
        with user_conn.cursor() as cursor:
            cursor.execute("SELECT uid FROM client_user WHERE phone_verify = 1")
            phone_verified_users = set(row['uid'] for row in cursor.fetchall())
        
        print(f"✅ 手机验证用户总数: {len(phone_verified_users)}")
        logging.info(f"手机验证用户总数: {len(phone_verified_users)}")
        
        # 显示部分手机验证用户示例
        sample_verified = list(phone_verified_users)[:10]
        print(f"📋 手机验证用户示例 (前10个): {sample_verified}")
        
        # 2. 获取任务记录中的手机验证用户
        print("📋 正在获取任务记录中的手机验证用户...")
        task_users = set()
        task_count_by_shard = {}
        
        with task_conn.cursor() as cursor:
            total_task_count = 0
            for shard in range(1024):
                try:
                    table_name = f"user_task_record_{shard}"
                    query = f"""
                    SELECT user_id, id, create_time, update_time, complete_status
                    FROM {table_name} 
                    WHERE task_code = '10005' AND delete_status = 0
                    """
                    cursor.execute(query)
                    records = cursor.fetchall()
                    
                    shard_count = len(records)
                    if shard_count > 0:
                        task_count_by_shard[shard] = shard_count
                        total_task_count += shard_count
                        
                        for record in records:
                            task_users.add(record['user_id'])
                        
                        # 显示每个有数据的分片的前几个用户
                        if shard < 5:  # 只显示前5个分片的详情
                            print(f"   分片 {shard}: {shard_count} 条记录")
                            for i, record in enumerate(records[:3]):
                                print(f"     用户 {record['user_id']}, 完成状态: {record['complete_status']}, 创建时间: {record['create_time']}")
                        
                except Exception as e:
                    logging.debug(f"分片 {shard} 查询失败: {e}")
                    continue
        
        print(f"✅ 任务记录中手机验证用户数: {len(task_users)}")
        print(f"📊 总任务记录数: {total_task_count}")
        print(f"📊 有数据的分片数: {len(task_count_by_shard)}")
        
        logging.info(f"任务记录中手机验证用户数: {len(task_users)}")
        logging.info(f"有数据的分片: {list(task_count_by_shard.keys())[:20]}")  # 记录前20个分片
        
        # 3. 获取能量记录中的手机验证用户
        print("⚡ 正在获取能量记录中的手机验证用户...")
        energy_users = set()
        energy_count_by_shard = {}
        
        with task_conn.cursor() as cursor:
            total_energy_count = 0
            for shard in range(1024):
                try:
                    table_name = f"user_ep_records_{shard}"
                    query = f"""
                    SELECT uer.user_id, uer.id as energy_id, uer.task_record_id, 
                           uer.create_time, uer.ep_amount, utr.id as task_id
                    FROM {table_name} uer
                    JOIN user_task_record_{shard} utr ON uer.task_record_id = utr.id
                    WHERE utr.task_code = '10005' AND utr.delete_status = 0
                    """
                    cursor.execute(query)
                    records = cursor.fetchall()
                    
                    shard_count = len(records)
                    if shard_count > 0:
                        energy_count_by_shard[shard] = shard_count
                        total_energy_count += shard_count
                        
                        for record in records:
                            energy_users.add(record['user_id'])
                        
                        # 显示每个有数据的分片的前几个用户
                        if shard < 5:  # 只显示前5个分片的详情
                            print(f"   分片 {shard}: {shard_count} 条能量记录")
                            for i, record in enumerate(records[:3]):
                                print(f"     用户 {record['user_id']}, 能量: {record['ep_amount']}, 创建时间: {record['create_time']}")
                        
                except Exception as e:
                    logging.debug(f"分片 {shard} 能量查询失败: {e}")
                    continue
        
        print(f"✅ 能量记录中手机验证用户数: {len(energy_users)}")
        print(f"📊 总能量记录数: {total_energy_count}")
        print(f"📊 有能量数据的分片数: {len(energy_count_by_shard)}")
        
        logging.info(f"能量记录中手机验证用户数: {len(energy_users)}")
        
        # 4. 分析差异
        print("\n" + "=" * 80)
        print("📊 差异分析结果")
        print("=" * 80)
        
        # 找出各种差异情况
        missing_in_tasks = phone_verified_users - task_users
        missing_in_energy = phone_verified_users - energy_users
        extra_in_tasks = task_users - phone_verified_users
        extra_in_energy = energy_users - phone_verified_users
        
        # 任务记录和能量记录不一致的情况
        task_energy_diff_missing_energy = task_users - energy_users
        task_energy_diff_extra_energy = energy_users - task_users
        
        print(f"❌ 已验证手机但缺少任务记录的用户数: {len(missing_in_tasks)}")
        print(f"❌ 已验证手机但缺少能量记录的用户数: {len(missing_in_energy)}")
        print(f"⚠️  有任务记录但未验证手机的用户数: {len(extra_in_tasks)}")
        print(f"⚠️  有能量记录但未验证手机的用户数: {len(extra_in_energy)}")
        print(f"🔄 有任务记录但缺少能量记录的用户数: {len(task_energy_diff_missing_energy)}")
        print(f"🔄 有能量记录但缺少任务记录的用户数: {len(task_energy_diff_extra_energy)}")
        
        # 记录详细差异信息到日志
        logging.info(f"差异统计 - 缺少任务记录: {len(missing_in_tasks)}, 缺少能量记录: {len(missing_in_energy)}")
        logging.info(f"差异统计 - 多余任务记录: {len(extra_in_tasks)}, 多余能量记录: {len(extra_in_energy)}")
        
        # 5. 输出详细差异数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 输出缺少任务记录的用户 (部分)
        if missing_in_tasks:
            print(f"📋 缺少任务记录的用户ID (前20个):")
            missing_sample = sorted(list(missing_in_tasks))[:20]
            for user_id in missing_sample:
                print(f"   {user_id}")
            if len(missing_in_tasks) > 20:
                print(f"   ... 还有{len(missing_in_tasks) - 20}个用户")
        
        # 输出多余任务记录的用户 (部分)
        if extra_in_tasks:
            print(f"📋 多余任务记录的用户ID (前20个):")
            extra_sample = sorted(list(extra_in_tasks))[:20]
            for user_id in extra_sample:
                print(f"   {user_id}")
            if len(extra_in_tasks) > 20:
                print(f"   ... 还有{len(extra_in_tasks) - 20}个用户")
        
        # 分析一些具体案例
        print("\n" + "=" * 80)
        print("🔍 案例分析")
        print("=" * 80)
        
        # 检查一些任务记录用户的手机验证状态
        if task_users:
            sample_task_users = list(task_users)[:5]
            print("📱 检查部分任务记录用户的手机验证状态:")
            
            with user_conn.cursor() as cursor:
                for user_id in sample_task_users:
                    cursor.execute("""
                        SELECT uid, phone_verify, phone, create_time
                        FROM client_user 
                        WHERE uid = %s
                    """, (user_id,))
                    user_info = cursor.fetchone()
                    
                    if user_info:
                        is_verified = user_info['phone_verify'] == 1
                        status = "✅已验证" if is_verified else "❌未验证"
                        print(f"   用户 {user_id}: {status}, 注册时间: {user_info.get('create_time', 'N/A')}")
                    else:
                        print(f"   用户 {user_id}: 用户不存在")
        
        # 检查一些手机验证用户是否在任务表中
        if phone_verified_users:
            sample_verified_users = list(phone_verified_users)[:5]
            print("\n📋 检查部分手机验证用户的任务记录状态:")
            
            with task_conn.cursor() as cursor:
                for user_id in sample_verified_users:
                    # 计算用户应该在哪个分片
                    shard = user_id % 1024
                    table_name = f"user_task_record_{shard}"
                    
                    try:
                        cursor.execute(f"""
                            SELECT id, task_code, complete_status, create_time
                            FROM {table_name}
                            WHERE user_id = %s AND task_code = '10005' AND delete_status = 0
                        """, (user_id,))
                        task_record = cursor.fetchone()
                        
                        if task_record:
                            print(f"   用户 {user_id} (分片{shard}): ✅有任务记录, 完成状态: {task_record['complete_status']}")
                        else:
                            print(f"   用户 {user_id} (分片{shard}): ❌无任务记录")
                    except Exception as e:
                        print(f"   用户 {user_id} (分片{shard}): 查询失败 - {e}")
        
        print("\n🎯 总结:")
        print(f"- 手机验证用户: {len(phone_verified_users)}")
        print(f"- 任务记录用户: {len(task_users)}")
        print(f"- 能量记录用户: {len(energy_users)}")
        print(f"- 数据一致性: {'✅完全一致' if len(missing_in_tasks) == 0 and len(extra_in_tasks) == 0 else '❌存在差异'}")
        
    except Exception as e:
        logging.error(f"执行过程中发生错误: {e}")
        print(f"❌ 执行过程中发生错误: {e}")
        
    finally:
        user_conn.close()
        task_conn.close()

if __name__ == "__main__":
    debug_phone_verification_differences() 