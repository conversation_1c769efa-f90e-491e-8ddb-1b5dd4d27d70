#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查任务表结构和数据的脚本
用于找出手机验证任务的正确查询条件
"""

import pymysql
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

TASK_DB = {
    'host': 'xme-prod-media-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task'
}

def get_db_config(**kwargs):
    """准备数据库配置"""
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

def check_task_structure():
    """检查任务表结构和数据"""
    print("🔍 检查任务表结构和数据...")
    print("=" * 80)
    
    task_conn = pymysql.connect(**get_db_config(**TASK_DB))
    
    try:
        with task_conn.cursor() as cursor:
            # 1. 检查表结构
            print("📋 检查 user_task_record_0 表结构:")
            cursor.execute("DESCRIBE user_task_record_0")
            columns = cursor.fetchall()
            for col in columns:
                print(f"   {col['Field']}: {col['Type']} - {col['Null']} - {col['Key']} - {col['Default']}")
            
            print("\n" + "=" * 60)
            
            # 2. 检查所有不同的task_code
            print("📊 检查所有任务代码分布:")
            cursor.execute("""
                SELECT task_code, COUNT(*) as count 
                FROM user_task_record_0 
                WHERE delete_status = 0
                GROUP BY task_code 
                ORDER BY count DESC
                LIMIT 20
            """)
            task_codes = cursor.fetchall()
            
            for task in task_codes:
                print(f"   任务代码: {task['task_code']} - 数量: {task['count']}")
            
            print("\n" + "=" * 60)
            
            # 3. 检查是否有手机验证相关的任务
            print("🔍 搜索可能的手机验证相关任务:")
            cursor.execute("""
                SELECT task_code, COUNT(*) as count 
                FROM user_task_record_0 
                WHERE delete_status = 0 
                AND (task_code LIKE '%phone%' OR task_code LIKE '%10005%' OR task_code LIKE '%verify%')
                GROUP BY task_code
            """)
            phone_tasks = cursor.fetchall()
            
            if phone_tasks:
                for task in phone_tasks:
                    print(f"   可能的手机验证任务: {task['task_code']} - 数量: {task['count']}")
            else:
                print("   ❌ 没有找到明显的手机验证相关任务")
            
            print("\n" + "=" * 60)
            
            # 4. 检查具体的10005任务代码
            print("🔍 检查任务代码 '10005':")
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM user_task_record_0 
                WHERE task_code = '10005' AND delete_status = 0
            """)
            result = cursor.fetchone()
            print(f"   任务代码 '10005' 的记录数: {result['count']}")
            
            # 5. 检查前几条记录的具体内容
            print("\n📋 查看前5条任务记录:")
            cursor.execute("""
                SELECT id, user_id, task_code, task_type, complete_status, delete_status, create_time
                FROM user_task_record_0 
                ORDER BY id DESC
                LIMIT 5
            """)
            records = cursor.fetchall()
            
            for record in records:
                print(f"   ID: {record['id']}, 用户: {record['user_id']}, 任务代码: {record['task_code']}, "
                      f"类型: {record['task_type']}, 完成状态: {record['complete_status']}, "
                      f"删除状态: {record['delete_status']}, 创建时间: {record['create_time']}")
            
            print("\n" + "=" * 60)
            
            # 6. 检查所有分片表的任务代码分布
            print("🔍 检查所有分片表中的10005任务代码:")
            total_10005_count = 0
            
            for shard in range(min(10, 1024)):  # 先检查前10个分片
                try:
                    table_name = f"user_task_record_{shard}"
                    cursor.execute(f"""
                        SELECT COUNT(*) as count 
                        FROM {table_name} 
                        WHERE task_code = '10005' AND delete_status = 0
                    """)
                    result = cursor.fetchone()
                    count = result['count']
                    total_10005_count += count
                    
                    if count > 0:
                        print(f"   表 {table_name}: {count} 条记录")
                        
                        # 查看一些具体记录
                        cursor.execute(f"""
                            SELECT user_id, task_code, create_time, update_time
                            FROM {table_name} 
                            WHERE task_code = '10005' AND delete_status = 0
                            LIMIT 3
                        """)
                        samples = cursor.fetchall()
                        for sample in samples:
                            print(f"     示例: 用户 {sample['user_id']}, 创建时间: {sample['create_time']}")
                            
                except Exception as e:
                    continue
            
            print(f"\n📊 前10个分片表中任务代码 '10005' 的总记录数: {total_10005_count}")
            
            # 7. 检查能量记录表结构
            print("\n" + "=" * 60)
            print("⚡ 检查 user_ep_records_0 表结构:")
            try:
                cursor.execute("DESCRIBE user_ep_records_0")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"   {col['Field']}: {col['Type']} - {col['Null']} - {col['Key']} - {col['Default']}")
                
                print("\n📊 检查能量记录表中的数据:")
                cursor.execute("SELECT COUNT(*) as count FROM user_ep_records_0")
                result = cursor.fetchone()
                print(f"   user_ep_records_0 总记录数: {result['count']}")
                
            except Exception as e:
                print(f"   ❌ 检查能量记录表失败: {e}")
            
    finally:
        task_conn.close()

if __name__ == "__main__":
    check_task_structure() 