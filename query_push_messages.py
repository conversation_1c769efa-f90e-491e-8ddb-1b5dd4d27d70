#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import pandas as pd
from collections import defaultdict
from datetime import datetime

# Database connection configuration
DB_CONFIG = {
    'game_db': {
        'host': 'xme-prod-rds-user.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',  # Replace with actual host
        'user': 'pro-user-user',       # Replace with actual username
        'password': 'VcEVqaE5HX',       # Replace with actual password
        'db': 'media_game',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    },
    'message_db': {
        'host': 'xme-prod-rds-user.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',  # Replace with actual host
        'user': 'pro-user-user',       # Replace with actual username
        'password': 'VcEVqaE5HX',       # Replace with actual password
        'db': 'media_message',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }
}

def connect_to_db(db_key):
    """Connect to the specified database"""
    try:
        connection = pymysql.connect(**DB_CONFIG[db_key])
        return connection
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def get_uids_from_daily_doge():
    """
    Query UIDs from daily_doge_user_award table with specific conditions:
    - join_date = '2025-05-14' and join_hour = 20, or
    - join_date = '2025-05-15' and join_hour < 8
    - status = 3
    """
    connection = connect_to_db('game_db')
    if not connection:
        return []
    
    try:
        with connection.cursor() as cursor:
            sql = """
            SELECT uid 
            FROM daily_doge_user_award 
            WHERE ((join_date = '2025-05-14' AND join_hour = 20) 
                  OR (join_date = '2025-05-15' AND join_hour < 8)) 
                  AND status = 3
            """
            cursor.execute(sql)
            results = cursor.fetchall()
            uids = [row['uid'] for row in results]
            return uids
    except Exception as e:
        print(f"Error querying UIDs: {e}")
        return []
    finally:
        connection.close()

def get_push_message_table_name(uid):
    """Calculate the push_message table name based on uid % 1024"""
    table_suffix = uid % 1024
    return f"push_message{table_suffix}"

def get_push_messages(table_name, uids=None, date='2025-05-15'):
    """
    Query push messages from the specified table with template 'DAILY_DOGE_HIT'
    and created on the specified date. If uids is provided, only messages for those uids are returned.
    """
    connection = connect_to_db('message_db')
    if not connection:
        return []
    
    try:
        with connection.cursor() as cursor:
            if uids and len(uids) > 0:
                # Convert list of uids to comma-separated string for SQL IN clause
                uid_list = ",".join(str(uid) for uid in uids)
                sql = f"""
                SELECT * 
                FROM {table_name} 
                WHERE push_template = 'DAILY_DOGE_HIT' 
                AND DATE(created_time) = '{date}'
                AND uid IN ({uid_list})
                """
            cursor.execute(sql)
            results = cursor.fetchall()
            return results
    except Exception as e:
        print(f"Error querying push messages from {table_name}: {e}")
        return []
    finally:
        connection.close()

def delete_duplicate_messages(table_name, uid, keep_id, date='2025-05-15'):
    """
    Delete all messages for a specific uid except the one with id=keep_id from the specified table
    with template 'DAILY_DOGE_HIT' and created on the specified date
    """
    connection = connect_to_db('message_db')
    if not connection:
        return 0
    
    try:
        with connection.cursor() as cursor:
            sql = f"""
            DELETE FROM {table_name} 
            WHERE push_template = 'DAILY_DOGE_HIT' 
            AND DATE(created_time) = '{date}'
            AND uid = {uid}
            AND id != {keep_id}
            """
            cursor.execute(sql)
            affected_rows = cursor.rowcount
            connection.commit()
            return affected_rows
    except Exception as e:
        print(f"Error deleting messages from {table_name}: {e}")
        connection.rollback()
        return 0
    finally:
        connection.close()

def main():
    # Step 1: Get UIDs from daily_doge_user_award
    print("Fetching UIDs from daily_doge_user_award...")
    uids = get_uids_from_daily_doge()
    print(f"Found {len(uids)} UIDs")
    
    if not uids:
        print("No UIDs found. Exiting.")
        return
    
    # Step 2: Group UIDs by corresponding push_message table
    table_to_uids = defaultdict(list)
    for uid in uids:
        table_name = get_push_message_table_name(uid)
        table_to_uids[table_name].append(uid)
    
    print(f"UIDs are distributed across {len(table_to_uids)} push_message tables")
    
    # Step 3: Query push messages for each table
    all_messages = []
    table_message_counts = {}
    uid_messages = {}  # Dictionary to store messages by uid
    
    for table_name, table_uids in table_to_uids.items():
        print(f"Querying messages from {table_name} for {len(table_uids)} UIDs...")
        messages = get_push_messages(table_name, table_uids)
        table_message_counts[table_name] = len(messages)
        all_messages.extend(messages)
        
        # Group messages by uid
        for msg in messages:
            uid = msg['uid']
            if uid not in uid_messages:
                uid_messages[uid] = []
            uid_messages[uid].append({
                'table_name': table_name,
                'message': msg
            })
    
    print(f"Found a total of {len(all_messages)} push messages for {len(uid_messages)} unique UIDs")
    

    # Step 5: Delete duplicate messages (COMMENTED OUT FOR SAFETY)
    # Uncomment this section after reviewing the data
    
    total_deleted = 0
    for uid, msg_list in uid_messages.items():
        if len(msg_list) > 1:
            # Keep the first message for this uid, delete the rest
            keep_msg = msg_list[0]
            keep_table = keep_msg['table_name']
            keep_id = keep_msg['message']['id']
            
            print(f"For UID {uid}: keeping message ID {keep_id} in table {keep_table}")
            
            # Delete other messages for this uid across all tables
            for msg_info in msg_list:
                if msg_info['message']['id'] != keep_id or msg_info['table_name'] != keep_table:
                    table = msg_info['table_name']
                    msg_id = msg_info['message']['id']
                #    deleted = delete_duplicate_messages(table, uid, keep_id if table == keep_table else -1)
                    total_deleted += deleted
                    print(f"  - Deleted {deleted} message(s) from {table} for UID {uid}")
    
    print(f"Total deleted: {total_deleted} duplicate messages")


if __name__ == "__main__":
    main()
