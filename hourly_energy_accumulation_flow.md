# 小时能量累积流程设计

## 概述

小时能量累积是XME挖矿系统的核心机制，用户通过各种活动（如时长任务、远征挖矿等）在每小时内累积能量点，系统定期结算并应用各种加成，最终转化为XME代币。本文档描述小时能量点的累积、结算和转化流程。

## 流程图

```mermaid
graph TD
    A["用户产生能量点"] --> B["记录到小时能量表"]
    B --> C["小时结束"]
    C --> D["触发小时能量结算"]
    
    %% 结算流程
    D --> E["获取用户小时能量数据"]
    E --> F["应用各类加成"]
    F --> G["更新用户总能量"]
    G --> H["创建能量结算记录"]
    
    %% 加成计算
    F --> F1["计算好友加成"]
    F --> F2["计算VIP加成"]
    F --> F3["计算活动加成"]
    F --> F4["计算团队加成"]
    
    %% 结果通知
    H --> I["发送结算通知"]
    I --> J["用户查看能量增长"]
    
    %% XME转化
    K["达到XME兑换阈值"] --> L["触发XME兑换"]
    L --> M["扣减能量点"]
    M --> N["增加XME余额"]
    N --> O["创建兑换记录"]
```

## 详细流程说明

### 1. 能量点产生

- 用户通过以下方式产生小时能量点：
  - 时长任务完成
  - 远征挖矿
  - 社交互动
  - 内容创作
  - 任务完成

- 每产生能量点时，系统记录到`user_hourly_energy`表：
  ```sql
  INSERT INTO user_hourly_energy 
  (user_id, hour_timestamp, hour_points, last_updated) 
  VALUES (?, ?, ?, NOW())
  ON DUPLICATE KEY UPDATE 
  hour_points = hour_points + ?, 
  last_updated = NOW()
  ```

### 2. 小时能量结算

- **触发条件**：每小时整点触发（通过XXL-Job调度）
- **处理范围**：上一个小时的能量数据
- **结算步骤**：
  1. 获取上一小时所有产生能量的用户列表
  2. 对每个用户计算各类加成
  3. 更新用户总能量
  4. 创建结算记录
  5. 发送结算通知

### 3. 加成计算

#### 3.1 好友加成

- 基于用户好友数量和好友活跃度计算加成
- 加成公式：`base_bonus + (friend_count * 0.01) + (active_friend_count * 0.02)`
- 最高加成上限：30%

#### 3.2 VIP加成

- 根据用户VIP等级提供固定加成比例
- VIP加成比例：
  | VIP等级 | 加成比例 |
  |---------|---------|
  | VIP1    | 5%      |
  | VIP2    | 10%     |
  | VIP3    | 15%     |
  | VIP4    | 20%     |
  | VIP5    | 30%     |

#### 3.3 活动加成

- 特定活动期间提供额外加成
- 活动加成可叠加，但总加成有上限
- 活动加成从`activity_bonus_config`表获取

#### 3.4 团队加成

- 用户所在团队提供额外加成
- 加成比例基于团队等级和在线成员数
- 计算公式：`base_team_bonus * (online_members / total_members) * team_level_factor`

### 4. 能量结算记录

- 每次小时结算后，创建能量结算记录
- 记录包含原始能量、各类加成和最终能量
- 存储在`energy_settlement_records`表中

### 5. XME兑换流程

- **触发条件**：
  - 用户手动触发兑换
  - 达到自动兑换阈值（可配置）
  
- **兑换步骤**：
  1. 验证用户能量点余额
  2. 计算可兑换的XME数量
  3. 扣减用户能量点
  4. 增加用户XME余额
  5. 创建兑换记录
  6. 发送兑换通知

- **兑换比例**：
  - 基础比例：100能量点 = 1 XME
  - 可根据市场情况和用户等级调整

## 数据模型

### 用户小时能量表

```sql
CREATE TABLE user_hourly_energy (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    hour_timestamp BIGINT NOT NULL COMMENT '小时时间戳，格式：UNIX_TIMESTAMP - UNIX_TIMESTAMP % 3600',
    hour_points INT NOT NULL DEFAULT 0 COMMENT '该小时累积的原始能量点',
    bonus_points INT NOT NULL DEFAULT 0 COMMENT '该小时的加成能量点',
    is_settled BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已结算',
    settled_at TIMESTAMP NULL COMMENT '结算时间',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_hour (user_id, hour_timestamp),
    INDEX idx_hour_timestamp (hour_timestamp),
    INDEX idx_is_settled (is_settled)
);
```

### 能量结算记录表

```sql
CREATE TABLE energy_settlement_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    hour_timestamp BIGINT NOT NULL,
    base_points INT NOT NULL COMMENT '基础能量点',
    friend_bonus_points INT NOT NULL DEFAULT 0 COMMENT '好友加成点数',
    vip_bonus_points INT NOT NULL DEFAULT 0 COMMENT 'VIP加成点数',
    activity_bonus_points INT NOT NULL DEFAULT 0 COMMENT '活动加成点数',
    team_bonus_points INT NOT NULL DEFAULT 0 COMMENT '团队加成点数',
    total_points INT NOT NULL COMMENT '总结算点数',
    settled_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_hour_timestamp (hour_timestamp),
    INDEX idx_settled_at (settled_at)
);
```

### 用户总能量表

```sql
CREATE TABLE user_total_energy (
    user_id BIGINT PRIMARY KEY,
    total_points BIGINT NOT NULL DEFAULT 0 COMMENT '总能量点',
    available_points BIGINT NOT NULL DEFAULT 0 COMMENT '可用能量点',
    exchanged_points BIGINT NOT NULL DEFAULT 0 COMMENT '已兑换能量点',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_total_points (total_points)
);
```

### XME兑换记录表

```sql
CREATE TABLE xme_exchange_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    energy_points BIGINT NOT NULL COMMENT '兑换的能量点数',
    xme_amount DECIMAL(20,8) NOT NULL COMMENT '兑换的XME数量',
    exchange_rate DECIMAL(10,4) NOT NULL COMMENT '兑换比率',
    exchange_type VARCHAR(20) NOT NULL COMMENT '兑换类型：manual, auto',
    exchange_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_exchange_time (exchange_time)
);
```

## 结算任务设计

### XXL-Job任务配置

| 任务名称 | 调度表达式 | 路由策略 | 执行器 | 任务处理器 |
|---------|-----------|---------|--------|-----------|
| 小时能量结算 | 0 1 * * * ? | 分片广播 | energy-executor | hourlyEnergySettlement |
| 日能量统计 | 0 5 0 * * ? | 分片广播 | energy-executor | dailyEnergyStatistics |
| XME自动兑换 | 0 10 0 * * ? | 分片广播 | exchange-executor | autoXmeExchange |

### 小时能量结算任务流程

1. **获取待处理数据**：
   - 查询上一小时有能量产出且未结算的记录
   - 按用户ID分片处理，确保负载均衡

2. **加成计算**：
   - 获取用户各类加成配置
   - 计算各类加成点数
   - 汇总总加成点数

3. **结算处理**：
   - 更新小时能量记录为已结算
   - 增加用户总能量点
   - 创建结算记录
   - 发送结算通知

4. **异常处理**：
   - 记录处理失败的用户ID
   - 自动重试机制
   - 达到重试上限后人工介入

## 性能优化建议

1. **数据分片处理**：
   - 使用XXL-Job的分片广播功能
   - 按用户ID范围分片，平衡负载

2. **批量操作**：
   - 批量查询和更新数据
   - 使用事务确保数据一致性

3. **缓存策略**：
   - 缓存用户加成配置
   - 缓存活跃好友列表
   - 定期刷新缓存数据

4. **数据归档**：
   - 定期归档历史结算数据
   - 保持活跃表数据量在合理范围

## 监控与告警

1. **关键指标**：
   - 结算任务执行时间
   - 结算失败率
   - 异常加成比例
   - 数据处理延迟

2. **告警规则**：
   - 结算任务执行超时
   - 连续结算失败
   - 异常高额加成
   - 用户能量点异常波动

## 扩展考虑

1. **多级缓存**：
   - 本地缓存 + Redis缓存
   - 减少数据库访问压力

2. **异步处理**：
   - 使用消息队列处理通知发送
   - 非关键操作异步执行

3. **数据一致性**：
   - 使用分布式锁确保关键操作不重复
   - 实现幂等性处理逻辑
