import pymysql
import sys
from typing import List, Dict, Tuple, Any

def connect_to_db(db_name: str):
    """Connect to the specified database"""
    try:
        connection = pymysql.connect(
            host='xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',  # Replace with your actual host
            user='pro-user-user',       # Replace with your actual username
            password='VcEVqaE5HX',       # Replace with your actual password
            database='media_user',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        return connection
    except Exception as e:
        print(f"Error connecting to database {db_name}: {e}")
        return None

def get_uid_by_invite_code(invite_codes: List[str]) -> Dict[str, int]:
    """Get UIDs for the given invite codes"""
    if not invite_codes:
        return {}
    
    connection = connect_to_db('media_user')
    if not connection:
        return {}
    
    result = {}
    try:
        with connection.cursor() as cursor:
            # Format the invite codes for SQL IN clause
            formatted_codes = ', '.join([f"'{code.strip()}'" for code in invite_codes if code.strip()])
            
            sql = f"""
            SELECT uid, invite_code
            FROM client_user
            WHERE invite_code IN ({formatted_codes})
            """
            cursor.execute(sql)
            rows = cursor.fetchall()
            
            for row in rows:
                result[row['invite_code']] = row['uid']
                
    except Exception as e:
        print(f"Error querying UIDs by invite codes: {e}")
    finally:
        connection.close()
        
    return result

def get_invitation_count_and_levels(uids: List[int]) -> Tuple[Dict[int, int], Dict[int, int]]:
    """Get invitation count (including recursive) and max levels for each UID"""
    if not uids:
        return {}, {}
    
    connection = connect_to_db('media_user')
    if not connection:
        return {}
    
    count_result = {}
    level_result = {}
    try:
        with connection.cursor() as cursor:
            # Create temporary table for target UIDs
            cursor.execute("DROP TEMPORARY TABLE IF EXISTS temp_target_uids")
            cursor.execute("CREATE TEMPORARY TABLE temp_target_uids (uid BIGINT)")
            
            # Insert UIDs into temporary table
            for uid in uids:
                cursor.execute("INSERT INTO temp_target_uids VALUES (%s)", (uid,))
            
            # Execute recursive query to get invitation counts
            query = """
            WITH RECURSIVE invited_users AS (
                -- Base query: direct invites
                SELECT 
                    tu.uid AS source_uid,
                    cu.uid AS invited_uid,
                    1 AS level
                FROM temp_target_uids tu
                JOIN client_user cu ON cu.invite_uid = tu.uid
                
                UNION ALL
                
                -- Recursive query: indirect invites
                SELECT 
                    iu.source_uid,
                    cu.uid AS invited_uid,
                    iu.level + 1
                FROM client_user cu
                JOIN invited_users iu ON cu.invite_uid = iu.invited_uid
            )
            
            -- Calculate total invitation count and max level for each source UID
            SELECT 
                source_uid AS uid,
                COUNT(*) AS total_invited_users,
                MAX(level) AS max_level
            FROM invited_users
            GROUP BY source_uid
            ORDER BY source_uid;
            """
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            for row in rows:
                count_result[row['uid']] = row['total_invited_users']
                level_result[row['uid']] = row['max_level']
            
            # For UIDs with no invitations, set count and level to 0
            for uid in uids:
                if uid not in count_result:
                    count_result[uid] = 0
                    level_result[uid] = 0
                    
            # Clean up temporary table
            cursor.execute("DROP TEMPORARY TABLE IF EXISTS temp_target_uids")
                
    except Exception as e:
        print(f"Error calculating invitation counts: {e}")
    finally:
        connection.close()
        
    return count_result,level_result

def get_user_details(uids: List[int]) -> Dict[int, Dict]:
    """Get user details for the given UIDs"""
    if not uids:
        return {}
    
    connection = connect_to_db('media_user')
    if not connection:
        return {}
    
    result = {}
    try:
        with connection.cursor() as cursor:
            # Format the UIDs for SQL IN clause
            formatted_uids = ', '.join([str(uid) for uid in uids])
            
            sql = f"""
            SELECT uid, nick_name, email, phone, invite_code, registration_time
            FROM client_user
            WHERE uid IN ({formatted_uids})
            """
            cursor.execute(sql)
            rows = cursor.fetchall()
            
            for row in rows:
                result[row['uid']] = row
                
    except Exception as e:
        print(f"Error querying user details: {e}")
    finally:
        connection.close()
        
    return result

def main():
    # Parse input from command line or use hardcoded values
    invite_codes = [
        "MSHIDN", "Y35JFL", "9RDHFS", "5GOWI1", "XLP0Z8", 
        "MUARGW", "2LFGYK", "7IZUSQ", "BSYIE3", "JJK9BR", 
        "JRVW9Q", "98W3WU", "P50PNT", "BHXE6G", "1NJ1OO", 
        "PGSALF", "UPM1XY"
    ]
    
    uids = [10121944588884, 19121970136209]
    
    # Clean up invite codes (remove empty strings and whitespace)
    invite_codes = [code.strip() for code in invite_codes if code.strip()]
    
    # Get UIDs for invite codes
    uid_by_invite_code = get_uid_by_invite_code(invite_codes)
    print(f"Found {len(uid_by_invite_code)} users by invite code")
    
    # Combine UIDs from both sources
    all_uids = uids + list(uid_by_invite_code.values())
    all_uids = list(set(all_uids))  # Remove duplicates
    
    # Get user details
    user_details = get_user_details(all_uids)
    
    # Get invitation counts and levels
    invitation_counts, invitation_levels = get_invitation_count_and_levels(all_uids)
    
    # Print results
    print("\n=== RESULTS ===")
    print(f"Total users analyzed: {len(all_uids)}")
    print("\n")
    
    # Format and print the results
    print(f"{'UID':<20} {'Invite Code':<15} {'Nickname':<20} {'Invitation Count':<15} {'Max Level':<10}")
    print("-" * 80)
    
    for uid in all_uids:
        details = user_details.get(uid, {})
        invite_code = details.get('invite_code', 'N/A')
        nickname = details.get('nick_name', 'N/A')
        count = invitation_counts.get(uid, 0)
        
        level = invitation_levels.get(uid, 0)
        print(f"{uid:<20} {invite_code:<15} {nickname:<20} {count:<15} {level:<10}")
    
    # Optionally, export to CSV
    try:
        import csv
        with open('invitation_results.csv', 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['UID', 'Invite Code', 'Nickname', 'Email', 'Phone', 'Registration Time', 'Invitation Count', 'Max Level']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for uid in all_uids:
                details = user_details.get(uid, {})
                writer.writerow({
                    'UID': uid,
                    'Invite Code': details.get('invite_code', 'N/A'),
                    'Nickname': details.get('nick_name', 'N/A'),
                    'Email': details.get('email', 'N/A'),
                    'Phone': details.get('phone', 'N/A'),
                    'Registration Time': details.get('registration_time', 'N/A'),
                    'Invitation Count': invitation_counts.get(uid, 0),
                    'Max Level': invitation_levels.get(uid, 0)
                })
        print(f"\nResults exported to invitation_results.csv")
    except Exception as e:
        print(f"Error exporting to CSV: {e}")

if __name__ == "__main__":
    main()
