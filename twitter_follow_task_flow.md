# Twitter关注官方账号任务流程

## 流程概述

Twitter关注官方账号任务是XME挖矿系统中的一种社交互动任务，要求用户关注项目的官方Twitter账号以获取能量点奖励。

## 详细流程设计

### 1. 用户发起关注任务
- 客户端展示关注任务和奖励说明
- 用户点击“关注并验证”按钮

### 2. OAuth授权流程
- 客户端发送：`POST /task/v1/twitter/authorize`
- 服务端生成OAuth授权URL并返回
- 客户端打开WebView加载Twitter授权页面
- 用户登录Twitter并授权应用访问其关注信息
- Twitter重定向回应用，带上授权码

### 3. 关注官方账号
- 客户端当前任务配置`
- 客户端展示官方账号信息和“关注”按钮
- 用户点击关注按钮
- 客户端发送：`POST /task/v1/twitter/follow`
- 请求参数：`{task_id, auth_code, target_username}`

### 4. 服务端验证关注
- 服务端使用授权码获取访问Twitter API的令牌
- 服务端调用Twitter API执行关注操作
- 服务端调用Twitter API验证关注状态
- 服务端返回验证结果

### 5. 任务完成与奖励
- 服务端验证关注成功后自动标记任务完成
- 计算并发放能量点奖励
- 客户端显示任务完成和奖励信息

## API接口设计

### 1. OAuth授权初始化接口

**请求**：
```
POST /task/v1/twitter/authorize
Content-Type: application/json

{
  "task_id": "任务ID"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "auth_url": "https://twitter.com/oauth/authorize?...",
    "request_id": "授权请求ID"
  },
  "success": true
}
```

### 2. 获取官方账号信息（通过任务列表config数据）

```
"twitter_username": "official_account",
"twitter_display_name": "Project Official",
"twitter_profile_url": "https://twitter.com/official_account"
```

### 3. 执行关注操作接口

**请求**：
```
POST /task/v1/twitter/follow
Content-Type: application/json

{
  "task_id": "任务ID",
  "auth_code": "授权码",
  "target_username": "official_account"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "followed": true,
    "verification_time": "2025-05-30T02:20:52+08:00",
    "task_completed": true,
    "energy_points_earned": 50
  },
  "success": true
}
```

## 实现要点

1. **关注验证机制**：
   - 使用Twitter API的关注者检查功能
   - 需要用户授权应用访问其Twitter关注列表

2. **防作弊措施**：
   - 定期检查用户是否取消关注
   - 如发现取消关注，可考虑撤销之前的奖励

3. **用户体验优化**：
   - 提供一键跳转到官方Twitter的功能
   - 自动检测关注状态，减少用户手动验证步骤
