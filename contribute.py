import requests
import json

# Function to get repositories for a specific organization
def get_repositories(org_name, token):
    url = f"https://api.github.com/orgs/{org_name}/repos"
    headers = {'Authorization': f'token {token}'}
    response = requests.get(url, headers=headers)
    return response.json()

# Function to get commits for a specific repository
def get_commits(owner, repo, token):
    url = f"https://api.github.com/repos/{owner}/{repo}/commits"
    headers = {'Authorization': f'token {token}'}
    response = requests.get(url, headers=headers)
    return response.json()

# Function to get commit details for a specific commit
def get_commit_details(owner, repo, sha, token):
    url = f"https://api.github.com/repos/{owner}/{repo}/commits/{sha}"
    headers = {'Authorization': f'token {token}'}
    response = requests.get(url, headers=headers)
    return response.json()

# Function to calculate contributions
def calculate_contributions(org_name, user_email, token):
    repos = get_repositories(org_name, token)
    total_repos = len(repos)
    print(f"Total Repositories: {total_repos}")
    
    # Debugging: Print the list of repositories
  #  print("Repositories:", [repo['name'] for repo in repos])

    total_commits = 0
    total_lines_added = 0
    total_lines_removed = 0

    for repo in repos:
        repo_name = repo['name']
        commits = get_commits(org_name, repo_name, token)
        # Debugging: Print the number of commits fetched
        #print(f"Repository: {repo_name}, Commits Fetched: {len(commits)}")
        for commit in commits:
            # Debugging: Print commit author details
           # print("Commit Author:", commit['commit']['author'])
            if commit['commit']['author']['email'] == user_email:
                total_commits += 1
                commit_details = get_commit_details(org_name, repo_name, commit['sha'], token)
                for file in commit_details['files']:
                    total_lines_added += file['additions']
                    total_lines_removed += file['deletions']

    print(f"Total Commits: {total_commits}")
    print(f"Total Lines Added: {total_lines_added}")
    print(f"Total Lines Removed: {total_lines_removed}")

def main():
    # Example usage
    org_name = input("Enter your organization name: ")
    user_email = input("Enter your GitHub email: ")
    token = input("Enter your GitHub token: ")

    calculate_contributions(org_name, user_email, token)

if __name__ == "__main__":
    main()