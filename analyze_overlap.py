#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析手机验证用户和任务记录用户重叠情况的脚本
找出为什么两个数据集几乎不重叠的原因
"""

import pymysql
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

USER_DB = {
    'host': 'xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user'
}

TASK_DB = {
    'host': 'xme-prod-media-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task'
}

def get_db_config(**kwargs):
    """准备数据库配置"""
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

def analyze_overlap():
    """分析重叠情况"""
    print("🔍 分析手机验证用户和任务记录用户的重叠情况...")
    print("=" * 80)
    
    user_conn = pymysql.connect(**get_db_config(**USER_DB))
    task_conn = pymysql.connect(**get_db_config(**TASK_DB))
    
    try:
        # 1. 获取手机验证用户样本
        print("📱 获取手机验证用户样本...")
        with user_conn.cursor() as cursor:
            cursor.execute("""
                SELECT uid, phone, phone_verify, create_time, update_time
                FROM client_user 
                WHERE phone_verify = 1 
                ORDER BY uid 
                LIMIT 50
            """)
            verified_users = cursor.fetchall()
        
        print(f"✅ 获取到 {len(verified_users)} 个手机验证用户样本")
        
        # 显示前10个用户的详细信息
        print("📋 手机验证用户样本 (前10个):")
        for i, user in enumerate(verified_users[:10]):
            print(f"   {i+1}. 用户ID: {user['uid']}, 手机: {user.get('phone', 'N/A')}, "
                  f"验证状态: {user['phone_verify']}, 注册时间: {user.get('create_time', 'N/A')}")
        
        # 2. 获取任务记录用户样本
        print(f"\n📋 获取任务记录用户样本...")
        with task_conn.cursor() as cursor:
            cursor.execute("""
                SELECT user_id, id, create_time, update_time, complete_status
                FROM user_task_record_0 
                WHERE task_code = '10005' AND delete_status = 0
                ORDER BY user_id 
                LIMIT 50
            """)
            task_users = cursor.fetchall()
        
        print(f"✅ 获取到 {len(task_users)} 个任务记录用户样本")
        
        # 显示前10个用户的详细信息
        print("📋 任务记录用户样本 (前10个):")
        for i, user in enumerate(task_users[:10]):
            print(f"   {i+1}. 用户ID: {user['user_id']}, 任务ID: {user['id']}, "
                  f"完成状态: {user['complete_status']}, 创建时间: {user['create_time']}")
        
        # 3. 检查重叠情况
        print(f"\n🔍 检查重叠情况...")
        verified_ids = {user['uid'] for user in verified_users}
        task_ids = {user['user_id'] for user in task_users}
        
        overlap = verified_ids & task_ids
        verified_only = verified_ids - task_ids
        task_only = task_ids - verified_ids
        
        print(f"📊 重叠分析:")
        print(f"   手机验证用户样本: {len(verified_ids)} 个")
        print(f"   任务记录用户样本: {len(task_ids)} 个")
        print(f"   重叠用户: {len(overlap)} 个")
        print(f"   仅手机验证: {len(verified_only)} 个")
        print(f"   仅任务记录: {len(task_only)} 个")
        
        if overlap:
            print(f"✅ 重叠用户ID: {list(overlap)}")
        else:
            print("❌ 样本中没有重叠用户!")
        
        # 4. 分析具体原因 - 检查任务记录用户的手机验证状态
        print(f"\n🔍 分析任务记录用户的手机验证状态...")
        
        sample_task_users = [user['user_id'] for user in task_users[:10]]
        
        with user_conn.cursor() as cursor:
            for user_id in sample_task_users:
                cursor.execute("""
                    SELECT uid, phone_verify, phone, create_time, update_time
                    FROM client_user 
                    WHERE uid = %s
                """, (user_id,))
                user_info = cursor.fetchone()
                
                if user_info:
                    verify_status = "✅已验证" if user_info['phone_verify'] == 1 else "❌未验证"
                    print(f"   用户 {user_id}: {verify_status}, 手机: {user_info.get('phone', 'N/A')}, "
                          f"注册: {user_info.get('create_time', 'N/A')}")
                else:
                    print(f"   用户 {user_id}: ❌用户不存在")
        
        # 5. 分析手机验证用户是否有任务记录
        print(f"\n🔍 分析手机验证用户是否有任务记录...")
        
        sample_verified_users = [user['uid'] for user in verified_users[:10]]
        
        with task_conn.cursor() as cursor:
            for user_id in sample_verified_users:
                # 计算应该在哪个分片
                shard = user_id % 1024
                table_name = f"user_task_record_{shard}"
                
                try:
                    cursor.execute(f"""
                        SELECT id, task_code, complete_status, create_time
                        FROM {table_name}
                        WHERE user_id = %s AND task_code = '10005' AND delete_status = 0
                    """, (user_id,))
                    task_record = cursor.fetchone()
                    
                    if task_record:
                        print(f"   用户 {user_id} (分片{shard}): ✅有任务记录, "
                              f"完成状态: {task_record['complete_status']}, 创建: {task_record['create_time']}")
                    else:
                        # 检查是否有其他任务记录
                        cursor.execute(f"""
                            SELECT task_code, COUNT(*) as count
                            FROM {table_name}
                            WHERE user_id = %s AND delete_status = 0
                            GROUP BY task_code
                        """, (user_id,))
                        other_tasks = cursor.fetchall()
                        
                        if other_tasks:
                            tasks_info = ", ".join([f"{t['task_code']}({t['count']})" for t in other_tasks])
                            print(f"   用户 {user_id} (分片{shard}): ❌无10005任务, 但有其他任务: {tasks_info}")
                        else:
                            print(f"   用户 {user_id} (分片{shard}): ❌无任何任务记录")
                            
                except Exception as e:
                    print(f"   用户 {user_id} (分片{shard}): 查询失败 - {e}")
        
        # 6. 时间范围分析
        print(f"\n📅 时间范围分析...")
        
        # 手机验证用户的注册时间范围
        verified_times = [user['create_time'] for user in verified_users if user.get('create_time')]
        if verified_times:
            min_verified = min(verified_times)
            max_verified = max(verified_times)
            print(f"   手机验证用户注册时间范围: {min_verified} 到 {max_verified}")
        
        # 任务记录的创建时间范围
        task_times = [user['create_time'] for user in task_users if user.get('create_time')]
        if task_times:
            min_task = min(task_times)
            max_task = max(task_times)
            print(f"   任务记录创建时间范围: {min_task} 到 {max_task}")
        
        # 7. 总结分析
        print(f"\n🎯 问题分析总结:")
        print(f"1. 从样本分析看，手机验证用户和任务记录用户基本不重叠")
        print(f"2. 这说明手机验证功能和任务系统可能是在不同时期上线的")
        print(f"3. 或者手机验证任务的逻辑发生了变化")
        print(f"4. 需要进一步调查:")
        print(f"   - 手机验证任务是什么时候开始记录的？")
        print(f"   - 是否存在历史数据迁移问题？")
        print(f"   - 手机验证的业务逻辑是否发生过变化？")
        
    finally:
        user_conn.close()
        task_conn.close()

if __name__ == "__main__":
    analyze_overlap() 