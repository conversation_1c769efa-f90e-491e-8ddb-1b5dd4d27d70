#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
能量系统对账检查演示脚本
展示对账功能和报告格式
"""

from datetime import datetime

def demo_reconciliation_report():
    """演示对账报告的格式和内容"""
    
    print("🚀 能量系统对账检查工具")
    print("=" * 80)
    print("功能说明：检查用户验证状态与任务记录、能量记录之间的数据一致性")
    print()
    
    # 模拟对账结果
    demo_results = {
        'phone_verify': {
            'verified_users': 15432,
            'task_records': 15398,
            'energy_records': 15398,
            'missing_tasks': 34,
            'missing_energy': 34,
            'extra_tasks': 0,
            'extra_energy': 0
        },
        'email_verify': {
            'verified_users': 8765,
            'task_records': 8765,
            'energy_records': 8765,
            'missing_tasks': 0,
            'missing_energy': 0,
            'extra_tasks': 0,
            'extra_energy': 0
        },
        'ugc_posts': {
            'eligible_users': 4321,
            'task_records': 4298,
            'energy_records': 4298,
            'missing_tasks': 23,
            'missing_energy': 23,
            'extra_tasks': 2,
            'extra_energy': 2
        },
        'interests': {
            'eligible_users': 12987,
            'task_records': 12987,
            'energy_records': 12987,
            'missing_tasks': 0,
            'missing_energy': 0,
            'extra_tasks': 0,
            'extra_energy': 0
        },
        'face_invite': {
            'eligible_users': 567,
            'task_records': 560,
            'energy_records': 560,
            'missing_tasks': 7,
            'missing_energy': 7,
            'extra_tasks': 0,
            'extra_energy': 0
        }
    }
    
    print(f"能量系统对账报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    task_names = {
        'phone_verify': '手机验证 (任务代码: 10005)',
        'email_verify': '邮箱验证 (任务代码: 10006)', 
        'ugc_posts': 'UGC内容发布 (任务代码: 10002)',
        'interests': '兴趣选择 (任务代码: 10001)',
        'face_invite': '人脸邀请 (任务代码: 10003)'
    }
    
    for task_type, results in demo_results.items():
        print(f"\n🔍 {task_names[task_type]}")
        print("-" * 60)
        
        # 显示统计信息
        if task_type in ['phone_verify', 'email_verify']:
            print(f"📊 统计信息:")
            print(f"   验证用户总数: {results['verified_users']}")
            print(f"   任务记录数量: {results['task_records']}")
            print(f"   能量记录数量: {results['energy_records']}")
        else:
            print(f"📊 统计信息:")
            print(f"   符合条件用户: {results['eligible_users']}")
            print(f"   任务记录数量: {results['task_records']}")
            print(f"   能量记录数量: {results['energy_records']}")
        
        # 检查是否有差异
        has_differences = (
            results['missing_tasks'] > 0 or 
            results['missing_energy'] > 0 or 
            results['extra_tasks'] > 0 or 
            results['extra_energy'] > 0
        )
        
        if not has_differences:
            print("✅ 数据完全一致，无差异")
        else:
            if results['missing_tasks'] > 0:
                print(f"❌ 缺少任务记录的用户数: {results['missing_tasks']}")
            
            if results['missing_energy'] > 0:
                print(f"❌ 缺少能量记录的用户数: {results['missing_energy']}")
            
            if results['extra_tasks'] > 0:
                print(f"⚠️  多余任务记录的用户数: {results['extra_tasks']}")
            
            if results['extra_energy'] > 0:
                print(f"⚠️  多余能量记录的用户数: {results['extra_energy']}")
    
    print(f"\n{'='*80}")
    print("📋 对账检查功能说明：")
    print("1. 检查手机验证用户与任务记录、能量记录的一致性")
    print("2. 检查邮箱验证用户与任务记录、能量记录的一致性") 
    print("3. 检查UGC内容发布用户与任务记录、能量记录的一致性")
    print("4. 检查兴趣选择用户与任务记录、能量记录的一致性")
    print("5. 检查人脸邀请用户与任务记录、能量记录的一致性")
    print()
    print("📁 输出文件：")
    print("- reconciliation_check_YYYYMMDD_HHMMSS.log - 详细执行日志")
    print("- reconciliation_differences_YYYYMMDD_HHMMSS.txt - 差异用户详细列表")
    print()
    print("🚨 注意事项：")
    print("- 脚本会检查所有1024个分片表 (user_task_record_0 到 user_task_record_1023)")
    print("- 同时检查对应的能量记录表 (user_ep_records_0 到 user_ep_records_1023)")
    print("- 预期结果：所有验证状态与任务记录、能量记录应该完全一致")
    print("- 如果发现差异，需要进一步调查原因并进行数据修复")

def show_usage():
    """显示使用说明"""
    print("\n" + "="*80)
    print("使用方法：")
    print("="*80)
    print("1. 运行完整对账检查：")
    print("   python reconciliation_check.py")
    print()
    print("2. 查看演示报告：")
    print("   python demo_reconciliation.py")
    print()
    print("3. 脚本会自动：")
    print("   - 连接到所有相关数据库 (media_user, media_task, ugc_hub, content_behavior)")
    print("   - 检查1024个分片表的数据一致性")
    print("   - 生成详细的对账报告")
    print("   - 输出差异用户列表到文件")
    print()
    print("4. 数据库配置：")
    print("   - 脚本使用测试环境数据库配置")
    print("   - 如需修改数据库配置，请编辑脚本中的数据库连接参数")

if __name__ == "__main__":
    demo_reconciliation_report()
    show_usage() 