#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查机器人用户的情况
验证system_user_record表中的机器人用户数据
"""

import pymysql
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 任务数据库配置
TASK_DB = {
    'host': 'xme-prod-media-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task'
}

def get_db_config(**kwargs):
    """准备数据库配置"""
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

def check_bot_users():
    """检查机器人用户情况"""
    print("🤖 检查机器人用户情况...")
    print("=" * 80)
    
    task_conn = pymysql.connect(**get_db_config(**TASK_DB))
    
    try:
        with task_conn.cursor() as cursor:
            # 1. 检查表结构
            print("📋 检查system_user_record表结构:")
            cursor.execute("DESCRIBE system_user_record")
            columns = cursor.fetchall()
            for col in columns:
                print(f"   {col['Field']}: {col['Type']} ({col['Null']}, {col['Key']}, {col['Default']})")
            
            # 2. 统计机器人用户总数
            print(f"\n📊 统计机器人用户:")
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN delete_status = 0 THEN 1 END) as active_count,
                    COUNT(CASE WHEN delete_status = 1 THEN 1 END) as deleted_count
                FROM system_user_record
            """)
            stats = cursor.fetchone()
            
            print(f"   总机器人用户数: {stats['total_count']}")
            print(f"   活跃机器人用户数: {stats['active_count']}")
            print(f"   已删除机器人用户数: {stats['deleted_count']}")
            
            # 3. 查看一些样本数据
            print(f"\n👥 机器人用户样本 (前10个活跃用户):")
            cursor.execute("""
                SELECT user_id, nick_name, create_time, update_time, delete_status
                FROM system_user_record 
                WHERE delete_status = 0
                ORDER BY user_id 
                LIMIT 10
            """)
            samples = cursor.fetchall()
            
            for bot in samples:
                print(f"   用户ID: {bot['user_id']}, 昵称: {bot['nick_name']}, "
                      f"创建时间: {bot['create_time']}, 状态: {'活跃' if bot['delete_status'] == 0 else '已删除'}")
            
            # 4. 检查创建时间分布
            print(f"\n📅 机器人用户创建时间分布:")
            cursor.execute("""
                SELECT 
                    DATE(create_time) as create_date,
                    COUNT(*) as count
                FROM system_user_record 
                WHERE delete_status = 0
                GROUP BY DATE(create_time)
                ORDER BY create_date DESC
                LIMIT 10
            """)
            date_stats = cursor.fetchall()
            
            for stat in date_stats:
                print(f"   {stat['create_date']}: {stat['count']} 个机器人用户")
            
            # 5. 检查这些机器人用户是否有任务记录
            print(f"\n🔍 检查机器人用户的任务记录情况:")
            
            # 获取前5个机器人用户ID
            cursor.execute("""
                SELECT user_id 
                FROM system_user_record 
                WHERE delete_status = 0
                ORDER BY user_id 
                LIMIT 5
            """)
            bot_user_ids = [row['user_id'] for row in cursor.fetchall()]
            
            print(f"   检查机器人用户ID: {bot_user_ids}")
            
            for user_id in bot_user_ids:
                shard = user_id % 1024
                table_name = f"user_task_record_{shard}"
                
                try:
                    cursor.execute(f"""
                        SELECT task_code, complete_status, create_time
                        FROM {table_name}
                        WHERE user_id = %s AND delete_status = 0
                        ORDER BY create_time DESC
                        LIMIT 5
                    """, (user_id,))
                    tasks = cursor.fetchall()
                    
                    if tasks:
                        print(f"   机器人用户 {user_id} 有 {len(tasks)} 个任务记录:")
                        for task in tasks:
                            print(f"     任务 {task['task_code']}: 状态={task['complete_status']}, "
                                  f"时间={task['create_time']}")
                    else:
                        print(f"   机器人用户 {user_id}: 无任务记录")
                except Exception as e:
                    print(f"   机器人用户 {user_id}: 检查任务记录时出错 - {e}")
                
                print()
            
    finally:
        task_conn.close()

if __name__ == "__main__":
    check_bot_users() 