# 社交验证综合任务流程

## 流程概述

社交验证综合任务是XME挖矿系统中的一种复合任务，要求用户同时完成关注官方Twitter账号、加入官方Telegram群组以及分享内容到Twitter三个子任务，以获取完整的能量点奖励。

## 详细流程设计

### 1. 用户发起社交验证任务
- 客户端展示社交验证任务和奖励说明
- 用户点击"开始社交验证"按钮

### 2. 社交账号状态检查
- 客户端发送：`POST /task/v1/user/social-verification`
- 请求参数：`{user_id, task_id}`
- 服务端检查用户是否关注官方Twitter账号
- 服务端检查用户是否加入官方Telegram群组
- 服务端返回检查结果：`{twitter_followed, telegram_joined}`

### 3. 引导用户完成未完成的子任务
- 如果未关注Twitter官方账号，引导用户完成Twitter关注任务
- 如果未加入Telegram群组，引导用户完成Telegram加入任务
- 完成上述子任务后，引导用户进行Twitter分享

### 4. 最终验证与奖励
- 客户端发送：`POST /task/v1/social/verify-all`
- 服务端综合验证所有子任务的完成状态
- 服务端计算并发放能量点奖励
- 客户端显示任务完成和奖励信息

## API接口设计

### 1. 社交验证状态检查接口

**请求**：
```
POST /task/v1/user/social-verification
Content-Type: application/json

{
  "user_id": "用户ID",
  "task_id": "任务ID"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "twitter_followed": true,
    "telegram_joined": false,
    "twitter_shared": false,
    "twitter_username": "official_account",
    "telegram_group_link": "https://t.me/official_group",
    "completed_subtasks": 1,
    "total_subtasks": 3
  },
  "success": true
}
```

### 2. 综合验证接口

**请求**：
```
POST /task/v1/social/verify-all
Content-Type: application/json

{
  "user_id": "用户ID",
  "task_id": "任务ID",
  "twitter_username": "用户的Twitter用户名",
  "telegram_username": "用户的Telegram用户名",
  "tweet_id": "分享的推文ID"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "all_verified": true,
    "twitter_followed": true,
    "telegram_joined": true,
    "twitter_shared": true,
    "energy_points_earned": 100,
    "verification_time": "2025-05-30T02:20:52+08:00"
  },
  "success": true
}
```

## 实现要点

1. **任务依赖关系**：
   - 建议先完成关注和加入任务，再进行分享任务
   - 但不强制要求完成顺序，用户可以自由选择完成路径

2. **奖励机制**：
   - 可以为每个子任务设置独立奖励
   - 全部完成后提供额外的奖励倍数或奖励点数
   - 实现阶梯式奖励机制，鼓励用户完成全部任务

3. **验证策略**：
   - 实时验证：每完成一个子任务立即验证
   - 最终验证：所有子任务完成后进行综合验证
   - 定期验证：定期检查用户是否保持关注和加入状态

4. **防作弊措施**：
   - 记录用户社交账号信息，防止使用多个账号完成任务
   - 设置合理的任务完成时间窗口，防止短时间内反复完成任务
   - 实施分享内容质量检查，防止无意义内容分享

5. **用户体验优化**：
   - 提供清晰的任务进度指示
   - 简化验证流程，减少用户操作步骤
   - 在应用内提供详细的任务指引
