import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import java.io.IOException;
import org.apache.http.client.methods.HttpGet;

public class ApolloOpenApiClient {
    
    private final String baseUrl;
    private final String token;
    private final HttpClient client;
    
    public ApolloOpenApiClient(String baseUrl, String token) {
        this.baseUrl = baseUrl;
        this.token = token;
        // 初始化HttpClient
        this.client = HttpClients.custom()
                .setConnectionTimeoutMillis(5000)  // 连接超时5秒
                .setSocketTimeoutMillis(10000)     // 读取超时10秒
                .setMaxConnTotal(20)               // 最大连接数
                .setMaxConnPerRoute(10)            // 每个路由最大连接数
                .build();
    }
    
    // 可选：提供关闭客户端的方法
    public void close() {
        if (client instanceof CloseableHttpClient) {
            try {
                ((CloseableHttpClient) client).close();
            } catch (IOException e) {
                // 处理关闭异常
            }
        }
    }
    
    public boolean updateAppInfo(String appId, String newVersion) throws Exception {
        String url = baseUrl + "/apps/" + appId;
        
        JSONObject body = new JSONObject();
        body.put("appId", appId);
        body.put("version", newVersion);
        
        HttpPut httpPut = new HttpPut(url);
        httpPut.setHeader("Authorization", token);
        httpPut.setHeader("Content-Type", "application/json;charset=UTF-8");
        
        StringEntity entity = new StringEntity(body.toString(), "UTF-8");
        httpPut.setEntity(entity);
        
        try {
            HttpResponse response = client.execute(httpPut);
            int statusCode = response.getStatusLine().getStatusCode();
            
            // 读取响应内容
            BufferedReader reader = new BufferedReader(
                new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
            StringBuilder responseContent = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                responseContent.append(line);
            }
            
            if (statusCode != 200) {
                System.err.println("更新失败: " + responseContent.toString());
                return false;
            }
            
            // 解析返回的JSON
            JSONObject result = new JSONObject(responseContent.toString());
            return true;
        } finally {
            httpPut.releaseConnection();
        }
    }
    
    public boolean releaseConfig(String appId, String env, String cluster, String namespace, String releaseTitle) throws Exception {
        String url = String.format("%s/envs/%s/apps/%s/clusters/%s/namespaces/%s/releases", 
            baseUrl, env, appId, cluster, namespace);
        
        JSONObject body = new JSONObject();
        body.put("releaseTitle", releaseTitle);
        body.put("releaseComment", "Version update through OpenAPI");
        
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Authorization", token);
        httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
        
        StringEntity entity = new StringEntity(body.toString(), "UTF-8");
        httpPost.setEntity(entity);
        
        try {
            HttpResponse response = client.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            
            // 读取响应内容
            BufferedReader reader = new BufferedReader(
                new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
            StringBuilder responseContent = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                responseContent.append(line);
            }
            
            if (statusCode != 200) {
                System.err.println("发布失败: " + responseContent.toString());
                return false;
            }
            
            // 解析返回的JSON
            JSONObject result = new JSONObject(responseContent.toString());
            return true;
        } finally {
            httpPost.releaseConnection();
        }
    }

    // 添加更新配置接口
    public boolean updateItemValue(String appId, String env, String cluster, String namespace, String key, String value) throws Exception {
        // 注意URL格式
        String url = String.format("%s/openapi/v1/envs/%s/apps/%s/clusters/%s/namespaces/%s/items/%s", 
            baseUrl, env, appId, cluster, namespace, key);
        
        JSONObject body = new JSONObject();
        body.put("key", key);
        body.put("value", value);
        body.put("dataChangeCreatedBy", "apollo");
        
        // 如果key不存在，需要先创建
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Authorization", token);
        HttpResponse getResponse = client.execute(httpGet);
        
        if (getResponse.getStatusLine().getStatusCode() == 404) {
            // key不存在，使用POST创建
            String createUrl = String.format("%s/openapi/v1/envs/%s/apps/%s/clusters/%s/namespaces/%s/items", 
                baseUrl, env, appId, cluster, namespace);
            
            HttpPost httpPost = new HttpPost(createUrl);
            httpPost.setHeader("Authorization", token);
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            
            StringEntity entity = new StringEntity(body.toString(), "UTF-8");
            httpPost.setEntity(entity);
            
            try {
                HttpResponse response = client.execute(httpPost);
                if (response.getStatusLine().getStatusCode() != 200) {
                    System.err.println("创建配置失败");
                    return false;
                }
            } finally {
                httpPost.releaseConnection();
            }
        } else {
            // key存在，使用PUT更新
            HttpPut httpPut = new HttpPut(url);
            httpPut.setHeader("Authorization", token);
            httpPut.setHeader("Content-Type", "application/json;charset=UTF-8");
            
            StringEntity entity = new StringEntity(body.toString(), "UTF-8");
            httpPut.setEntity(entity);
            
            try {
                HttpResponse response = client.execute(httpPut);
                if (response.getStatusLine().getStatusCode() != 200) {
                    System.err.println("更新配置失败");
                    return false;
                }
            } finally {
                httpPut.releaseConnection();
            }
        }
        
        return true;
    }
} 