#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
短信服务商价格对比Excel生成器
"""

import pandas as pd
import numpy as np
from datetime import datetime

def create_comparison_excel():
    """创建价格对比Excel表格"""
    
    # 原始数据
    data = {
        '国家/地区': [
            '尼日利亚', '印度尼西亚', '越南', '巴基斯坦', '孟加拉国', 
            '马来西亚', '印度', '肯尼亚', '泰国', '缅甸', '台湾'
        ],
        'Submail_CNY': [0.28, 0.246, 0.271, 0.32, 0.18, 0.196, 0.11, 0.29, 0.09, 0.5, 0.218],
        'Submail_USD': [0.039, 0.034, 0.038, 0.045, 0.025, 0.027, 0.015, 0.041, 0.013, 0.070, 0.030],
        '润信_USD': [0.1201, 0.0326, 0.0601, 0.1111, 0.0450, 0.0250, 0.0182, 0.0551, 0.0034, 0.1772, np.nan],
        '牛信_USD': [0.04, 0.01, 0.013, 0.0065, 0.0392, 0.018, 0.002, np.nan, 0.00345, 0.025, 0.0252]
    }
    
    df = pd.DataFrame(data)
    
    # 计算最低价格和供应商
    def get_min_price_and_provider(row):
        prices = {
            'Submail': row['Submail_USD'],
            '润信': row['润信_USD'],
            '牛信': row['牛信_USD']
        }
        # 过滤掉NaN值
        valid_prices = {k: v for k, v in prices.items() if not pd.isna(v)}
        if valid_prices:
            min_provider = min(valid_prices, key=valid_prices.get)
            min_price = valid_prices[min_provider]
            max_price = max(valid_prices.values())
            price_ratio = max_price / min_price if min_price > 0 else 0
            return min_price, min_provider, price_ratio
        return np.nan, '', 0
    
    # 应用函数
    df[['最低价格', '最低价供应商', '价差倍数']] = df.apply(
        lambda row: pd.Series(get_min_price_and_provider(row)), axis=1
    )
    
    # 计算成本节省
    df['相对Submail节省'] = ((df['Submail_USD'] - df['最低价格']) / df['Submail_USD'] * 100).round(1)
    df['相对润信节省'] = ((df['润信_USD'] - df['最低价格']) / df['润信_USD'] * 100).round(1)
    
    # 创建Excel writer
    with pd.ExcelWriter('短信服务商价格对比.xlsx', engine='openpyxl') as writer:
        
        # 主要对比表
        df.to_excel(writer, sheet_name='价格对比', index=False)
        
        # 统计分析表
        stats_data = {
            '供应商': ['Submail', '润信', '牛信'],
            '平均价格(USD)': [
                df['Submail_USD'].mean(),
                df['润信_USD'].mean(),
                df['牛信_USD'].mean()
            ],
            '最低价格(USD)': [
                df['Submail_USD'].min(),
                df['润信_USD'].min(),
                df['牛信_USD'].min()
            ],
            '最高价格(USD)': [
                df['Submail_USD'].max(),
                df['润信_USD'].max(),
                df['牛信_USD'].max()
            ],
            '价格优势国家数': [
                sum(df['最低价供应商'] == 'Submail'),
                sum(df['最低价供应商'] == '润信'),
                sum(df['最低价供应商'] == '牛信')
            ]
        }
        
        stats_df = pd.DataFrame(stats_data)
        stats_df.to_excel(writer, sheet_name='统计分析', index=False)
        
        # 成本估算表
        volumes = [10000, 50000, 100000, 500000, 1000000]
        cost_data = []
        
        for volume in volumes:
            row = {'月发送量': volume}
            row['Submail月成本'] = volume * df['Submail_USD'].mean()
            row['润信月成本'] = volume * df['润信_USD'].mean()
            row['牛信月成本'] = volume * df['牛信_USD'].mean()
            row['选择牛信vs Submail年节省'] = (row['Submail月成本'] - row['牛信月成本']) * 12
            row['选择牛信vs润信年节省'] = (row['润信月成本'] - row['牛信月成本']) * 12
            cost_data.append(row)
        
        cost_df = pd.DataFrame(cost_data)
        cost_df.to_excel(writer, sheet_name='成本估算', index=False)
        
        # 服务商特色对比
        features_data = {
            '特色功能': [
                '价格优势',
                '服务稳定性',
                '技术支持',
                'WhatsApp支持',
                'Zalo支持',
                '运营商信息',
                '本土化服务',
                '计费透明度'
            ],
            'Submail': [
                '中等',
                '高',
                '中文支持好',
                '否',
                '否',
                '一般',
                '是',
                '高'
            ],
            '润信': [
                '特定市场有优势',
                '中等',
                '英文',
                '否',
                '否',
                '详细',
                '否',
                '中等'
            ],
            '牛信': [
                '整体最低',
                '待验证',
                '待确认',
                '是',
                '是',
                '待确认',
                '否',
                '待确认'
            ]
        }
        
        features_df = pd.DataFrame(features_data)
        features_df.to_excel(writer, sheet_name='服务商特色', index=False)
    
    print("✅ Excel文件已生成: 短信服务商价格对比.xlsx")
    return df

def generate_summary_report():
    """生成总结报告"""
    df = create_comparison_excel()
    
    print("\n=== 短信服务商价格对比分析总结 ===")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"覆盖国家/地区: {len(df)} 个")
    
    print("\n📊 整体价格分析:")
    print(f"Submail平均价格: ${df['Submail_USD'].mean():.4f}")
    print(f"润信平均价格: ${df['润信_USD'].mean():.4f}")  
    print(f"牛信平均价格: ${df['牛信_USD'].mean():.4f}")
    
    print("\n🏆 价格优势统计:")
    provider_wins = df['最低价供应商'].value_counts()
    for provider, count in provider_wins.items():
        print(f"{provider}: {count} 个国家/地区")
    
    print("\n💰 潜在成本节省 (以月发送10万条为例):")
    avg_submail = df['Submail_USD'].mean() * 100000
    avg_runxin = df['润信_USD'].mean() * 100000  
    avg_niuxin = df['牛信_USD'].mean() * 100000
    
    print(f"Submail月成本: ${avg_submail:.0f}")
    print(f"润信月成本: ${avg_runxin:.0f}")
    print(f"牛信月成本: ${avg_niuxin:.0f}")
    print(f"选择牛信vs Submail年节省: ${(avg_submail - avg_niuxin) * 12:.0f}")
    print(f"选择牛信vs润信年节省: ${(avg_runxin - avg_niuxin) * 12:.0f}")
    
    print("\n⚠️  价差最大的市场:")
    top_3_diff = df.nlargest(3, '价差倍数')[['国家/地区', '价差倍数', '最低价供应商']]
    for _, row in top_3_diff.iterrows():
        print(f"{row['国家/地区']}: {row['价差倍数']:.1f}倍 (最优: {row['最低价供应商']})")
    
    print("\n📋 推荐策略:")
    print("1. 成本优先: 选择牛信 (整体价格最低)")
    print("2. 稳定优先: 选择Submail (本土服务，支持好)")  
    print("3. 混合策略: 根据国家选择最优供应商")
    print("4. 风险控制: 先小批量测试各供应商实际效果")

if __name__ == "__main__":
    generate_summary_report()

# 创建需求文件
def create_requirements():
    requirements = """pandas==2.1.0
openpyxl==3.1.2
numpy==1.24.3"""
    
    with open('sms_comparison_requirements.txt', 'w') as f:
        f.write(requirements)
    print("✅ 依赖文件已创建: sms_comparison_requirements.txt")

if __name__ == "__main__":
    create_requirements()
    generate_summary_report() 