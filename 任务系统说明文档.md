# 能量系统任务配置文档

## 任务概览

### 基础任务 (一次性任务)

| 任务代码 | 任务标题 | 任务描述 | 能量奖励 | 权重 | 状态 | 备注 |
|---------|---------|---------|---------|------|------|------|
| 10001 | 选择兴趣偏好 | 启动远征获得基础能量点 | 100 | 1000 | ✅ 启用 | 新用户入门任务 |
| 10002 | 首次发布内容（图+文） | 启动远征获得基础能量点 | 150 | 2000 | ✅ 启用 | 内容创作激励 |
| 10003 | 首次邀请好友 (奖励给邀请人) | 启动远征获得基础能量点 | 200 | 3000 | ✅ 启用 | 社交推广任务 |
| 10004 | 绑定twitter | 启动远征获得基础能量点 | 200 | 4000 | ❌ 停用 | 社交媒体绑定 |
| 10005 | 绑定手机号 | 启动远征获得基础能量点 | 200 | 5000 | ✅ 启用 | 身份验证任务 |
| 10006 | 绑定邮箱 | 启动远征获得基础能量点 | 200 | 6000 | ✅ 启用 | 身份验证任务 |
| 10007 | 关注官方twitter | 启动远征获得基础能量点 | 200 | 7000 | ❌ 停用 | 官方推广任务 |
| 10008 | 加入TG群 | 启动远征获得基础能量点 | 200 | 8000 | ❌ 停用 | 社群建设任务 |

### 日常任务 (可重复任务)

| 任务代码 | 任务标题 | 任务描述 | 能量奖励 | 最大次数 | 权重 | 状态 | 特殊配置 |
|---------|---------|---------|---------|---------|------|------|---------|
| 20001 | 浏览文章/视频 时间累积 | 启动远征获得影响力能量点 | 60+ | 6级 | 10000 | ✅ 启用 | 分级奖励系统 |
| 20002 | 点赞内容 (每次) | 启动远征获得影响力能量点 | 50 | 20次 | 20000 | ✅ 启用 | 每日上限20次 |
| 20003 | 评论内容 (每次有效) | 启动远征获得影响力能量点 | 200 | 5次 | 30000 | ✅ 启用 | 每日上限5次 |
| 20004 | 分享内容到外部 (每次) | 启动远征获得影响力能量点 | 300 | 3次 | 40000 | ✅ 启用 | 每日上限3次 |
| 20005 | 好友活跃-好友活跃能量点加成比例 | 启动远征获得影响力能量点 | 300 | 无限 | 50000 | ⚠️ 测试中 | 加成机制 |
| 20006 | 成功邀请新人 | 启动远征获得影响力能量点 | 500 | 无限 | 60000 | ⚠️ 测试中 | 邀请奖励 |

## 任务配置详情

### 20001 - 浏览时长任务配置

| 等级 | 累积时长 | 显示时长 | 能量奖励 |
|------|---------|---------|---------|
| 1 | 60秒 | 60s | 100 |
| 2 | 120秒 | 3m | 200 |
| 3 | 420秒 | 10m | 700 |
| 4 | 600秒 | 20m | 1000 |
| 5 | 1500秒 | 45m | 1100 |
| 6 | 2700秒 | 90m | 2000 |

## 任务状态说明

- **状态 0**: ❌ 停用 - 任务已关闭，用户无法完成
- **状态 1**: ✅ 启用 - 任务正常开放，用户可以完成
- **状态 2**: ⚠️ 测试中 - 任务在测试阶段，可能有特殊限制

## 能量奖励统计

### 基础任务总奖励
- **启用任务总奖励**: 1,050 能量点
- **停用任务总奖励**: 800 能量点
- **所有基础任务总计**: 1,850 能量点

### 日常任务潜在奖励 (按每日上限计算)
- **浏览时长任务**: 最高 2,000 能量点/日
- **点赞任务**: 最高 1,000 能量点/日 (20次 × 50点)
- **评论任务**: 最高 1,000 能量点/日 (5次 × 200点)
- **分享任务**: 最高 900 能量点/日 (3次 × 300点)
- **每日常规任务总计**: 最高 4,900 能量点/日

## 机器人用户情况

### 机器人统计信息
- **机器人用户总数**: 10,000 个
- **创建日期**: 2025-06-21
- **状态**: 全部活跃
- **自动完成任务**: 10001, 10002, 10003, 10005, 10006

### 机器人影响
机器人用户会自动完成所有启用的基础任务，但不会影响真实用户的对账分析，因为：
1. 机器人用户在 `system_user_record` 表中有专门记录
2. 对账脚本已排除机器人用户的数据
3. 机器人用户不需要真实的手机或邮箱验证

## 对账建议

### 数据一致性检查
1. **手机验证任务 (10005)**: 排除机器人后数据基本一致
2. **邮箱验证任务 (10006)**: 排除机器人后数据基本一致
3. **UGC发布任务 (10002)**: 需要内容质量审核，可能存在少量差异
4. **兴趣选择任务 (10001)**: 需要至少3个兴趣才算完成
5. **邀请任务 (10003)**: 需要被邀请人完成人脸验证才算成功

### 优化建议
1. 定期检查停用任务是否需要重新启用
2. 监控机器人用户行为，确保不影响真实用户体验
3. 调整任务奖励以平衡用户参与度和系统成本
4. 完善任务完成条件，减少误判情况

## 更新记录

- **2025-06-25**: 创建文档，包含完整任务配置和机器人用户分析
- **2025-06-21**: 机器人用户系统上线，10,000个机器人用户创建 