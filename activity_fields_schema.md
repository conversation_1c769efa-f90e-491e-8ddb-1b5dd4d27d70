## 活动配置字段说明

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| id | String | 是 | 活动唯一标识符 | "creator" |
| titleKey | String | 是 | 活动标题的国际化键名 | "activity.creator.title" |
| descriptionKey | String | 是 | 活动描述的国际化键名 | "activity.creator.description" |
| icon | String | 是 | 活动图标名称 | "gift" |
| type | String | 是 | 跳转类型，可选值："native"、"h5"、"display" | "native" |
| url | String | 否 | 跳转链接，当 type 为 "display" 时可为空 | "xme://creator/home" |
| clickable | Boolean | 是 | 是否可点击，true 表示可点击跳转，false 表示仅展示 | true |
| status | Integer | 是 | 活动状态：<br>1 - 未开始<br>2 - 进行中<br>3 - 已结束 | 2 |
| order | Integer | 是 | 排序序号，数字越小越靠前 | 1 |
| needLogin | Boolean | 是 | 是否需要登录才能访问，true 表示需要登录，false 表示无需登录 | true |

## 跳转类型说明

- **native**: 应用内原生页面跳转，URL 格式为 `xme://[path]`
- **h5**: 网页跳转，URL 格式为标准 HTTP/HTTPS URL
- **display**: 仅展示，不可跳转，此时 url 可为空，clickable 应设为 false

## 示例配置

```json
{
  "id": "creator",
  "titleKey": "activity.creator.title",
  "descriptionKey": "activity.creator.description",
  "icon": "gift",
  "type": "native",
  "url": "xme://creator/home",
  "clickable": true,
  "status": 2,
  "order": 1,
  "needLogin": true
}
```

## 多语言翻译示例

### 语言键值对应表

| 键名 | 中文(zh-CN) | 英文(en) | 日文(ja) |
|------|------------|----------|----------|
| activity.creator.title | 创作者中心 | Creator Center | クリエイターセンター |
| activity.creator.description | 发布作品，获取奖励 | Publish works and get rewards | 作品を公開して報酬を獲得 |
