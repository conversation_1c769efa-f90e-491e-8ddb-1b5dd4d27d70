DELIMITER //

DROP PROCEDURE IF EXISTS migrate_database;
CREATE PROCEDURE migrate_database(
    IN source_db VARCHAR(64),
    IN target_db VARCHAR(64)
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE current_table VARCHAR(64);
    
    -- Cursor to get all tables from source database
    DECLARE table_cursor CURSOR FOR 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = source_db AND table_type = 'BASE TABLE';
    
    -- Declare continue handler
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Create target database if it doesn't exist
    SET @sql = CONCAT('CREATE DATABASE IF NOT EXISTS `', target_db, '`');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- Log start of migration
    SELECT CONCAT('Starting migration from ', source_db, ' to ', target_db) AS 'Info';
    
    -- Open cursor
    OPEN table_cursor;
    
    -- Loop through all tables
    read_loop: LOOP
        FETCH table_cursor INTO current_table;
        
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Create table in target database with same structure
        SET @sql = CONCAT('CREATE TABLE IF NOT EXISTS `', target_db, '`.`', current_table, '` LIKE `', source_db, '`.`', current_table, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- Copy data
        SET @sql = CONCAT('INSERT IGNORE INTO `', target_db, '`.`', current_table, '` SELECT * FROM `', source_db, '`.`', current_table, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- Log progress
        SELECT CONCAT('Migrated table: ', current_table) AS 'Progress';
    END LOOP;
    
    -- Close cursor
    CLOSE table_cursor;
    
    -- Log completion
    SELECT CONCAT('Migration completed from ', source_db, ' to ', target_db) AS 'Info';
END //

-- Procedure to migrate specific tables
DROP PROCEDURE IF EXISTS migrate_tables;
CREATE PROCEDURE migrate_tables(
    IN source_db VARCHAR(64),
    IN target_db VARCHAR(64),
    IN table_names VARCHAR(1000)
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE current_table VARCHAR(64);
    DECLARE table_list TEXT;
    DECLARE next_comma_pos INT;
    
    -- Create target database if it doesn't exist
    SET @sql = CONCAT('CREATE DATABASE IF NOT EXISTS `', target_db, '`');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- Log start of migration
    SELECT CONCAT('Starting migration of selected tables from ', source_db, ' to ', target_db) AS 'Info';
    
    -- Process the comma-separated list of tables
    SET table_list = table_names;
    
    -- Loop through the table list
    tables_loop: LOOP
        -- Exit if no more tables
        IF LENGTH(table_list) = 0 THEN
            LEAVE tables_loop;
        END IF;
        
        -- Find the next comma position
        SET next_comma_pos = LOCATE(',', table_list);
        
        -- Extract the table name
        IF next_comma_pos > 0 THEN
            SET current_table = TRIM(SUBSTRING(table_list, 1, next_comma_pos - 1));
            SET table_list = SUBSTRING(table_list, next_comma_pos + 1);
        ELSE
            -- Last table in the list
            SET current_table = TRIM(table_list);
            SET table_list = '';
        END IF;
        
        -- Skip if table_name is empty
        IF LENGTH(current_table) = 0 THEN
            ITERATE tables_loop;
        END IF;
        
        -- Check if table exists in source database
        SET @table_exists = 0;
        SELECT COUNT(*) INTO @table_exists 
        FROM information_schema.tables 
        WHERE table_schema = source_db AND table_name = current_table;
        
        IF @table_exists > 0 THEN
            -- Create table in target database with same structure
            SET @sql = CONCAT('CREATE TABLE IF NOT EXISTS `', target_db, '`.`', current_table, '` LIKE `', source_db, '`.`', current_table, '`');
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- Copy data
            SET @sql = CONCAT('INSERT IGNORE INTO `', target_db, '`.`', current_table, '` SELECT * FROM `', source_db, '`.`', current_table, '`');
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- Log data migration
            SELECT CONCAT('Migrated table: ', current_table) AS 'Progress';
        ELSE
            SELECT CONCAT('Table not found in source database: ', current_table) AS 'Warning';
        END IF;
    END LOOP;
    
    -- Log completion
    SELECT CONCAT('Migration of selected tables completed from ', source_db, ' to ', target_db) AS 'Info';
END //

DELIMITER ;

-- Example usage:
-- CALL migrate_database('source_database', 'target_database');
-- CALL migrate_tables('source_database', 'target_database', 'table1,table2,table3');

-- Additional procedure to migrate specific tables
DELIMITER //

CREATE PROCEDURE migrate_selected_tables(
    IN source_db VARCHAR(64),
    IN target_db VARCHAR(64),
    IN table_list TEXT
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(64);
    DECLARE table_list_copy TEXT;
    DECLARE next_comma_pos INT;
    
    -- Create target database if it doesn't exist
    SET @create_db = CONCAT('CREATE DATABASE IF NOT EXISTS `', target_db, '`');
    PREPARE stmt FROM @create_db;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- Log start of migration
    SELECT CONCAT('Starting migration of selected tables from ', source_db, ' to ', target_db) AS 'Info';
    
    -- Process the comma-separated list of tables
    SET table_list_copy = table_list;
    
    -- Loop through the table list
    tables_loop: LOOP
        -- Find the next comma position
        SET next_comma_pos = LOCATE(',', table_list_copy);
        
        -- Extract the table name
        IF next_comma_pos > 0 THEN
            SET table_name = TRIM(SUBSTRING(table_list_copy, 1, next_comma_pos - 1));
            SET table_list_copy = SUBSTRING(table_list_copy, next_comma_pos + 1);
        ELSE
            -- Last table in the list
            SET table_name = TRIM(table_list_copy);
            SET table_list_copy = '';
        END IF;
        
        -- Skip if table_name is empty
        IF LENGTH(table_name) = 0 THEN
            IF LENGTH(table_list_copy) = 0 THEN
                LEAVE tables_loop;
            ELSE
                ITERATE tables_loop;
            END IF;
        END IF;
        
        -- Check if table exists in source database
        SET @table_exists = 0;
        SELECT COUNT(*) INTO @table_exists 
        FROM information_schema.tables 
        WHERE table_schema = source_db AND table_name = table_name;
        
        IF @table_exists > 0 THEN
            -- Create table in target database with same structure
            SET @create_table_stmt = CONCAT('CREATE TABLE IF NOT EXISTS `', target_db, '`.`', table_name, '` LIKE `', source_db, '`.`', table_name, '`');
            PREPARE stmt FROM @create_table_stmt;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- Copy data
            SET @copy_data_stmt = CONCAT('INSERT IGNORE INTO `', target_db, '`.`', table_name, '` SELECT * FROM `', source_db, '`.`', table_name, '`');
            PREPARE stmt FROM @copy_data_stmt;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- Log data migration
            SELECT CONCAT('Migrated data for table: ', table_name) AS 'Progress';
        ELSE
            SELECT CONCAT('Table not found in source database: ', table_name) AS 'Warning';
        END IF;
        
        -- Exit if no more tables
        IF LENGTH(table_list_copy) = 0 THEN
            LEAVE tables_loop;
        END IF;
    END LOOP;
    
    -- Log completion
    SELECT CONCAT('Migration of selected tables completed from ', source_db, ' to ', target_db) AS 'Info';
END //

DELIMITER ;

-- Example usage:
-- CALL migrate_selected_tables('source_database', 'target_database', 'table1,table2,table3');

-- Procedure to get table creation script
DELIMITER //

CREATE PROCEDURE get_create_table_script(
    IN p_schema VARCHAR(64),
    IN p_table VARCHAR(64)
)
BEGIN
    SELECT CONCAT(
        'CREATE TABLE IF NOT EXISTS `', p_table, '` (\n',
        GROUP_CONCAT(
            CONCAT('  `', column_name, '` ', column_type, 
                IF(is_nullable = 'NO', ' NOT NULL', ''),
                IF(column_default IS NOT NULL, CONCAT(' DEFAULT ', IF(column_default = 'CURRENT_TIMESTAMP', column_default, CONCAT("'", column_default, "'"))), ''),
                IF(extra != '', CONCAT(' ', extra), '')
            ) 
            ORDER BY ordinal_position
            SEPARATOR ',\n'
        ),
        IF(COUNT(k.column_name) > 0, ',\n', '\n'),
        GROUP_CONCAT(
            CONCAT('  ', 
                IF(k.constraint_name = 'PRIMARY', 'PRIMARY KEY', 
                    IF(k.constraint_name LIKE 'UNIQUE%', 
                        CONCAT('UNIQUE KEY `', k.constraint_name, '`'), 
                        CONCAT('KEY `', k.constraint_name, '`')
                    )
                ),
                ' (`', GROUP_CONCAT(k.column_name ORDER BY k.ordinal_position SEPARATOR '`, `'), '`)'
            )
            SEPARATOR ',\n'
        ),
        '\n) ENGINE=', t.engine, 
        ' DEFAULT CHARSET=', t.table_collation, 
        IF(t.auto_increment IS NOT NULL, CONCAT(' AUTO_INCREMENT=', t.auto_increment), ''),
        ';'
    ) AS create_table_script
    FROM information_schema.columns c
    LEFT JOIN (
        SELECT 
            k.constraint_name,
            k.table_schema,
            k.table_name,
            k.column_name,
            k.ordinal_position
        FROM information_schema.key_column_usage k
        JOIN information_schema.table_constraints tc
        ON k.constraint_name = tc.constraint_name
        AND k.table_schema = tc.table_schema
        AND k.table_name = tc.table_name
        WHERE tc.constraint_type IN ('PRIMARY KEY', 'UNIQUE')
        AND k.table_schema = p_schema
        AND k.table_name = p_table
    ) k ON c.table_schema = k.table_schema AND c.table_name = k.table_name AND c.column_name = k.column_name
    JOIN information_schema.tables t ON c.table_schema = t.table_schema AND c.table_name = t.table_name
    WHERE c.table_schema = p_schema
    AND c.table_name = p_table
    GROUP BY c.table_name;
END //

DELIMITER ;

-- Example usage:
-- CALL get_create_table_script('source_database', 'table_name');
