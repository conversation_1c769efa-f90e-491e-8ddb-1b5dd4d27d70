# 定时任务系统设计

## 概述

定时任务系统负责处理XME挖矿平台中的各类周期性任务，包括能量点结算、数据统计、用户状态更新等。系统设计追求简洁高效，确保任务按时执行并能够在分布式环境中正确处理。

## 表结构设计

### 1. 定时任务配置表

```sql
CREATE TABLE scheduled_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_key VARCHAR(50) NOT NULL COMMENT '任务唯一标识',
    cron_expression VARCHAR(50) NOT NULL COMMENT 'cron表达式',
    task_handler VARCHAR(100) NOT NULL COMMENT '任务处理器类名',
    task_param JSON NULL COMMENT '任务参数',
    retry_count INT NOT NULL DEFAULT 3 COMMENT '重试次数',
    timeout_seconds INT NOT NULL DEFAULT 300 COMMENT '超时时间(秒)',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    description VARCHAR(255) NULL COMMENT '任务描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_task_key (task_key)
);
```

### 2. 任务执行记录表

```sql
CREATE TABLE task_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL COMMENT '关联的任务ID',
    execution_id VARCHAR(36) NOT NULL COMMENT '执行ID',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    status VARCHAR(20) NOT NULL COMMENT '状态: RUNNING, SUCCESS, FAILED',
    server_id VARCHAR(50) NOT NULL COMMENT '执行服务器ID',
    affected_rows INT NULL COMMENT '影响行数',
    error_message TEXT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
);
```

### 3. 任务锁表

```sql
CREATE TABLE task_locks (
    task_key VARCHAR(50) PRIMARY KEY COMMENT '任务标识',
    server_id VARCHAR(50) NOT NULL COMMENT '持有锁的服务器ID',
    lock_time TIMESTAMP NOT NULL COMMENT '锁定时间',
    expire_time TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_expire_time (expire_time)
);
```

## 主要任务类型

### 1. 小时能量结算任务

**执行频率**: 每小时整点执行

**任务描述**: 结算用户当前小时的能量点，应用各类加成，更新用户总能量

**处理逻辑**:
```java
@Component("hourlyEnergySettlement")
public class HourlyEnergySettlementTask implements TaskHandler {
    
    @Override
    public TaskResult execute(TaskContext context) {
        // 1. 获取上一小时时间戳
        long hourTimestamp = getLastHourTimestamp();
        
        // 2. 批量获取有能量产出的用户
        List<UserHourlyEnergy> hourlyEnergies = userHourlyEnergyRepository
            .findByHourTimestamp(hourTimestamp);
            
        // 3. 批量处理用户能量结算
        int processedCount = 0;
        for (UserHourlyEnergy energy : hourlyEnergies) {
            // 应用加成规则
            applyEnergyBonuses(energy);
            
            // 更新用户总能量
            updateUserTotalEnergy(energy.getUserId(), energy.getHourPoints());
            
            processedCount++;
        }
        
        // 4. 返回处理结果
        return new TaskResult(true, "Processed " + processedCount + " users' energy");
    }
}
```

### 2. 每日任务重置

**执行频率**: 每天凌晨0点执行

**任务描述**: 重置用户每日任务状态，更新任务配置

**处理逻辑**:
```java
@Component("dailyTaskReset")
public class DailyTaskResetTask implements TaskHandler {
    
    @Override
    public TaskResult execute(TaskContext context) {
        // 1. 重置所有用户的每日任务状态
        int resetCount = userTaskRepository.resetDailyTasks();
        
        // 2. 更新任务配置（如有变更）
        taskConfigService.refreshTaskConfigurations();
        
        // 3. 清理过期数据
        int cleanedCount = cleanupService.cleanExpiredData();
        
        // 4. 返回处理结果
        return new TaskResult(true, 
            "Reset " + resetCount + " daily tasks, cleaned " + cleanedCount + " expired records");
    }
}
```

### 3. 用户成就检查

**执行频率**: 每小时执行一次

**任务描述**: 检查用户是否达成新的成就，发放成就奖励

**处理逻辑**:
```java
@Component("achievementCheck")
public class AchievementCheckTask implements TaskHandler {
    
    @Override
    public TaskResult execute(TaskContext context) {
        // 1. 获取需要检查的成就列表
        List<Achievement> achievements = achievementRepository.findActiveAchievements();
        
        // 2. 获取活跃用户列表（可分批处理）
        List<User> activeUsers = userRepository.findRecentActiveUsers(1000);
        
        // 3. 检查每个用户的成就完成情况
        int awardedCount = 0;
        for (User user : activeUsers) {
            for (Achievement achievement : achievements) {
                if (achievementService.checkAchievement(user.getId(), achievement)) {
                    // 发放成就奖励
                    achievementService.awardAchievement(user.getId(), achievement);
                    awardedCount++;
                }
            }
        }
        
        // 4. 返回处理结果
        return new TaskResult(true, "Awarded " + awardedCount + " achievements");
    }
}
```

## 任务执行流程

```mermaid
graph TD
    A["任务调度器"] --> B{"是否到达执行时间?"}
    B -->|是| C["获取任务锁"]
    B -->|否| A
    
    C --> D{"是否获取到锁?"}
    D -->|是| E["执行任务"]
    D -->|否| A
    
    E --> F["记录执行结果"]
    F --> G["释放任务锁"]
    G --> A
    
    E --> H{"执行是否出错?"}
    H -->|是| I["记录错误信息"]
    I --> J{"是否需要重试?"}
    J -->|是| K["安排重试"]
    J -->|否| G
    K --> G
```

## 分布式任务处理

为确保在多服务器环境中任务不被重复执行，系统使用数据库锁机制：

```java
public boolean acquireTaskLock(String taskKey, String serverId, int timeoutSeconds) {
    try {
        // 尝试获取锁
        int updated = jdbcTemplate.update(
            "INSERT INTO task_locks (task_key, server_id, lock_time, expire_time) " +
            "VALUES (?, ?, NOW(), DATE_ADD(NOW(), INTERVAL ? SECOND)) " +
            "ON DUPLICATE KEY UPDATE " +
            "server_id = IF(expire_time < NOW(), VALUES(server_id), server_id), " +
            "lock_time = IF(expire_time < NOW(), NOW(), lock_time), " +
            "expire_time = IF(expire_time < NOW(), VALUES(expire_time), expire_time)",
            taskKey, serverId, timeoutSeconds
        );
        
        if (updated > 0) {
            return true;
        }
        
        // 检查是否是当前持有者
        String currentHolder = jdbcTemplate.queryForObject(
            "SELECT server_id FROM task_locks WHERE task_key = ?",
            String.class, taskKey
        );
        
        return serverId.equals(currentHolder);
    } catch (Exception e) {
        log.error("Failed to acquire lock for task: {}", taskKey, e);
        return false;
    }
}
```

## 任务监控与管理

### 1. 任务状态监控

- 通过任务执行记录表监控任务执行情况
- 关注长时间运行的任务和频繁失败的任务

### 2. 任务管理接口

```
GET /api/admin/tasks                 # 获取所有任务列表
GET /api/admin/tasks/{taskKey}       # 获取单个任务详情
PUT /api/admin/tasks/{taskKey}       # 更新任务配置
POST /api/admin/tasks/{taskKey}/run  # 手动触发任务
GET /api/admin/tasks/{taskKey}/logs  # 获取任务执行日志
```

### 3. 告警机制

- 任务连续失败超过阈值触发告警
- 任务执行时间超过预期触发告警
- 关键任务未按时执行触发告警

## 部署建议

1. **多实例部署**：部署多个任务处理实例确保高可用
2. **独立数据库连接池**：为任务处理分配独立的数据库连接池
3. **监控集成**：与监控系统集成，实时监控任务执行状态

## 示例任务配置

```sql
-- 小时能量结算任务
INSERT INTO scheduled_tasks 
(task_name, task_key, cron_expression, task_handler, task_param, is_active, description)
VALUES 
('小时能量结算', 'hourly_energy_settlement', '0 0 * * * ?', 'hourlyEnergySettlement', 
 '{"batchSize": 1000}', TRUE, '每小时整点结算用户能量点');

-- 每日任务重置
INSERT INTO scheduled_tasks 
(task_name, task_key, cron_expression, task_handler, task_param, is_active, description)
VALUES 
('每日任务重置', 'daily_task_reset', '0 0 0 * * ?', 'dailyTaskReset', 
 '{}', TRUE, '每天凌晨重置用户每日任务状态');

-- 用户成就检查
INSERT INTO scheduled_tasks 
(task_name, task_key, cron_expression, task_handler, task_param, is_active, description)
VALUES 
('用户成就检查', 'achievement_check', '0 30 * * * ?', 'achievementCheck', 
 '{"userBatchSize": 1000}', TRUE, '每小时检查用户成就完成情况');
```
