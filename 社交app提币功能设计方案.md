# 社交App提币功能设计方案

## 1. 系统概述

### 1.1 背景
基于现有的XME挖矿系统，设计提币功能对接三方交易所，支持用户将平台内的XME、狗狗币等数字货币提取到外部钱包或交易所。

### 1.2 核心目标
- 安全可靠的提币流程
- 完善的风控体系
- 用户友好的操作体验
- 合规的KYC流程
- 高效的三方交易所对接

### 1.3 支持币种
- XME（主要币种）
- DOGE（狗狗币）
- 其他主流数字货币（可扩展）

## 2. 整体架构设计

### 2.1 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   移动端App     │    │     Web端       │    │   管理后台      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API网关              │
                    │  - 认证鉴权               │
                    │  - 限流熔断               │
                    │  - 请求路由               │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────┴───────┐    ┌─────────┴─────────┐    ┌─────────┴─────────┐
│  提币服务     │    │   风控服务        │    │   KYC服务         │
│- 提币申请     │    │- 风险评估         │    │- 身份验证         │
│- 状态管理     │    │- 异常检测         │    │- 实名认证         │
│- 流程控制     │    │- 规则引擎         │    │- 合规检查         │
└───────┬───────┘    └─────────┬─────────┘    └─────────┬─────────┘
        │                     │                        │
        └─────────────────────┼────────────────────────┘
                             │
                ┌─────────────┴─────────────┐
                │     钱包服务              │
                │  - 热钱包管理             │
                │  - 冷钱包管理             │
                │  - 地址生成               │
                │  - 交易签名               │
                └─────────────┬─────────────┘
                             │
        ┌────────────────────┼────────────────────┐
        │                   │                    │
┌───────┴───────┐  ┌────────┴────────┐  ┌───────┴───────┐
│  Point服务    │  │  消息队列       │  │  三方交易所   │
│- 余额管理     │  │- Kafka          │  │- Binance      │
│- 交易记录     │  │- 异步处理       │  │- OKX          │
│- 资产冻结     │  │- 消息可靠性     │  │- 其他交易所   │
└───────────────┘  └─────────────────┘  └───────────────┘
```

### 2.2 核心服务设计

#### 2.2.1 提币服务 (Withdrawal Service)
- **职责**：提币流程的核心控制器
- **功能**：
  - 提币申请处理
  - 提币状态管理
  - 提币流程编排
  - 提币限额控制
  - 提币历史查询

#### 2.2.2 风控服务 (Risk Control Service)
- **职责**：提币风险评估和控制
- **功能**：
  - 实时风险评估
  - 异常行为检测
  - 风控规则引擎
  - 黑名单管理
  - 风险等级评定

#### 2.2.3 KYC服务 (KYC Service)
- **职责**：用户身份验证和合规检查
- **功能**：
  - 身份证件验证
  - 人脸识别验证
  - 地址证明验证
  - 合规等级评定
  - 监管报告生成

#### 2.2.4 钱包服务 (Wallet Service)
- **职责**：数字货币钱包管理
- **功能**：
  - 热钱包管理
  - 冷钱包管理
  - 地址生成和验证
  - 交易签名和广播
  - 余额监控

#### 2.2.5 三方对接服务 (Exchange Integration Service)
- **职责**：与三方交易所的API对接
- **功能**：
  - 交易所API调用
  - 订单状态同步
  - 汇率信息获取
  - 手续费计算
  - 异常处理和重试

## 3. 数据流设计

### 3.1 提币流程数据流

```
用户发起提币申请
    ↓
验证用户身份和KYC状态
    ↓
检查账户余额和提币限额
    ↓
风控系统评估风险等级
    ↓
创建提币订单（待审核状态）
    ↓
自动审核/人工审核
    ↓
审核通过后冻结用户资产
    ↓
调用钱包服务执行提币
    ↓
监控区块链交易状态
    ↓
更新订单状态并通知用户
    ↓
记录完整的操作日志
```

### 3.2 与现有系统的集成

#### 3.2.1 与Point服务集成
- 查询用户XME余额
- 冻结提币金额
- 扣减用户余额
- 记录交易流水

#### 3.2.2 与用户服务集成
- 获取用户基本信息
- 验证用户状态
- 更新用户风险等级
- 记录用户操作日志

#### 3.2.3 与挖矿系统集成
- 确保提币不影响挖矿收益
- 处理挖矿收益的提币申请
- 维护用户总资产统计

## 4. 安全机制设计

### 4.1 多层安全防护

#### 4.1.1 接口安全
- API签名验证
- 请求频率限制
- IP白名单机制
- 防重放攻击

#### 4.1.2 业务安全
- 多重身份验证
- 提币地址白名单
- 大额提币人工审核
- 异常行为监控

#### 4.1.3 资金安全
- 热冷钱包分离
- 多重签名机制
- 资金池监控
- 异常转账告警

### 4.2 风控规则体系

#### 4.2.1 用户维度风控
- 新用户提币限制
- 单日提币限额
- 单笔提币限额
- 提币频率限制

#### 4.2.2 行为维度风控
- 异常登录检测
- 设备指纹识别
- IP地址分析
- 操作时间分析

#### 4.2.3 资金维度风控
- 大额资金监控
- 资金流向分析
- 可疑交易识别
- 洗钱风险评估

## 5. 技术实现要点

### 5.1 数据库设计原则
- 分库分表策略
- 读写分离
- 数据备份和恢复
- 事务一致性保证

### 5.2 缓存策略
- Redis集群部署
- 热点数据缓存
- 缓存一致性保证
- 缓存穿透防护

### 5.3 消息队列设计
- Kafka集群部署
- 消息可靠性保证
- 消息幂等性处理
- 死信队列处理

### 5.4 监控和告警
- 系统性能监控
- 业务指标监控
- 异常告警机制
- 日志分析系统

## 6. 合规和监管

### 6.1 KYC合规要求
- 身份证件验证
- 地址证明验证
- 资金来源证明
- 定期合规审查

### 6.2 AML反洗钱
- 可疑交易监控
- 大额交易报告
- 黑名单筛查
- 监管报告生成

### 6.3 数据保护
- 个人信息加密
- 数据访问控制
- 数据保留政策
- 数据删除机制

## 7. 用户体系对接方案

### 7.1 用户身份验证体系

#### 7.1.1 多级身份验证
```
Level 0: 基础注册用户
- 手机号/邮箱验证
- 基础信息填写
- 提币限制：不允许提币

Level 1: 初级认证用户
- 身份证件上传
- 基础信息验证
- 提币限制：单日1000 XME

Level 2: 中级认证用户
- 人脸识别验证
- 地址证明上传
- 提币限制：单日10000 XME

Level 3: 高级认证用户
- 银行卡绑定验证
- 资金来源证明
- 提币限制：单日50000 XME
```

#### 7.1.2 KYC流程设计

**第一阶段：基础信息收集**
```
1. 个人基本信息
   - 真实姓名
   - 身份证号码
   - 出生日期
   - 性别
   - 国籍

2. 联系信息
   - 手机号码（已验证）
   - 邮箱地址（已验证）
   - 居住地址
   - 邮政编码

3. 职业信息
   - 职业类型
   - 工作单位
   - 年收入范围
   - 资金来源
```

**第二阶段：身份证件验证**
```
1. 证件类型支持
   - 身份证（中国大陆）
   - 护照（国际用户）
   - 驾驶证（部分地区）
   - 其他政府颁发证件

2. 证件验证流程
   - 证件照片上传
   - OCR自动识别
   - 证件真伪验证
   - 信息一致性检查
   - 人工审核（必要时）

3. 验证标准
   - 证件清晰度检查
   - 证件有效期验证
   - 证件信息完整性
   - 与填写信息一致性
```

**第三阶段：生物识别验证**
```
1. 人脸识别验证
   - 活体检测
   - 人脸与证件照比对
   - 多角度人脸采集
   - 防伪检测

2. 验证流程
   - 引导用户完成人脸采集
   - 实时活体检测
   - 与证件照片比对
   - 相似度评分
   - 人工复审（低分情况）

3. 技术要求
   - 活体检测准确率 > 99%
   - 人脸比对准确率 > 95%
   - 防攻击能力强
   - 用户体验友好
```

### 7.2 风控体系设计

#### 7.2.1 用户风险评级模型

**风险评分维度**
```
1. 身份风险 (25%)
   - KYC完成度
   - 身份验证真实性
   - 证件有效性
   - 个人信息完整性

2. 行为风险 (30%)
   - 登录行为模式
   - 操作时间分布
   - 设备使用习惯
   - IP地址稳定性

3. 交易风险 (25%)
   - 交易频率
   - 交易金额
   - 交易时间
   - 资金流向

4. 关联风险 (20%)
   - 设备关联度
   - IP关联度
   - 社交关系图谱
   - 行为相似度
```

**风险等级定义**
```
低风险 (0-30分)
- 绿色通道处理
- 自动审核通过
- 正常提币限额

中风险 (31-70分)
- 加强监控
- 部分人工审核
- 适当降低限额

高风险 (71-100分)
- 严格审核
- 人工审核必须
- 大幅限制额度
- 可能暂停服务
```

#### 7.2.2 实时风控规则引擎

**规则类型**
```
1. 阈值类规则
   - 单笔提币金额限制
   - 日/周/月累计限额
   - 提币频率限制
   - 余额比例限制

2. 模式类规则
   - 异常时间段操作
   - 异常地理位置
   - 设备异常切换
   - 行为模式突变

3. 关联类规则
   - 多账户关联检测
   - 设备指纹关联
   - IP地址关联
   - 钱包地址关联

4. 黑名单规则
   - 用户黑名单
   - 设备黑名单
   - IP黑名单
   - 钱包地址黑名单
```

**规则执行流程**
```
提币申请 → 规则引擎评估 → 风险评分计算 → 决策输出
                ↓
        [通过/拒绝/人工审核]
                ↓
        执行相应处理流程
```

### 7.3 与现有用户系统集成

#### 7.3.1 用户数据扩展
```sql
-- 扩展现有用户表
ALTER TABLE users ADD COLUMN kyc_level TINYINT DEFAULT 0 COMMENT 'KYC等级 0-3';
ALTER TABLE users ADD COLUMN risk_score INT DEFAULT 0 COMMENT '风险评分 0-100';
ALTER TABLE users ADD COLUMN withdrawal_limit DECIMAL(20,8) DEFAULT 0 COMMENT '提币限额';
ALTER TABLE users ADD COLUMN last_kyc_time TIMESTAMP NULL COMMENT '最后KYC时间';
ALTER TABLE users ADD COLUMN kyc_status ENUM('pending','approved','rejected') DEFAULT 'pending';
```

#### 7.3.2 KYC信息存储
```sql
-- KYC信息表
CREATE TABLE user_kyc_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    id_number VARCHAR(50) NOT NULL COMMENT '身份证号',
    id_type ENUM('id_card','passport','driver_license') NOT NULL,
    birth_date DATE NOT NULL,
    gender ENUM('male','female') NOT NULL,
    nationality VARCHAR(10) NOT NULL,
    address TEXT NOT NULL COMMENT '居住地址',
    occupation VARCHAR(100) COMMENT '职业',
    income_range VARCHAR(50) COMMENT '收入范围',
    id_front_url VARCHAR(500) COMMENT '证件正面照',
    id_back_url VARCHAR(500) COMMENT '证件背面照',
    face_photo_url VARCHAR(500) COMMENT '人脸照片',
    address_proof_url VARCHAR(500) COMMENT '地址证明',
    submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    review_time TIMESTAMP NULL,
    review_status ENUM('pending','approved','rejected') DEFAULT 'pending',
    review_comment TEXT COMMENT '审核意见',
    reviewer_id BIGINT COMMENT '审核员ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_review_status (review_status),
    INDEX idx_submit_time (submit_time)
);
```

#### 7.3.3 风控记录表
```sql
-- 风控评估记录表
CREATE TABLE risk_assessment_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    assessment_type ENUM('login','withdrawal','transaction') NOT NULL,
    risk_score INT NOT NULL COMMENT '风险评分',
    risk_level ENUM('low','medium','high') NOT NULL,
    risk_factors JSON COMMENT '风险因子详情',
    device_fingerprint VARCHAR(255) COMMENT '设备指纹',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    assessment_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    action_taken ENUM('pass','block','manual_review') NOT NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_risk_level (risk_level),
    INDEX idx_assessment_time (assessment_time)
);
```

### 7.4 KYC审核流程

#### 7.4.1 自动审核流程
```
1. 证件OCR识别
   - 提取证件信息
   - 验证证件格式
   - 检查证件有效期
   - 与填写信息比对

2. 证件真伪验证
   - 调用第三方验证API
   - 检查证件数据库
   - 验证证件特征
   - 防伪标识检查

3. 人脸识别验证
   - 活体检测
   - 人脸质量评估
   - 与证件照比对
   - 相似度评分

4. 自动决策
   - 综合评分计算
   - 规则引擎判断
   - 输出审核结果
   - 记录审核日志
```

#### 7.4.2 人工审核流程
```
1. 审核任务分配
   - 按地区分配审核员
   - 考虑审核员工作量
   - 紧急情况优先处理
   - 避免利益冲突

2. 审核标准
   - 证件清晰度要求
   - 信息一致性检查
   - 人脸相似度标准
   - 可疑情况识别

3. 审核决策
   - 通过/拒绝/补充材料
   - 详细审核意见
   - 风险等级评定
   - 后续处理建议

4. 质量控制
   - 审核结果抽查
   - 审核员绩效评估
   - 审核标准培训
   - 争议处理机制
```
