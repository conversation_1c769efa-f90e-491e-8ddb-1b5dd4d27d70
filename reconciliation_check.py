#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
能量系统对账脚本
检查用户验证状态与任务记录、能量记录之间的数据一致性
"""

import pymysql
import logging
from datetime import datetime
from collections import defaultdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"reconciliation_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)

# Database server configurations
# 媒体用户数据库配置
USER_DB = {
    'host': 'xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user'
}

# 任务数据库配置
TASK_DB = {
    'host': 'xme-prod-media-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task'
}

# UGC内容数据库配置
UGC_DB = {
    'host': 'xme-prod-rds-contentflow.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'ugc',
    'password': 'Ne2nbEk9',
    'database': 'ugc_hub'
}

# 用户行为数据库配置
BEHAVIOR_DB = {
    'host': 'xme-prod-rds-analysis.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-analysis-user',
    'password': 'k8xet*5YKT',
    'database': 'content_behavior'
}

def get_db_config(**kwargs):
    """准备数据库配置"""
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

class ReconciliationChecker:
    def __init__(self):
        self.differences = {
            'phone_verify': {'missing_tasks': [], 'missing_energy': [], 'extra_tasks': [], 'extra_energy': []},
            'email_verify': {'missing_tasks': [], 'missing_energy': [], 'extra_tasks': [], 'extra_energy': []},
            'ugc_posts': {'missing_tasks': [], 'missing_energy': [], 'extra_tasks': [], 'extra_energy': []},
            'interests': {'missing_tasks': [], 'missing_energy': [], 'extra_tasks': [], 'extra_energy': []},
            'face_invite': {'missing_tasks': [], 'missing_energy': [], 'extra_tasks': [], 'extra_energy': []}
        }

    def check_phone_verification_reconciliation(self):
        """检查手机验证的对账一致性"""
        logging.info("开始检查手机验证对账...")
        
        # 获取所有手机验证用户
        user_conn = pymysql.connect(**get_db_config(**USER_DB))
        task_conn = pymysql.connect(**get_db_config(**TASK_DB))
        
        try:
            # 获取手机验证用户
            with user_conn.cursor() as cursor:
                cursor.execute("SELECT uid FROM client_user WHERE phone_verify = 1")
                phone_verified_users = set(row['uid'] for row in cursor.fetchall())
            
            logging.info(f"手机验证用户总数: {len(phone_verified_users)}")
            
            # 获取任务记录中的手机验证任务 (任务代码 10005)
            task_users = set()
            energy_users = set()
            
            with task_conn.cursor() as cursor:
                # 检查所有分片的user_task_record表
                for shard in range(1024):
                    try:
                        table_name = f"user_task_record_{shard}"
                        query = f"""
                        SELECT DISTINCT user_id 
                        FROM {table_name} 
                        WHERE task_code = '10005' AND delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            task_users.add(row['user_id'])
                    except Exception as e:
                        # 表不存在或其他错误，跳过
                        continue
                
                # 检查所有分片的user_ep_records表
                for shard in range(1024):
                    try:
                        table_name = f"user_ep_records_{shard}"
                        query = f"""
                        SELECT DISTINCT uer.user_id 
                        FROM {table_name} uer
                        JOIN user_task_record_{shard} utr ON uer.task_record_id = utr.id
                        WHERE utr.task_code = '10005' AND utr.delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            energy_users.add(row['user_id'])
                    except Exception as e:
                        # 表不存在或其他错误，跳过
                        continue
            
            logging.info(f"任务记录中手机验证用户数: {len(task_users)}")
            logging.info(f"能量记录中手机验证用户数: {len(energy_users)}")
            
            # 查找差异
            self.differences['phone_verify']['missing_tasks'] = list(phone_verified_users - task_users)
            self.differences['phone_verify']['missing_energy'] = list(phone_verified_users - energy_users)
            self.differences['phone_verify']['extra_tasks'] = list(task_users - phone_verified_users)
            self.differences['phone_verify']['extra_energy'] = list(energy_users - phone_verified_users)
            
        finally:
            user_conn.close()
            task_conn.close()

    def check_email_verification_reconciliation(self):
        """检查邮箱验证的对账一致性"""
        logging.info("开始检查邮箱验证对账...")
        
        user_conn = pymysql.connect(**get_db_config(**USER_DB))
        task_conn = pymysql.connect(**get_db_config(**TASK_DB))
        
        try:
            # 获取邮箱验证用户
            with user_conn.cursor() as cursor:
                cursor.execute("SELECT uid FROM client_user WHERE email_verify = 1")
                email_verified_users = set(row['uid'] for row in cursor.fetchall())
            
            logging.info(f"邮箱验证用户总数: {len(email_verified_users)}")
            
            # 获取任务记录中的邮箱验证任务 (任务代码 10006)
            task_users = set()
            energy_users = set()
            
            with task_conn.cursor() as cursor:
                # 检查所有分片的user_task_record表
                for shard in range(1024):
                    try:
                        table_name = f"user_task_record_{shard}"
                        query = f"""
                        SELECT DISTINCT user_id 
                        FROM {table_name} 
                        WHERE task_code = '10006' AND delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            task_users.add(row['user_id'])
                    except Exception as e:
                        continue
                
                # 检查所有分片的user_ep_records表
                for shard in range(1024):
                    try:
                        table_name = f"user_ep_records_{shard}"
                        query = f"""
                        SELECT DISTINCT uer.user_id 
                        FROM {table_name} uer
                        JOIN user_task_record_{shard} utr ON uer.task_record_id = utr.id
                        WHERE utr.task_code = '10006' AND utr.delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            energy_users.add(row['user_id'])
                    except Exception as e:
                        continue
            
            logging.info(f"任务记录中邮箱验证用户数: {len(task_users)}")
            logging.info(f"能量记录中邮箱验证用户数: {len(energy_users)}")
            
            # 查找差异
            self.differences['email_verify']['missing_tasks'] = list(email_verified_users - task_users)
            self.differences['email_verify']['missing_energy'] = list(email_verified_users - energy_users)
            self.differences['email_verify']['extra_tasks'] = list(task_users - email_verified_users)
            self.differences['email_verify']['extra_energy'] = list(energy_users - email_verified_users)
            
        finally:
            user_conn.close()
            task_conn.close()

    def check_ugc_posts_reconciliation(self):
        """检查UGC内容发布的对账一致性"""
        logging.info("开始检查UGC内容发布对账...")
        
        ugc_conn = pymysql.connect(**get_db_config(**UGC_DB))
        task_conn = pymysql.connect(**get_db_config(**TASK_DB))
        
        try:
            # 获取有资格的UGC用户
            with ugc_conn.cursor() as cursor:
                query = """
                SELECT DISTINCT user_id, id, content, content_type
                FROM posts 
                WHERE status = 2 
                AND ((content_type = 4) OR (content_type = 2 AND content != '' AND content IS NOT NULL))
                AND post_type = 1
                AND is_del = 0
                """
                cursor.execute(query)
                posts = cursor.fetchall()
                
                eligible_users = set()
                for post in posts:
                    content = post['content']
                    if content and content.strip():
                        import re
                        cleaned_content = re.sub(r'#\S+', '', content)
                        cleaned_content = cleaned_content.strip()
                        
                        if cleaned_content and len(cleaned_content) >= 15:
                            eligible_users.add(post['user_id'])
            
            logging.info(f"符合UGC条件的用户总数: {len(eligible_users)}")
            
            # 获取任务记录中的UGC任务 (任务代码 10002)
            task_users = set()
            energy_users = set()
            
            with task_conn.cursor() as cursor:
                for shard in range(1024):
                    try:
                        table_name = f"user_task_record_{shard}"
                        query = f"""
                        SELECT DISTINCT user_id 
                        FROM {table_name} 
                        WHERE task_code = '10002' AND delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            task_users.add(row['user_id'])
                    except Exception as e:
                        continue
                
                for shard in range(1024):
                    try:
                        table_name = f"user_ep_records_{shard}"
                        query = f"""
                        SELECT DISTINCT uer.user_id 
                        FROM {table_name} uer
                        JOIN user_task_record_{shard} utr ON uer.task_record_id = utr.id
                        WHERE utr.task_code = '10002' AND utr.delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            energy_users.add(row['user_id'])
                    except Exception as e:
                        continue
            
            logging.info(f"任务记录中UGC用户数: {len(task_users)}")
            logging.info(f"能量记录中UGC用户数: {len(energy_users)}")
            
            # 查找差异
            self.differences['ugc_posts']['missing_tasks'] = list(eligible_users - task_users)
            self.differences['ugc_posts']['missing_energy'] = list(eligible_users - energy_users)
            self.differences['ugc_posts']['extra_tasks'] = list(task_users - eligible_users)
            self.differences['ugc_posts']['extra_energy'] = list(energy_users - eligible_users)
            
        finally:
            ugc_conn.close()
            task_conn.close()

    def check_interests_reconciliation(self):
        """检查兴趣选择的对账一致性"""
        logging.info("开始检查兴趣选择对账...")
        
        behavior_conn = pymysql.connect(**get_db_config(**BEHAVIOR_DB))
        task_conn = pymysql.connect(**get_db_config(**TASK_DB))
        
        try:
            # 获取选择了至少3个兴趣的用户
            with behavior_conn.cursor() as cursor:
                query = """
                SELECT user_id, interest_ids
                FROM user_interests
                WHERE interest_ids != '-1' AND interest_ids IS NOT NULL
                """
                cursor.execute(query)
                users_with_interests = cursor.fetchall()
                
                eligible_users = set()
                for user in users_with_interests:
                    interests = user['interest_ids'].split(',')
                    if len(interests) >= 3:
                        eligible_users.add(user['user_id'])
            
            logging.info(f"选择了至少3个兴趣的用户总数: {len(eligible_users)}")
            
            # 获取任务记录中的兴趣选择任务 (任务代码 10001)
            task_users = set()
            energy_users = set()
            
            with task_conn.cursor() as cursor:
                for shard in range(1024):
                    try:
                        table_name = f"user_task_record_{shard}"
                        query = f"""
                        SELECT DISTINCT user_id 
                        FROM {table_name} 
                        WHERE task_code = '10001' AND delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            task_users.add(row['user_id'])
                    except Exception as e:
                        continue
                
                for shard in range(1024):
                    try:
                        table_name = f"user_ep_records_{shard}"
                        query = f"""
                        SELECT DISTINCT uer.user_id 
                        FROM {table_name} uer
                        JOIN user_task_record_{shard} utr ON uer.task_record_id = utr.id
                        WHERE utr.task_code = '10001' AND utr.delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            energy_users.add(row['user_id'])
                    except Exception as e:
                        continue
            
            logging.info(f"任务记录中兴趣选择用户数: {len(task_users)}")
            logging.info(f"能量记录中兴趣选择用户数: {len(energy_users)}")
            
            # 查找差异
            self.differences['interests']['missing_tasks'] = list(eligible_users - task_users)
            self.differences['interests']['missing_energy'] = list(eligible_users - energy_users)
            self.differences['interests']['extra_tasks'] = list(task_users - eligible_users)
            self.differences['interests']['extra_energy'] = list(energy_users - eligible_users)
            
        finally:
            behavior_conn.close()
            task_conn.close()

    def check_face_invite_reconciliation(self):
        """检查人脸邀请的对账一致性"""
        logging.info("开始检查人脸邀请对账...")
        
        user_conn = pymysql.connect(**get_db_config(**USER_DB))
        task_conn = pymysql.connect(**get_db_config(**TASK_DB))
        
        try:
            # 获取成功邀请人脸验证的用户
            with user_conn.cursor() as cursor:
                query = """
                SELECT DISTINCT from_uid
                FROM user_invite_face_record 
                WHERE face_type = 2 
                AND amount_status = 2 
                AND from_uid IS NOT NULL
                """
                cursor.execute(query)
                eligible_users = set(row['from_uid'] for row in cursor.fetchall())
            
            logging.info(f"成功邀请人脸验证的用户总数: {len(eligible_users)}")
            
            # 获取任务记录中的人脸邀请任务 (任务代码 10003)
            task_users = set()
            energy_users = set()
            
            with task_conn.cursor() as cursor:
                for shard in range(1024):
                    try:
                        table_name = f"user_task_record_{shard}"
                        query = f"""
                        SELECT DISTINCT user_id 
                        FROM {table_name} 
                        WHERE task_code = '10003' AND delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            task_users.add(row['user_id'])
                    except Exception as e:
                        continue
                
                for shard in range(1024):
                    try:
                        table_name = f"user_ep_records_{shard}"
                        query = f"""
                        SELECT DISTINCT uer.user_id 
                        FROM {table_name} uer
                        JOIN user_task_record_{shard} utr ON uer.task_record_id = utr.id
                        WHERE utr.task_code = '10003' AND utr.delete_status = 0
                        """
                        cursor.execute(query)
                        for row in cursor.fetchall():
                            energy_users.add(row['user_id'])
                    except Exception as e:
                        continue
            
            logging.info(f"任务记录中人脸邀请用户数: {len(task_users)}")
            logging.info(f"能量记录中人脸邀请用户数: {len(energy_users)}")
            
            # 查找差异
            self.differences['face_invite']['missing_tasks'] = list(eligible_users - task_users)
            self.differences['face_invite']['missing_energy'] = list(eligible_users - energy_users)
            self.differences['face_invite']['extra_tasks'] = list(task_users - eligible_users)
            self.differences['face_invite']['extra_energy'] = list(energy_users - eligible_users)
            
        finally:
            user_conn.close()
            task_conn.close()

    def generate_report(self):
        """生成对账报告"""
        logging.info("生成对账报告...")
        
        report_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"\n{'='*80}")
        print(f"能量系统对账报告 - {report_time}")
        print(f"{'='*80}")
        
        for task_type, diffs in self.differences.items():
            task_names = {
                'phone_verify': '手机验证 (任务代码: 10005)',
                'email_verify': '邮箱验证 (任务代码: 10006)', 
                'ugc_posts': 'UGC内容发布 (任务代码: 10002)',
                'interests': '兴趣选择 (任务代码: 10001)',
                'face_invite': '人脸邀请 (任务代码: 10003)'
            }
            
            print(f"\n🔍 {task_names[task_type]}")
            print("-" * 60)
            
            if not any(diffs.values()):
                print("✅ 数据完全一致，无差异")
            else:
                if diffs['missing_tasks']:
                    print(f"❌ 缺少任务记录的用户数: {len(diffs['missing_tasks'])}")
                    if len(diffs['missing_tasks']) <= 10:
                        print(f"   用户ID: {diffs['missing_tasks']}")
                    else:
                        print(f"   用户ID (前10个): {diffs['missing_tasks'][:10]}")
                
                if diffs['missing_energy']:
                    print(f"❌ 缺少能量记录的用户数: {len(diffs['missing_energy'])}")
                    if len(diffs['missing_energy']) <= 10:
                        print(f"   用户ID: {diffs['missing_energy']}")
                    else:
                        print(f"   用户ID (前10个): {diffs['missing_energy'][:10]}")
                
                if diffs['extra_tasks']:
                    print(f"⚠️  多余任务记录的用户数: {len(diffs['extra_tasks'])}")
                    if len(diffs['extra_tasks']) <= 10:
                        print(f"   用户ID: {diffs['extra_tasks']}")
                    else:
                        print(f"   用户ID (前10个): {diffs['extra_tasks'][:10]}")
                
                if diffs['extra_energy']:
                    print(f"⚠️  多余能量记录的用户数: {len(diffs['extra_energy'])}")
                    if len(diffs['extra_energy']) <= 10:
                        print(f"   用户ID: {diffs['extra_energy']}")
                    else:
                        print(f"   用户ID (前10个): {diffs['extra_energy'][:10]}")
        
        # 导出详细差异数据到文件
        self.export_differences_to_file()

    def export_differences_to_file(self):
        """导出差异数据到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"reconciliation_differences_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"能量系统对账差异详细报告\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*80 + "\n\n")
            
            for task_type, diffs in self.differences.items():
                task_names = {
                    'phone_verify': '手机验证 (任务代码: 10005)',
                    'email_verify': '邮箱验证 (任务代码: 10006)', 
                    'ugc_posts': 'UGC内容发布 (任务代码: 10002)',
                    'interests': '兴趣选择 (任务代码: 10001)',
                    'face_invite': '人脸邀请 (任务代码: 10003)'
                }
                
                f.write(f"{task_names[task_type]}\n")
                f.write("-" * 60 + "\n")
                
                if diffs['missing_tasks']:
                    f.write(f"缺少任务记录的用户 ({len(diffs['missing_tasks'])}个):\n")
                    for user_id in diffs['missing_tasks']:
                        f.write(f"  {user_id}\n")
                    f.write("\n")
                
                if diffs['missing_energy']:
                    f.write(f"缺少能量记录的用户 ({len(diffs['missing_energy'])}个):\n")
                    for user_id in diffs['missing_energy']:
                        f.write(f"  {user_id}\n")
                    f.write("\n")
                
                if diffs['extra_tasks']:
                    f.write(f"多余任务记录的用户 ({len(diffs['extra_tasks'])}个):\n")
                    for user_id in diffs['extra_tasks']:
                        f.write(f"  {user_id}\n")
                    f.write("\n")
                
                if diffs['extra_energy']:
                    f.write(f"多余能量记录的用户 ({len(diffs['extra_energy'])}个):\n")
                    for user_id in diffs['extra_energy']:
                        f.write(f"  {user_id}\n")
                    f.write("\n")
                
                f.write("\n")
        
        logging.info(f"详细差异数据已导出到文件: {filename}")

def main():
    """主函数"""
    logging.info("开始执行能量系统对账检查")
    
    checker = ReconciliationChecker()
    
    # 执行各项对账检查
    checker.check_phone_verification_reconciliation()
    checker.check_email_verification_reconciliation()
    checker.check_ugc_posts_reconciliation()
    checker.check_interests_reconciliation()
    checker.check_face_invite_reconciliation()
    
    # 生成报告
    checker.generate_report()
    
    logging.info("能量系统对账检查完成")

if __name__ == "__main__":
    main() 