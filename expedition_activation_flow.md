# 启动远征任务流程图

## 流程图

```mermaid
graph TD
    A["用户进入挖矿页面"] --> B{"检查挖矿状态"}
    B -->|未激活| C["显示'启动远征'按钮"]
    B -->|已激活| D["显示当前挖矿状态"]
    C --> E["用户点击启动按钮"]
    E --> F["发送启动请求"]
    
    %% 反作弊逻辑开始
    F --> AA{"验证设备加密标识"}
    AA -->|不符合加密逻辑| AB["拒绝请求"]
    AB --> AC["显示安全提示"]
    AA -->|验证通过| AD{"检查设备占用"}
    AD -->|设备已被占用| AE["拒绝请求"]
    AE --> AF["显示冲突提示"]
    AD -->|设备可用| AG{"检查行为模式"}
    AG -->|存在异常| AH["标记可疑行为"]
    AH --> AI["额外验证流程"]
    AG -->|行为正常| G{"服务端验证"}
    %% 反作弊逻辑结束
    
    G -->|验证失败| H["显示错误信息"]
    H --> I["显示失败原因"]
    G -->|验证成功| J["记录启动数据"]
    J --> L["更新挖矿状态"]
    L --> M["返回启动结果"]
    M --> N["显示成功动画"]
    N --> O["展示挖矿已启动"]
    O --> P["更新UI状态"]
    D --> Q["显示剩余时间"]
    Q --> R["显示能量数据"]
    P --> S["开始能量计时器"]
    S --> T["定期更新显示"]
    R --> T
```


## 详细流程说明

### 1. 远征初始化
- 用户进入挖矿页面
- 系统检查用户当前远征状态（查询`user_mining_status`表）
  ```sql
  SELECT * FROM user_mining_status 
  WHERE user_id = ? AND mining_active = TRUE 
  AND NOW() BETWEEN mining_start_time AND mining_end_time
  ```
- 根据远征状态显示对应界面（启动按钮或当前状态）

### 2. 启动远征操作
- 用户点击"启动远征"按钮
- 客户端发送启动远征请求到服务端
- 请求参数包含用户ID、设备信息、时间戳等

### 3. 服务端处理
- 验证用户身份和设备信息
- 验证设备加密标识(X-Device-Id)是否符合端加密逻辑
- 检查设备是否已被其他用户用于挖矿
  ```sql
  SELECT * FROM user_mining_status 
  WHERE device_id = ? AND user_id != ? 
  AND NOW() BETWEEN mining_start_time AND mining_end_time
  ```
- 检查是否存在异常行为（如设备共享挖矿、短时间内多次切换设备）
- 验证用户是否满足启动远征条件
- 记录远征启动数据到数据库

### 4. 挖矿状态设置
- 设置远征开始时间和结束时间，更新`user_mining_status`表
  ```sql
  INSERT INTO user_mining_status 
  (user_id, device_id, mining_active, mining_start_time, mining_end_time) 
  VALUES (?, ?, TRUE, NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR))
  ```
- 记录挖矿启动日志（可选）
  ```sql
  INSERT INTO mining_activity_logs
  (user_id, device_id, action_type, action_time, session_id)
  VALUES (?, ?, 'start', NOW(), ?)
  ```

### 5. 结果返回
- 返回启动结果，包含挖矿开始时间、结束时间等信息
- 客户端展示启动成功动画
- 更新UI显示当前挖矿状态

### 6. 挖矿状态维护
- 定期更新显示剩余挖矿时间（基于`user_mining_status`表）
- 实时显示当前小时可获得的XME（基于`user_hourly_energy`表）
  ```sql
  SELECT hour_points FROM user_hourly_energy 
  WHERE user_id = ? AND hour_timestamp = CURRENT_TIMESTAMP - CURRENT_TIMESTAMP % 3600
  ```
- 显示累计获得的能量点数据（基于`user_base_energy`表）
  ```sql
  SELECT total_base_points FROM user_base_energy WHERE user_id = ?
  ```

### 7. 反作弊机制
- **设备唯一性验证**：
  - 验证X-Device-Id是否符合端加密逻辑
  - 确保同一设备在同一时间只能被一个用户用于挖矿
  
- **设备冲突检测**：
  ```sql
  SELECT * FROM user_mining_status 
  WHERE device_id = ? AND user_id != ? 
  AND NOW() BETWEEN mining_start_time AND mining_end_time
  ```
  
- **异常行为监控**：
  - 监控用户短时间内多次切换设备的行为
  - 检测多个账号使用相同设备的模式
  - 识别不合理的挖矿时间模式
  
- **地理位置验证**（可选）：
  - 检测用户设备地理位置的异常变化
  - 识别多账号在相同地理位置的异常聚集

## API接口设计

### 1. 远征状态查询接口

**请求**：
```
GET /task/v1/status
Content-Type: application/json

{
  "device_id": "设备ID"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "mining_active": false,
    "device_available": true,
    "energy_points": {
      "total_base_points": 5000,
      "today_earned": 500,
      "current_hour_points": 50
    },
    "mining_info": {
      "available_time": 24,
      "last_mining_end_time": "2025-05-29T13:38:39+08:00",
      "hourly_multiplier": 1.0
    },
    "xme_estimate": {
      "hourly_rate": 0.5,
      "daily_estimate": 12.0
    }
  },
  "success": true
}
```

### 2. 启动远征接口

**请求**：
```
POST /task/v1/start
Content-Type: application/json

{
  "device_id": "设备ID",
  "timestamp": "2025-05-30T05:38:39+00:00"
}
```

**请求头**：
```
X-Device-Id: "设备加密标识"  // 符合端加密逻辑的设备唯一标识
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "mining_started": true,
    "mining_session": {
      "mining_start_time": "2025-05-30T13:38:39+08:00",
      "mining_end_time": "2025-05-31T13:38:39+08:00",
      "session_id": "ms_789012"
    },
    "hourly_estimate": {
      "energy_per_hour": 50,
      "xme_per_hour": 0.5
    }
  },
  "success": true
}
```

**错误响应(设备冲突)**：
```
{
  "code": xxxxx,
  "message": "device_conflict",
  "result": {
    "mining_started": false,
    "error_details": {
      "reason": "device_already_in_use",
      "conflict_user_id": "***", // 脱敏处理
      "conflict_end_time": "2025-05-31T10:15:22+08:00"
    },
    "retry_suggestion": "请在冲突结束后重试，或使用其他设备"
  },
  "success": false
}
```
