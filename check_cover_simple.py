#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版的cover_url检查脚本
使用方法：
1. 修改下面的数据库配置
2. 运行: python check_cover_simple.py
"""

import pymysql
import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# ========== 配置区域 ==========
# 修改这里的数据库配置
DB_CONFIG = {
    'host': 'xme-prod-rds-contentflow.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'port': 3306,
    'user': 'ugc',
    'password': 'Ne2nbEk9',
    'database': 'ugc_hub',
    'charset': 'utf8mb4'
}

# 检查配置
BATCH_SIZE = 1000    # 每批检查的数量
MAX_WORKERS = 20     # 并发线程数
TIMEOUT = 10         # 请求超时时间
LIMIT = 5000         # 限制检查的总数量，None表示检查所有
# ============================

def get_cover_urls_from_db():
    """从数据库获取cover_url数据"""
    print("正在连接数据库...")
    try:
        connection = pymysql.connect(**DB_CONFIG)
        with connection.cursor() as cursor:
            sql = """
            SELECT id, post_id, cover_url 
            FROM post_medias 
            WHERE cover_url IS NOT NULL 
            AND cover_url != '' 
            AND is_del = 0
            ORDER BY created_at DESC
            """
            
            if LIMIT:
                sql += f" LIMIT {LIMIT}"
            
            cursor.execute(sql)
            results = cursor.fetchall()
            print(f"从数据库获取到 {len(results)} 条记录")
            return results
            
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return []
    finally:
        if 'connection' in locals():
            connection.close()

def check_url(url):
    """检查单个URL是否存在"""
    try:
        response = requests.head(url, timeout=TIMEOUT, allow_redirects=True)
        return response.status_code == 200
    except:
        return False

def check_urls_batch(records):
    """批量检查URL"""
    results = []
    
    def check_record(record):
        record_id, post_id, cover_url = record
        exists = check_url(cover_url)
        return {
            'id': record_id,
            'post_id': post_id,
            'cover_url': cover_url,
            'exists': exists
        }
    
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = [executor.submit(check_record, record) for record in records]
        
        for i, future in enumerate(as_completed(futures)):
            try:
                result = future.result()
                results.append(result)
                
                # 显示进度
                if (i + 1) % 100 == 0:
                    print(f"已检查 {i + 1}/{len(records)} 个URL")
                    
            except Exception as e:
                print(f"检查失败: {e}")
    
    return results

def save_invalid_urls(results):
    """保存无效的URL到文件"""
    invalid_urls = [r for r in results if not r['exists']]
    
    if not invalid_urls:
        print("所有URL都是有效的！")
        return
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"invalid_cover_urls_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"无效的cover_url列表 (共{len(invalid_urls)}个)\n")
        f.write("="*50 + "\n")
        for item in invalid_urls:
            f.write(f"ID: {item['id']}, POST_ID: {item['post_id']}\n")
            f.write(f"URL: {item['cover_url']}\n")
            f.write("-"*50 + "\n")
    
    print(f"无效URL已保存到: {filename}")
    return invalid_urls

def main():
    """主函数"""
    print("开始检查cover_url...")
    start_time = time.time()
    
    # 1. 从数据库获取数据
    records = get_cover_urls_from_db()
    if not records:
        print("没有数据需要检查")
        return
    
    # 2. 检查URL
    print(f"开始检查 {len(records)} 个URL...")
    results = check_urls_batch(records)
    
    # 3. 统计结果
    total = len(results)
    valid_count = sum(1 for r in results if r['exists'])
    invalid_count = total - valid_count
    
    print("\n" + "="*50)
    print("检查完成！")
    print(f"总共检查: {total} 个URL")
    print(f"有效URL: {valid_count} 个")
    print(f"无效URL: {invalid_count} 个")
    print(f"有效率: {(valid_count/total*100):.2f}%")
    print(f"耗时: {time.time() - start_time:.2f} 秒")
    
    # 4. 保存无效URL
    if invalid_count > 0:
        invalid_urls = save_invalid_urls(results)
        print(f"\n发现 {len(invalid_urls)} 个无效URL，详情请查看输出文件")

if __name__ == "__main__":
    main() 