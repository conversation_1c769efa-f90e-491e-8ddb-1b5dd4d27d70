codes = [
    "<PERSON><PERSON><PERSON><PERSON>",
    "Y35<PERSON><PERSON>",
    "9RDHF<PERSON>",
    "5GOWI1",  # This one is highlighted in yellow in the image
    "XLPOZ8",
    "MUARG<PERSON>",
    "2LFGYK",
    "7IZUSQ",
    "BSYIE3",
    "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "JRV<PERSON>O",
    "98W3WU",
    "P50PNT",
    "BIXMEG",
    "1NJTOO",
    "PGSALF",
    "UPM1XY",
    "3AVJS1",
    "DRM1NB",
    "L9Y97O",
    "HMP9H",
    "FLRLPP",
    "6HOWI2",
    "1O1OKW",
    "X9PEZ3",
    "1JC4O2",
    "JVZ046",
    "NLBSVI",
    "KODKE1",
    "DBE10F",
    "R8HXRO",
    "8I9XP4",
    "LJQBLT",
    "0CT108"
]

# Example of how to use this array
if __name__ == "__main__":
    print(f"Total codes: {len(codes)}")
    print(f"Highlighted code: {codes[3]}")  # 5GOWI1 is highlighted in the image
    
    # Print all codes
    for i, code in enumerate(codes):
        print(f"{i+1}. {code}")
