#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查post_medias表中所有URL字段的脚本
检查 url 和 cover_url 两个字段
"""

import pymysql
import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# ========== 配置区域 ==========
# 修改这里的数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'password',
    'database': 'your_database',
    'charset': 'utf8mb4'
}

# 检查配置
MAX_WORKERS = 20     # 并发线程数
TIMEOUT = 10         # 请求超时时间
LIMIT = 5000         # 限制检查的总数量，None表示检查所有
# ============================

def get_urls_from_db():
    """从数据库获取所有URL数据"""
    print("正在连接数据库...")
    try:
        connection = pymysql.connect(**DB_CONFIG)
        with connection.cursor() as cursor:
            sql = """
            SELECT id, post_id, url, cover_url, type, status
            FROM post_medias 
            WHERE is_del = 0
            ORDER BY created_at DESC
            """
            
            if LIMIT:
                sql += f" LIMIT {LIMIT}"
            
            cursor.execute(sql)
            results = cursor.fetchall()
            print(f"从数据库获取到 {len(results)} 条记录")
            return results
            
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return []
    finally:
        if 'connection' in locals():
            connection.close()

def check_url(url):
    """检查单个URL是否存在"""
    if not url or url.strip() == '':
        return False, "Empty URL"
    
    try:
        response = requests.head(url, timeout=TIMEOUT, allow_redirects=True)
        if response.status_code == 200:
            return True, "OK"
        else:
            return False, f"HTTP {response.status_code}"
    except requests.exceptions.Timeout:
        return False, "Timeout"
    except requests.exceptions.ConnectionError:
        return False, "Connection Error"
    except Exception as e:
        return False, f"Error: {str(e)}"

def check_record_urls(record):
    """检查单条记录的所有URL"""
    record_id, post_id, url, cover_url, media_type, status = record
    
    # 检查主URL
    url_exists = False
    url_message = "Empty URL"
    if url and url.strip():
        url_exists, url_message = check_url(url)
    
    # 检查封面URL
    cover_exists = False
    cover_message = "Empty URL"
    if cover_url and cover_url.strip():
        cover_exists, cover_message = check_url(cover_url)
    
    return {
        'id': record_id,
        'post_id': post_id,
        'type': media_type,
        'status': status,
        'url': url,
        'url_exists': url_exists,
        'url_message': url_message,
        'cover_url': cover_url,
        'cover_exists': cover_exists,
        'cover_message': cover_message
    }

def check_all_urls(records):
    """批量检查所有URL"""
    results = []
    
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = [executor.submit(check_record_urls, record) for record in records]
        
        for i, future in enumerate(as_completed(futures)):
            try:
                result = future.result()
                results.append(result)
                
                # 显示进度
                if (i + 1) % 100 == 0:
                    print(f"已检查 {i + 1}/{len(records)} 条记录")
                    
            except Exception as e:
                print(f"检查失败: {e}")
    
    return results

def save_results(results):
    """保存检查结果到文件"""
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # 统计数据
    total = len(results)
    url_valid = sum(1 for r in results if r['url_exists'])
    cover_valid = sum(1 for r in results if r['cover_exists'])
    
    # 找出有问题的记录
    invalid_records = []
    for r in results:
        issues = []
        if r['url'] and not r['url_exists']:
            issues.append(f"主URL无效: {r['url_message']}")
        if r['cover_url'] and not r['cover_exists']:
            issues.append(f"封面URL无效: {r['cover_message']}")
        
        if issues:
            invalid_records.append({**r, 'issues': issues})
    
    # 保存详细结果
    filename = f"url_check_results_{timestamp}.txt"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"URL检查结果报告\n")
        f.write(f"检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("="*60 + "\n\n")
        
        f.write(f"总计检查: {total} 条记录\n")
        f.write(f"主URL有效: {url_valid} 个\n")
        f.write(f"封面URL有效: {cover_valid} 个\n")
        f.write(f"有问题的记录: {len(invalid_records)} 个\n\n")
        
        if invalid_records:
            f.write("有问题的记录详情:\n")
            f.write("-"*60 + "\n")
            for record in invalid_records:
                f.write(f"ID: {record['id']}, POST_ID: {record['post_id']}, TYPE: {record['type']}\n")
                f.write(f"主URL: {record['url'] or 'N/A'}\n")
                f.write(f"封面URL: {record['cover_url'] or 'N/A'}\n")
                for issue in record['issues']:
                    f.write(f"  ❌ {issue}\n")
                f.write("-"*60 + "\n")
    
    print(f"详细结果已保存到: {filename}")
    
    # 保存CSV格式
    csv_filename = f"url_check_results_{timestamp}.csv"
    with open(csv_filename, 'w', encoding='utf-8') as f:
        f.write("id,post_id,type,status,url,url_exists,url_message,cover_url,cover_exists,cover_message\n")
        for r in results:
            f.write(f"{r['id']},{r['post_id']},{r['type']},{r['status']},")
            f.write(f'"{r['url'] or ''}",{r['url_exists']},"{r['url_message']}",')
            f.write(f'"{r['cover_url'] or ''}",{r['cover_exists']},"{r['cover_message']}"\n')
    
    print(f"CSV结果已保存到: {csv_filename}")
    return invalid_records

def main():
    """主函数"""
    print("开始检查所有URL...")
    start_time = time.time()
    
    # 1. 从数据库获取数据
    records = get_urls_from_db()
    if not records:
        print("没有数据需要检查")
        return
    
    # 2. 检查所有URL
    print(f"开始检查 {len(records)} 条记录的URL...")
    results = check_all_urls(records)
    
    # 3. 统计结果
    total = len(results)
    url_total = sum(1 for r in results if r['url'])
    url_valid = sum(1 for r in results if r['url_exists'])
    cover_total = sum(1 for r in results if r['cover_url'])
    cover_valid = sum(1 for r in results if r['cover_exists'])
    
    print("\n" + "="*60)
    print("检查完成！")
    print(f"总记录数: {total}")
    print(f"主URL:")
    print(f"  - 有URL的记录: {url_total}")
    print(f"  - 有效URL: {url_valid}")
    print(f"  - 有效率: {(url_valid/url_total*100):.2f}%" if url_total > 0 else "  - 有效率: N/A")
    print(f"封面URL:")
    print(f"  - 有封面URL的记录: {cover_total}")
    print(f"  - 有效封面URL: {cover_valid}")
    print(f"  - 有效率: {(cover_valid/cover_total*100):.2f}%" if cover_total > 0 else "  - 有效率: N/A")
    print(f"耗时: {time.time() - start_time:.2f} 秒")
    
    # 4. 保存结果
    invalid_records = save_results(results)
    if invalid_records:
        print(f"\n发现 {len(invalid_records)} 条记录有问题，详情请查看输出文件")

if __name__ == "__main__":
    main() 