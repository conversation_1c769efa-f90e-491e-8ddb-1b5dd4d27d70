# XME挖矿系统任务调度流程图

## 定时任务文字描述

### 1. 小时能量衰减任务

**执行时间**：每小时的第1分钟（`0 1 * * * ?`）

**执行方式**：分片广播，按用户ID分片

**主要功能**：
- 对上一小时的能量点应用衰减规则
- 为当前小时准备初始能量点

**表操作流程**：
1. 读取：`user_hourly_energy`表中上一小时的用户能量记录
2. 计算：应用衰减规则（默认衰减20%，最低保留30%）
3. 写入：创建`energy_points_records`表中的衰减记录（负值）
4. 准备：为当前小时的能量汇总准备衰减后的数据（不直接更新表）

**数据流向**：
- 输入：上一小时的用户能量点（`user_hourly_energy`表）
- 输出：衰减记录（`energy_points_records`表）和内存中的衰减后数据

**说明** 在衰减任务和上个小时的服务端gap时间的衰减处理流程：
如果用户有上一小时的能量，应用衰减规则后得到当前小时的初始能量
如果衰减后的能量为0，则不需要创建新的小时能量记录，衰减任务只是确保energy_points_records的准确性。


### 2. 小时能量汇总任务（确保数据一致性，捕获可能的漏更新情况）

**执行时间**：每小时的第2分钟（`0 2 * * * ?`）

**执行方式**：分片广播，按用户ID分片

**主要功能**：
- 汇总用户当前小时的各来源能量点
- 创建当前小时的能量点快照

**表操作流程**：
1. 读取：
   - `energy_points_records`表中当前小时的所有能量记录（包括衰减记录和新增能量记录）
   - `user_mining_status`表中的用户挖矿状态
2. 计算：汇总当前小时的所有能量来源
3. 写入：
   - 更新`user_hourly_energy`表，创建或更新当前小时的能量记录
   

**数据流向**：
- 输入：`energy_points_records`表中当前小时的所有能量记录
- 输出：当前小时的能量点快照（`user_hourly_energy`表）


### 3. 好友能量加成任务

**执行时间**：每5分钟执行一次（`0 */5 * * * ?`）

**执行方式**：分片广播，按用户ID分片

**主要功能**：
- 计算好友能量加成（默认为好友能量的10%）
- 更新用户当前小时的能量点

**表操作流程**：
1. 读取：
   - `user_mining_status`表中的活跃用户列表
   - `user_invitations`表中的用户邀请关系
   - `user_hourly_energy`表中好友的当前小时能量点
   - `energy_points_records`表中当前小时已经计算过的好友加成记录
2. 计算：
   - 判断当前5分钟区间内好友新增的能量点（通过比较上次计算时的能量点）
   - 计算新增能量的加成值（新增好友能量 × 加成比例）
3. 写入：
   - 更新`user_hourly_energy`表中的用户当前小时能量点（增加新计算的加成值）
   - 创建`energy_points_records`表中的加成记录（包含加成时间戳和来源信息）

**重复计算防止**：
- 使用时间戳和来源标记：每次加成记录包含计算时间和好友能量来源
- 增量计算：只计算上次加成后好友新增的能量点
- 并发控制：使用分布式锁确保同一用户的加成不会并发计算

**数据流向**：
- 输入：活跃用户列表、用户邀请关系、好友能量点、已计算的加成记录
- 输出：更新后的用户当前小时能量点、新的能量加成记录

### 4. XME结算任务（确保好友加成执行完毕）

**执行时间**：每小时的第5分钟（`0 5 * * * ?`）

**执行方式**：分片广播，按用户ID分片

**主要功能**：
- 将用户的基础能量和累积小时能量转换为XME
- 更新用户XME余额
- 创建XME交易记录

**表操作流程**：
1. 读取：
   - `user_hourly_energy`表中的用户当前小时能量汇总
   - `user_base_energy`表中的用户基础能量
   - `user_mining_status`表中的用户挖矿状态
   - 系统配置中的XME转换比率
2. 计算：
   - 总能量 = 基础能量 + 小时能量
   - 应得XME = 总能量 × 转换比率
3. 写入：
   - 计算用户能量给point服务发送kafka消息

**数据流向**：
- 输入：用户基础能量、当前小时能量汇总、用户挖矿状态、XME转换比率
- 输出：kafka消息记录
