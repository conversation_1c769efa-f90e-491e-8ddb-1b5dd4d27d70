@startuml 社交App提币功能架构图

!define RECTANGLE class
!define COMPONENT component
!define DATABASE database
!define QUEUE queue

title 社交App提币功能轻量化架构图

' 定义颜色
!define PRIMARY_COLOR #4A90E2
!define SECONDARY_COLOR #7ED321
!define WARNING_COLOR #F5A623
!define DANGER_COLOR #D0021B
!define NEUTRAL_COLOR #9B9B9B

' 客户端层
package "客户端层" as ClientLayer {
    RECTANGLE "移动端App" as MobileApp PRIMARY_COLOR
    RECTANGLE "Web端" as WebApp PRIMARY_COLOR
    RECTANGLE "管理后台" as AdminPanel WARNING_COLOR
}

' API网关层
package "网关层" as GatewayLayer {
    COMPONENT "API网关" as APIGateway SECONDARY_COLOR {
        - 认证鉴权
        - 限流熔断
        - 请求路由
        - 日志记录
    }
}

' 业务服务层
package "业务服务层" as BusinessLayer {
    
    package "提币相关服务" as WithdrawalServices {
        COMPONENT "提币服务" as WithdrawalService PRIMARY_COLOR {
            - 提币申请接收
            - 订单创建管理
            - 状态跟踪更新
            - 用户通知
        }
        
        COMPONENT "基础风控服务" as RiskService WARNING_COLOR {
            - 基础限额检查
            - 频率限制
            - 黑名单检查
            - 异常监控
        }
        
        COMPONENT "交易所对接服务" as ExchangeService SECONDARY_COLOR {
            - 用户绑定管理
            - 订单状态同步
            - 回调处理
            - 错误处理重试
        }
        
        COMPONENT "用户绑定服务" as UserBindingService NEUTRAL_COLOR {
            - 绑定流程管理
            - KYC状态同步
            - 绑定关系维护
        }
    }
    
    package "现有服务" as ExistingServices {
        COMPONENT "Point服务" as PointService PRIMARY_COLOR {
            - 余额管理
            - 资产冻结
            - 交易记录
            - 流水记录
        }
        
        COMPONENT "用户服务" as UserService PRIMARY_COLOR {
            - 用户信息
            - 认证授权
            - 权限管理
        }
        
        COMPONENT "挖矿服务" as MiningService PRIMARY_COLOR {
            - XME挖矿
            - 能量点管理
            - 奖励发放
        }
    }
}

' 基础设施层
package "基础设施层" as InfrastructureLayer {
    DATABASE "MySQL集群" as MySQL PRIMARY_COLOR {
        - 用户数据
        - 订单数据
        - 绑定关系
        - 风控记录
    }
    
    DATABASE "Redis集群" as Redis SECONDARY_COLOR {
        - 缓存数据
        - 会话信息
        - 限流计数
        - 临时数据
    }
    
    QUEUE "Kafka集群" as Kafka WARNING_COLOR {
        - 异步消息
        - 状态同步
        - 事件通知
        - 日志收集
    }
}

' 外部服务层
package "外部服务" as ExternalServices {
    COMPONENT "三方交易所" as Exchange DANGER_COLOR {
        **支付中心角色**
        - KYC认证
        - 合规检查
        - 钱包管理
        - 资金托管
        - 实际转账
        - 区块链交易
    }
    
    COMPONENT "监控告警" as Monitoring NEUTRAL_COLOR {
        - Prometheus
        - Grafana
        - 钉钉告警
        - 邮件通知
    }
}

' 连接关系
ClientLayer --> APIGateway : HTTPS请求

APIGateway --> WithdrawalService : 提币相关请求
APIGateway --> PointService : 余额查询
APIGateway --> UserService : 用户认证

WithdrawalService --> RiskService : 风控检查
WithdrawalService --> ExchangeService : 订单提交
WithdrawalService --> UserBindingService : 绑定验证
WithdrawalService --> PointService : 资产操作

RiskService --> Redis : 限流计数
RiskService --> MySQL : 风控记录

ExchangeService --> Exchange : API调用
ExchangeService --> Kafka : 状态消息

UserBindingService --> Exchange : 绑定验证
UserBindingService --> MySQL : 绑定关系

PointService --> MySQL : 数据持久化
PointService --> Kafka : 交易消息

UserService --> MySQL : 用户数据
UserService --> Redis : 会话缓存

Exchange --> ExchangeService : 状态回调
Exchange --> UserBindingService : 绑定回调

Kafka --> WithdrawalService : 消息消费
Kafka --> Monitoring : 日志收集

' 数据流说明
note right of WithdrawalService
  **轻量化设计核心**
  1. 我们只做订单管理
  2. 复杂业务交给交易所
  3. 专注用户体验
  4. 快速响应需求
end note

note right of Exchange
  **交易所承担重责**
  1. 用户KYC认证
  2. 合规风控检查
  3. 钱包私钥管理
  4. 区块链交易执行
  5. 资金安全保障
end note

note bottom of RiskService
  **基础风控策略**
  - 限额控制
  - 频率限制
  - 黑名单检查
  - 简单异常检测
end note

@enduml
