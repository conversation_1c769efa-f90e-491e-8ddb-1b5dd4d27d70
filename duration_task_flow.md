# 时长任务流程设计文档

## 概述

时长任务是XME挖矿系统中的一种特殊任务类型，用户通过在应用内完成特定时长的活动（如观看内容、阅读文章等）来获取小时能量点奖励。时长任务根据用户参与的时间长短提供阶梯式奖励，鼓励用户增加应用使用时长。这些奖励直接计入用户的小时能量点，而非基础能量点。

## 流程图

```mermaid
graph TD
    A["用户进入内容页面"] --> B["系统检查时长任务状态"]
    B --> C["显示当前时长进度和奖励"]
    C --> D["用户开始内容消费"]
    D --> E["系统开始计时"]
    
    %% 心跳检测机制
    E --> F["发送心跳包确认活跃状态"]
    F --> G{"检查用户是否活跃"}
    G -->|活跃| H["累计有效时长"]
    G -->|不活跃| I["暂停计时"]
    I --> J["等待用户重新活跃"]
    J --> F
    
    %% 奖励发放
    H --> K{"是否达到奖励阈值"}
    K -->|否| F
    K -->|是| L["触发奖励发放"]
    L --> M["更新UI显示已获得奖励"]
    M --> N["继续计时"]
    N --> F
    
    %% 任务完成
    H --> O{"是否达到最大时长"}
    O -->|否| F
    O -->|是| P["标记任务完成"]
    P --> Q["显示任务完成提示"]
    Q --> R["记录任务完成数据"]
```

## 详细流程说明

### 1. 任务初始化
- 用户进入内容消费页面（如视频、文章等）
- 系统检查用户当前的时长任务状态
- 显示已累计时长和下一阶段奖励信息

### 2. 时长计算
- 用户开始消费内容时启动计时器
- 客户端维护计时器，记录用户有效观看时长
- 定期（每10-30秒）向服务器发送心跳包，上报累计时长
- 客户端负责检测用户活跃状态，可通过以下方式：
  - 内容滚动事件
  - 屏幕触摸事件
  - 视频播放进度变化

### 3. 反作弊机制（本期不做）
- **活跃度验证**：
  ```sql
  -- 记录用户活跃事件
  INSERT INTO user_activity_events 
  (user_id, task_id, content_id, event_type, timestamp, client_data)
  VALUES (?, ?, ?, 'content_view', NOW(), ?)
  ```
  
- **异常行为检测**：
  - 检测不自然的滚动模式
  - 识别机器人行为
  - 监控多设备同时在线
  
- **时长验证**： （本期不做）
  ```sql
  -- 验证累计时长是否合理
  SELECT COUNT(*) FROM user_activity_events
  WHERE user_id = ? AND event_type = 'content_view'
  AND timestamp BETWEEN ? AND ?
  AND TIMESTAMPDIFF(SECOND, LAG(timestamp) OVER (ORDER BY timestamp), timestamp) <= 15
  ```

### 4. 奖励发放
- 根据配置的阶梯式奖励规则发放小时能量点
- 每达到一个时长阈值，触发一次奖励
- 奖励直接计入用户小时能量点，无需手动领取
- 更新用户小时能量点：
  ```sql
  INSERT INTO user_hourly_energy 
  (user_id, hour_timestamp, hour_points, last_updated) 
  VALUES (?, CURRENT_TIMESTAMP - CURRENT_TIMESTAMP % 3600, ?, NOW())
  ON DUPLICATE KEY UPDATE 
  hour_points = hour_points + ?, 
  last_updated = NOW()
  ```
  
- 创建任务完成记录：
  ```sql
  INSERT INTO user_task_completions 
  (user_id, task_id, completion_time, points_awarded, is_claimed, claim_time, duration_seconds) 
  VALUES (?, ?, NOW(), ?, TRUE, NOW(), ?)
  ```
  
- 记录能量点流水：
  ```sql
  INSERT INTO energy_points_records 
  (user_id, points_type, points_amount, source_type, source_id, task_id, hour_timestamp) 
  VALUES (?, 'hourly', ?, 'duration_task', ?, ?, CURRENT_TIMESTAMP - CURRENT_TIMESTAMP % 3600)
  ```

### 5. 任务完成
- 用户达到最大奖励时长后，标记任务完成
- 显示任务完成提示
- 更新任务状态和每日限额

## 奖励配置

| 时长阈值 | 能量点奖励 | 累计奖励 |
|---------|-----------|---------|
| 60秒    | 100点     | 100点   |
| 5分钟   | 200点     | 300点   |
| 10分钟  | 200点     | 500点   |
| 20分钟  | 300点     | 800点   |
| 45分钟  | 300点     | 1100点  |
| 90分钟  | 900点     | 2000点  |

## API接口设计

### 1. 获取时长任务状态

**请求**：
```
GET /task/v1/duration/status
Content-Type: application/json

{
  "task_id": "12345"     // 任务ID
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "task_active": true,
    "current_duration": 180,  // 当前已累计秒数
    "next_threshold": 300,    // 下一个奖励阈值（秒）
    "next_reward": 200,       // 下一阶段奖励点数
    "max_threshold": 5400,    // 最大奖励阈值（秒）
    "rewards_earned": [       // 已获得的奖励
      {
        "threshold": 60,
        "points": 100,
        "earned_at": "2025-05-30T14:30:22+08:00"
      }
    ],
    "daily_limit": {
      "used": 1,
      "total": 3,
      "reset_at": "2025-05-31T00:00:00+08:00"
    }
  },
  "success": true
}
```

### 2. 记录活跃心跳

**请求**：
```
POST /task/v1/duration/heartbeat
Content-Type: application/json

{
  "task_id": "12345",
  "duration": 185        // 累计观看秒数
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "heartbeat_received": true,
    "current_duration": 185,
    "valid_duration": 185,     // 服务器验证后的有效时长
    "reward_triggered": false
  },
  "success": true
}
```

### 3. 奖励触发通知

**响应**（当心跳导致奖励触发时）：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "heartbeat_received": true,
    "current_duration": 300,
    "valid_duration": 300,     // 服务器验证后的有效时长
    "reward_triggered": true,
    "reward_details": {
      "threshold_reached": 300,
      "points_awarded": 200,
      "total_points_earned": 300,
      "task_completion_id": 67890
    }
  },
  "success": true
}
```

## 客户端实现建议

1. **时长计算**：
   - 客户端维护本地计时器
   - 定期（每10-30秒）发送心跳包，直接上报累计时长
   - 在用户切换应用或页面时暂停计时

2. **UI展示**：
   - 显示进度条指示当前时长和下一阶段
   - 达到奖励阈值时显示动画效果
   - 在内容页面保持时长任务状态可见
   - 客户端自行维护计时器，定期上报累计时长

3. **离线处理**：
   - 本地记录时长数据
   - 网络恢复后同步到服务器
   - 处理时区和时间差异

## 数据库设计

## 性能优化建议

1. **减少数据库写入**：
   - 降低心跳频率（每10-30秒一次）
- 客户端直接发送累计时长，减少服务器计算
- 只在关键节点（如达到阈值）更新数据库

2. **使用缓存**：
   - 缓存任务配置和用户进度
   - 使用Redis记录实时时长数据
   - 定期将缓存数据同步到数据库

3. **异步处理**：
   - 心跳处理和验证使用异步队列
   - 奖励发放使用事件驱动模式
   - 非关键数据使用延迟写入

## 安全考虑

1. **防止时间篡改**：
   - 使用服务器时间作为权威时间源
   - 验证客户端时间戳的合理性
   - 记录设备时区信息

2. **防止脚本模拟**：
   - 要求不规则的用户交互
   - 分析用户行为模式识别自动化脚本
   - 实施验证码或简单任务验证

3. **账号安全**：
   - 限制单个账号的每日任务次数
   - 监控异常的任务完成模式
   - 对可疑账号实施额外验证
