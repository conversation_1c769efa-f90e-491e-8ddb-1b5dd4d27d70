# Twitter分享任务流程设计

## 流程概述

Twitter分享任务是XME挖矿系统中的一种社交分享任务，允许用户通过分享内容到Twitter获取能量点奖励。本文档详细说明了该任务的流程设计和技术实现方案。

## 详细流程设计

### 1. 用户发起分享任务
- 客户端展示分享预览和奖励说明
- 用户点击"分享到Twitter"按钮

### 2. 授权处理
- 客户端发送：`POST /task/v1/twitter/authorize`
- 服务端返回授权URL
- 客户端打开WebView进行授权
- 授权完成后回传授权码

### 3. 服务端执行分享
- 客户端发送：`POST /task/v1/twitter/share`
- 请求参数：`{task_id, auth_code, content_params}`
- 服务端代理调用Twitter API发布推文
- 服务端返回：`{success, tweet_id}`

### 4. 任务完成与奖励
- 服务端验证推文发布成功
- 自动标记任务完成
- 计算并发放能量点奖励
- 客户端显示任务完成和奖励信息

## API接口设计

### 1. 授权初始化接口

**请求**：
```
POST /task/v1/twitter/authorize
Content-Type: application/json

{
  "task_id": "任务ID"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "auth_url": "https://twitter.com/oauth/authorize?...",
    "request_id": "授权请求ID"
  },
  "success": true
}
```

### 2. 分享执行接口

**请求**：
```
POST /task/v1/twitter/share
Content-Type: application/json

{
  "task_id": "任务ID",
  "auth_code": "授权码",
  "content_params": {
    "text": "分享文本",
    "image_url": "可选图片URL",
    "hashtags": ["标签1", "标签2"]
  }
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "tweet_id": "123456789",
    "tracking_id": "分享跟踪ID"
  },
  "success": true
}
```

