# 好友能量加成任务流程

```mermaid
sequenceDiagram
    participant Job as XXL-Job(每5分钟)
    participant BonusTask as 好友加成任务
    participant Lock as 分布式锁
    participant DB as 数据库

    Job->>BonusTask: 触发任务
    BonusTask->>Lock: 获取分布式锁
    
    alt 获取锁失败
        Lock-->>BonusTask: 返回锁定失败
        BonusTask-->>Job: 任务结束
    else 获取锁成功
        BonusTask->>DB: 读取活跃用户(user_mining_status)
        BonusTask->>DB: 读取邀请关系(user_invitations)
        
        loop 每个活跃用户
            BonusTask->>DB: 读取好友当前小时能量(user_hourly_energy)
            BonusTask->>DB: 读取已计算加成记录(energy_points_records)
            
            alt 有新增能量
                BonusTask->>DB: 开始事务
                BonusTask->>DB: 更新user_hourly_energy
                BonusTask->>DB: 创建加成记录(energy_points_records)
                BonusTask->>DB: 提交事务
            end
        end
        
        BonusTask->>Lock: 释放分布式锁
        BonusTask-->>Job: 任务完成
    end

```

## 关键点说明

1. **增量计算**：
   ```sql
   -- 查询已计算的加成记录
    条件：
    1. hour_timestamp = ?
    2. source = 'friend_bonus'
   
   
   -- 获取好友新增能量
   SELECT energy_points 
   FROM user_hourly_energy 
   WHERE user_id = ? 
   AND hour_timestamp = ？
   AND update_time > ?  -- last_bonus_time
   ```

2. **写入流程**：
   ```sql
   -- 事务开始
   START TRANSACTION;
   
   -- 1. 更新小时能量
   UPDATE user_hourly_energy 
   SET energy_points = energy_points + ?
   WHERE user_id = ? AND hour_key = ?;
   
   -- 2. 创建加成记录
   INSERT INTO energy_points_records
   (user_id, hour_key, points, source, create_time)
   VALUES (?, ?, ?, 'friend_bonus', NOW());
   
   COMMIT;
   ```
