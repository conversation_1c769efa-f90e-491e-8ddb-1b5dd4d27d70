#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手机验证用户对账调试脚本
专门用于分析手机验证用户与任务记录、能量记录之间的差异
"""

import pymysql
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"phone_verification_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)

# Database configurations - 生产环境
USER_DB = {
    'host': 'xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user'
}

TASK_DB = {
    'host': 'xme-prod-media-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task'
}

def get_db_config(**kwargs):
    """准备数据库配置"""
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

def debug_phone_verification_differences():
    """详细分析手机验证用户差异"""
    print("🔍 开始详细分析手机验证用户差异...")
    print("=" * 80)
    
    # 连接数据库
    user_conn = pymysql.connect(**get_db_config(**USER_DB))
    task_conn = pymysql.connect(**get_db_config(**TASK_DB))
    
    try:
        # 1. 获取所有手机验证用户
        print("📱 正在获取手机验证用户...")
        with user_conn.cursor() as cursor:
            cursor.execute("SELECT uid FROM client_user WHERE phone_verify = 1")
            phone_verified_users = set(row['uid'] for row in cursor.fetchall())
        
        print(f"✅ 手机验证用户总数: {len(phone_verified_users)}")
        
        # 2. 获取任务记录中的手机验证用户
        print("📋 正在获取任务记录中的手机验证用户...")
        task_users = set()
        task_user_details = []
        
        with task_conn.cursor() as cursor:
            for shard in range(1024):
                try:
                    table_name = f"user_task_record_{shard}"
                    query = f"""
                    SELECT user_id, id, created_time, updated_time
                    FROM {table_name} 
                    WHERE task_code = '10005' AND delete_status = 0
                    """
                    cursor.execute(query)
                    records = cursor.fetchall()
                    
                    for record in records:
                        task_users.add(record['user_id'])
                        task_user_details.append({
                            'user_id': record['user_id'],
                            'task_id': record['id'],
                            'shard': shard,
                            'created_time': record['created_time'],
                            'updated_time': record['updated_time']
                        })
                        
                except Exception as e:
                    # 表不存在或其他错误，跳过
                    continue
        
        print(f"✅ 任务记录中手机验证用户数: {len(task_users)}")
        
        # 3. 获取能量记录中的手机验证用户
        print("⚡ 正在获取能量记录中的手机验证用户...")
        energy_users = set()
        energy_user_details = []
        
        with task_conn.cursor() as cursor:
            for shard in range(1024):
                try:
                    table_name = f"user_ep_records_{shard}"
                    query = f"""
                    SELECT uer.user_id, uer.id as energy_id, uer.task_record_id, 
                           uer.created_time, uer.updated_time, utr.id as task_id
                    FROM {table_name} uer
                    JOIN user_task_record_{shard} utr ON uer.task_record_id = utr.id
                    WHERE utr.task_code = '10005' AND utr.delete_status = 0
                    """
                    cursor.execute(query)
                    records = cursor.fetchall()
                    
                    for record in records:
                        energy_users.add(record['user_id'])
                        energy_user_details.append({
                            'user_id': record['user_id'],
                            'energy_id': record['energy_id'],
                            'task_record_id': record['task_record_id'],
                            'task_id': record['task_id'],
                            'shard': shard,
                            'created_time': record['created_time'],
                            'updated_time': record['updated_time']
                        })
                        
                except Exception as e:
                    # 表不存在或其他错误，跳过
                    continue
        
        print(f"✅ 能量记录中手机验证用户数: {len(energy_users)}")
        
        # 4. 分析差异
        print("\n" + "=" * 80)
        print("📊 差异分析结果")
        print("=" * 80)
        
        # 找出各种差异情况
        missing_in_tasks = phone_verified_users - task_users
        missing_in_energy = phone_verified_users - energy_users
        extra_in_tasks = task_users - phone_verified_users
        extra_in_energy = energy_users - phone_verified_users
        
        # 任务记录和能量记录不一致的情况
        task_energy_diff_missing_energy = task_users - energy_users
        task_energy_diff_extra_energy = energy_users - task_users
        
        print(f"❌ 已验证手机但缺少任务记录的用户数: {len(missing_in_tasks)}")
        print(f"❌ 已验证手机但缺少能量记录的用户数: {len(missing_in_energy)}")
        print(f"⚠️  有任务记录但未验证手机的用户数: {len(extra_in_tasks)}")
        print(f"⚠️  有能量记录但未验证手机的用户数: {len(extra_in_energy)}")
        print(f"🔄 有任务记录但缺少能量记录的用户数: {len(task_energy_diff_missing_energy)}")
        print(f"🔄 有能量记录但缺少任务记录的用户数: {len(task_energy_diff_extra_energy)}")
        
        # 5. 输出详细差异数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 输出缺少任务记录的用户
        if missing_in_tasks:
            filename = f"missing_task_records_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"已验证手机但缺少任务记录的用户 (共{len(missing_in_tasks)}个)\n")
                f.write("=" * 60 + "\n")
                for user_id in sorted(missing_in_tasks):
                    f.write(f"{user_id}\n")
            print(f"📝 已验证手机但缺少任务记录的用户ID已保存到: {filename}")
            
            # 显示前20个
            print("📋 缺少任务记录的用户ID (前20个):")
            for user_id in sorted(list(missing_in_tasks))[:20]:
                print(f"   {user_id}")
            if len(missing_in_tasks) > 20:
                print(f"   ... 还有{len(missing_in_tasks) - 20}个用户")
        
        # 输出多余任务记录的用户
        if extra_in_tasks:
            filename = f"extra_task_records_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"有任务记录但未验证手机的用户 (共{len(extra_in_tasks)}个)\n")
                f.write("=" * 60 + "\n")
                for user_id in sorted(extra_in_tasks):
                    f.write(f"{user_id}\n")
            print(f"📝 有任务记录但未验证手机的用户ID已保存到: {filename}")
            
            # 显示前20个
            print("📋 多余任务记录的用户ID (前20个):")
            for user_id in sorted(list(extra_in_tasks))[:20]:
                print(f"   {user_id}")
            if len(extra_in_tasks) > 20:
                print(f"   ... 还有{len(extra_in_tasks) - 20}个用户")
        
        # 输出任务记录和能量记录不一致的情况
        if task_energy_diff_missing_energy:
            filename = f"task_without_energy_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"有任务记录但缺少能量记录的用户 (共{len(task_energy_diff_missing_energy)}个)\n")
                f.write("=" * 60 + "\n")
                for user_id in sorted(task_energy_diff_missing_energy):
                    f.write(f"{user_id}\n")
            print(f"📝 有任务记录但缺少能量记录的用户ID已保存到: {filename}")
        
        if task_energy_diff_extra_energy:
            filename = f"energy_without_task_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"有能量记录但缺少任务记录的用户 (共{len(task_energy_diff_extra_energy)}个)\n")
                f.write("=" * 60 + "\n")
                for user_id in sorted(task_energy_diff_extra_energy):
                    f.write(f"{user_id}\n")
            print(f"📝 有能量记录但缺少任务记录的用户ID已保存到: {filename}")
        
        # 6. 分析一些具体案例
        print("\n" + "=" * 80)
        print("🔍 案例分析")
        print("=" * 80)
        
        # 分析一些多余任务记录的用户，看看他们的手机验证状态
        if extra_in_tasks:
            sample_users = list(extra_in_tasks)[:5]
            print("📱 检查部分多余任务记录用户的手机验证状态:")
            
            with user_conn.cursor() as cursor:
                for user_id in sample_users:
                    cursor.execute("""
                        SELECT uid, phone_verify, phone, created_time, updated_time
                        FROM client_user 
                        WHERE uid = %s
                    """, (user_id,))
                    user_info = cursor.fetchone()
                    
                    if user_info:
                        print(f"   用户 {user_id}: phone_verify={user_info['phone_verify']}, phone={user_info.get('phone', 'N/A')}")
                    else:
                        print(f"   用户 {user_id}: 用户不存在")
        
        print("\n🎯 总结:")
        print(f"- 理论上应该完全一致的用户数量出现了差异")
        print(f"- 主要问题是有{len(extra_in_tasks)}个用户有任务记录但phone_verify=0")
        print(f"- 这可能是由于数据同步问题或业务逻辑变更导致的")
        print(f"- 建议进一步调查这些用户的历史操作记录")
        
    finally:
        user_conn.close()
        task_conn.close()

if __name__ == "__main__":
    debug_phone_verification_differences() 