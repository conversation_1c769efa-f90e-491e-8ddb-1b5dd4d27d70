# -*- coding: utf-8 -*-
"""
配置文件示例
复制此文件为 config_local.py 并修改相应配置
"""

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'your_username',   # 数据库用户名
    'password': 'your_password', # 数据库密码
    'database': 'your_database', # 数据库名
    'charset': 'utf8mb4'       # 字符集
}

# 检查配置
CHECK_CONFIG = {
    'limit': 10000,            # 检查记录数限制，None表示检查所有
    'batch_size': 1000,        # 每批处理的记录数
    'max_workers': 20,         # 并发线程数
    'request_timeout': 10,     # 请求超时时间（秒）
    'save_to_csv': True,       # 是否保存结果到CSV
    'update_db': False,        # 是否更新数据库中的无效记录
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',           # 日志级别: DEBUG, INFO, WARNING, ERROR
    'log_file': 'cover_url_check.log',  # 日志文件名
    'max_file_size': 10 * 1024 * 1024,  # 最大日志文件大小 (10MB)
    'backup_count': 5,         # 保留的日志文件数量
} 