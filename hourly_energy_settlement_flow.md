# 好友能量加成流程

## 概述

好友能量加成是XME挖矿系统中的社交激励机制，用户可以通过邀请好友获得额外的能量点加成。系统每5分钟执行一次处理，为所有活跃用户计算好友能量加成。本文档描述好友能量加成的处理流程和实现方案。

## 流程图

```mermaid
graph TD
    A["XXL-Job触发好友能量加成任务"] --> B["获取活跃用户列表"]
    B --> C["遍历活跃用户"]
    C --> D["查询用户邀请关系"]
    D --> E{"是否有邀请关系?"}
    
    %% 处理邀请关系
    E -->|Yes| F["获取好友能量点"]
    E -->|No| C
    
    %% 计算加成
    F --> G["计算加成能量点"]
    G --> H["更新用户小时能量"]
    H --> I["创建能量点流水记录"]
    I --> C
```

## 详细流程

### 1. 触发机制
- XXL-Job定时任务，每5分钟触发一次

### 2. 活跃用户获取
- 获取当前活跃的用户列表：
  ```sql
  SELECT DISTINCT user_id FROM user_mining_status 
  WHERE mining_active = TRUE AND NOW() BETWEEN mining_start_time AND mining_end_time
  ```

### 3. 邀请关系处理

#### 3.1 查询用户邀请关系
- 对每个活跃用户，查询其邀请关系：
  ```sql
  -- 查询用户的邀请人（上级）
  SELECT inviter_id FROM user_invitations WHERE invitee_id = ?
  ```

#### 3.2 获取好友能量点
- 获取好友（邀请人）的小时能量点：
  ```sql
  SELECT hour_points FROM user_hourly_energy 
  WHERE user_id = ? AND hour_timestamp = CURRENT_TIMESTAMP - CURRENT_TIMESTAMP % 3600
  ```

#### 3.3 加成比例配置
- 好友活跃能量点加成比例默认为10%
- 从`system_configs`表中获取配置参数：
  ```sql
  SELECT config_value FROM system_configs WHERE config_key = 'friend_energy_bonus_rate'
  ```

### 4. 加成能量点处理

#### 4.1 计算加成能量点
- 根据好友能量点和加成比例计算加成能量点：
  ```java
  // 伪代码
  int friendEnergyPoints = getFriendHourlyEnergyPoints(inviterId);
  double bonusRate = getConfigValue("friend_energy_bonus_rate", 0.1); // 默认10%
  int bonusPoints = (int)(friendEnergyPoints * bonusRate);
  ```

#### 4.2 更新用户小时能量
- 将加成能量点添加到用户当前小时能量中：
  ```sql
  INSERT INTO user_hourly_energy 
  (user_id, hour_timestamp, hour_points) 
  VALUES (?, CURRENT_TIMESTAMP - CURRENT_TIMESTAMP % 3600, ?)
  ON DUPLICATE KEY UPDATE 
  hour_points = hour_points + ?
  ```

#### 4.3 创建能量点流水记录
- 记录好友加成能量点的来源：
  ```sql
  INSERT INTO energy_points_records 
  (user_id, points_type, points_amount, source_type, source_id, hour_timestamp) 
  VALUES (?, 'hourly', ?, 'friend_bonus', ?, CURRENT_TIMESTAMP - CURRENT_TIMESTAMP % 3600)
  ```

## 数据表结构

### 用户挖矿状态表 (user_mining_status)
```sql
CREATE TABLE user_mining_status (
    user_id BIGINT PRIMARY KEY COMMENT '用户ID',
    device_id VARCHAR(128) NOT NULL COMMENT '设备唯一标识，用于反作弊',
    mining_active BOOLEAN DEFAULT FALSE COMMENT '挖矿是否激活状态',
    mining_start_time TIMESTAMP COMMENT '挖矿周期开始时间',
    mining_end_time TIMESTAMP COMMENT '挖矿周期结束时间'
);
```

### 用户小时能量表 (user_hourly_energy)
```sql
CREATE TABLE user_hourly_energy (
    user_id BIGINT NOT NULL COMMENT '用户ID',
    hour_timestamp TIMESTAMP NOT NULL COMMENT '小时时间戳，精确到小时',
    hour_points INT NOT NULL DEFAULT 0 COMMENT '该小时累积的能量点',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (user_id, hour_timestamp)
);
```

### 能量点记录表 (energy_points_records)
```sql
CREATE TABLE energy_points_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    points_type ENUM('base', 'hourly') NOT NULL COMMENT '能量点类型',
    points_amount INT NOT NULL COMMENT '能量点数量',
    source_type VARCHAR(50) NOT NULL COMMENT '能量点来源类型',
    source_id BIGINT COMMENT '来源ID',
    hour_timestamp TIMESTAMP NOT NULL COMMENT '小时时间戳'
);
```

### 用户邀请关系表 (user_invitations)
```sql
CREATE TABLE user_invitations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    inviter_id BIGINT NOT NULL COMMENT '邀请人用户ID',
    invitee_id BIGINT NOT NULL COMMENT '被邀请人用户ID',
    invitation_code VARCHAR(20) COMMENT '邀请码',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_invitee (invitee_id)
);
```

## XXL-Job任务配置

| 参数名称 | 参数值 |
|---------|-------|
| 任务名称 | 好友能量加成处理 |
| Cron表达式 | 0 */5 * * * ? |
| 路由策略 | 分片广播 |
| 执行器 | energy-executor |
| 任务处理器 | friendEnergyBonusProcessor |

## 实现代码示例

```java
@Component
public class FriendEnergyBonusJob {
    
    @XxlJob("friendEnergyBonusProcessor")
    public ReturnT<String> processFriendEnergyBonus(String param) {
        // 获取分片参数
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        int index = shardingVO.getIndex();  // 当前分片
        int total = shardingVO.getTotal();  // 总分片数
        
        // 1. 获取活跃用户
        List<Long> activeUsers = userMiningStatusRepository.findActiveUsersBySharding(index, total);
        
        int processedCount = 0;
        int bonusAppliedCount = 0;
        
        // 2. 遍历处理活跃用户
        for (Long userId : activeUsers) {
            // 3. 查询邀请关系
            Long inviterId = userInvitationRepository.findInviterIdByInviteeId(userId);
            
            if (inviterId != null) {
                // 4. 获取好友能量点
                Integer friendEnergyPoints = userHourlyEnergyRepository.findHourlyPoints(
                    inviterId, getCurrentHourTimestamp());
                
                if (friendEnergyPoints != null && friendEnergyPoints > 0) {
                    // 5. 计算加成
                    double bonusRate = getConfigValue("friend_energy_bonus_rate", 0.1);
                    int bonusPoints = (int)(friendEnergyPoints * bonusRate);
                    
                    // 6. 更新用户小时能量
                    userHourlyEnergyRepository.addHourlyPoints(userId, getCurrentHourTimestamp(), bonusPoints);
                    
                    // 7. 创建能量点流水记录
                    energyPointsRecordRepository.createRecord(
                        userId, "hourly", bonusPoints, "friend_bonus", inviterId, getCurrentHourTimestamp());
                    
                    bonusAppliedCount++;
                }
            }
            
            processedCount++;
        }
        
        XxlJobLogger.log("处理了 {} 个活跃用户，应用了 {} 个好友能量加成", 
                        processedCount, bonusAppliedCount);
        
        return ReturnT.SUCCESS;
    }
    
    private long getCurrentHourTimestamp() {
        return System.currentTimeMillis() / 3600000 * 3600000;
    }
    
    private double getConfigValue(String key, double defaultValue) {
        String value = systemConfigRepository.findValueByKey(key);
        if (value != null) {
            try {
                return Double.parseDouble(value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }
}
```

## 性能优化

1. **分片处理**：
   - 使用XXL-Job的分片广播功能
   - 按用户ID范围分片，平衡负载

2. **批量操作**：
   - 批量查询和更新数据
   - 使用事务确保数据一致性

3. **缓存策略**：
   - 缓存系统配置参数
   - 缓存邀请关系数据

## 注意事项

1. **幂等性设计**：
   - 即使同一用户被多次处理，也不会重复计算加成

2. **防止循环依赖**：
   - 确保好友之间不会互相形成能量加成循环

3. **数据监控**：
   - 监控异常高的能量加成值
   - 设置合理的加成上限
