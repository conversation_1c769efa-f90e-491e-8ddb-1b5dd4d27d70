#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析有任务记录但phone_verify=0的用户
找出为什么会出现这种矛盾的情况
"""

import pymysql
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

USER_DB = {
    'host': 'xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user'
}

TASK_DB = {
    'host': 'xme-prod-media-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task'
}

def get_db_config(**kwargs):
    """准备数据库配置"""
    config = dict(kwargs)
    config['cursorclass'] = pymysql.cursors.DictCursor
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    return config

def analyze_extra_task_users():
    """分析有任务记录但phone_verify=0的用户"""
    print("🔍 分析有任务记录但phone_verify=0的用户...")
    print("=" * 80)
    
    user_conn = pymysql.connect(**get_db_config(**USER_DB))
    task_conn = pymysql.connect(**get_db_config(**TASK_DB))
    
    try:
        # 1. 获取一些有任务记录的用户样本
        print("📋 获取任务记录用户样本...")
        with task_conn.cursor() as cursor:
            cursor.execute("""
                SELECT user_id, id, create_time, update_time, complete_status
                FROM user_task_record_0 
                WHERE task_code = '10005' AND delete_status = 0
                ORDER BY user_id 
                LIMIT 20
            """)
            task_users = cursor.fetchall()
        
        print(f"✅ 获取到 {len(task_users)} 个任务记录用户样本")
        
        # 2. 检查这些用户的详细信息
        print("\n🔍 检查任务记录用户的详细信息:")
        sample_user_ids = [user['user_id'] for user in task_users]
        
        with user_conn.cursor() as cursor:
            for user_id in sample_user_ids:
                cursor.execute("""
                    SELECT uid, phone_verify, phone, created_time, updated_time
                    FROM client_user 
                    WHERE uid = %s
                """, (user_id,))
                user_info = cursor.fetchone()
                
                if user_info:
                    verify_status = "✅已验证" if user_info['phone_verify'] == 1 else "❌未验证"
                    print(f"   用户 {user_id}: {verify_status}")
                    print(f"     手机号: {user_info.get('phone', 'N/A')}")
                    print(f"     注册时间: {user_info.get('created_time', 'N/A')}")
                    print(f"     更新时间: {user_info.get('updated_time', 'N/A')}")
                else:
                    print(f"   用户 {user_id}: ❌用户不存在")
                
                # 检查对应的任务记录
                task_info = next((t for t in task_users if t['user_id'] == user_id), None)
                if task_info:
                    print(f"     任务创建时间: {task_info['create_time']}")
                    print(f"     任务完成状态: {task_info['complete_status']}")
                    print(f"     任务更新时间: {task_info['update_time']}")
                print()
        
        # 3. 分析时间关系
        print("📅 分析时间关系:")
        print("检查用户注册时间和任务创建时间的关系...")
        
        with user_conn.cursor() as cursor:
            time_analysis = []
            for user_id in sample_user_ids[:10]:
                cursor.execute("""
                    SELECT uid, phone_verify, created_time as user_created
                    FROM client_user 
                    WHERE uid = %s
                """, (user_id,))
                user_info = cursor.fetchone()
                
                task_info = next((t for t in task_users if t['user_id'] == user_id), None)
                
                if user_info and task_info:
                    time_analysis.append({
                        'user_id': user_id,
                        'phone_verify': user_info['phone_verify'],
                        'user_created': user_info.get('created_time'),
                        'task_created': task_info['create_time']
                    })
            
            print("\n📊 时间关系分析:")
            for analysis in time_analysis:
                user_time = analysis['user_created']
                task_time = analysis['task_created']
                verify_status = "已验证" if analysis['phone_verify'] == 1 else "未验证"
                
                print(f"   用户 {analysis['user_id']} ({verify_status}):")
                print(f"     用户注册: {user_time}")
                print(f"     任务创建: {task_time}")
                
                if user_time and task_time:
                    if user_time < task_time:
                        print(f"     ✅ 用户先注册，后完成任务")
                    elif user_time > task_time:
                        print(f"     ⚠️  任务时间早于注册时间（异常）")
                    else:
                        print(f"     📅 同一时间")
                print()
        
        # 4. 检查是否存在历史数据或状态变更
        print("🔍 深度分析：检查可能的原因...")
        
        # 随机选择一个phone_verify=0但有任务记录的用户进行深度分析
        problem_user_id = None
        with user_conn.cursor() as cursor:
            for user_id in sample_user_ids:
                cursor.execute("""
                    SELECT uid, phone_verify, phone
                    FROM client_user 
                    WHERE uid = %s AND phone_verify = 0
                """, (user_id,))
                user_info = cursor.fetchone()
                
                if user_info:
                    problem_user_id = user_id
                    break
        
        if problem_user_id:
            print(f"\n🔬 深度分析用户 {problem_user_id}:")
            
            # 检查用户的所有任务记录
            shard = problem_user_id % 1024
            table_name = f"user_task_record_{shard}"
            
            with task_conn.cursor() as cursor:
                cursor.execute(f"""
                    SELECT task_code, complete_status, create_time, update_time
                    FROM {table_name}
                    WHERE user_id = %s AND delete_status = 0
                    ORDER BY create_time
                """, (problem_user_id,))
                all_tasks = cursor.fetchall()
                
                print(f"   该用户的所有任务记录:")
                for task in all_tasks:
                    print(f"     任务 {task['task_code']}: 状态={task['complete_status']}, "
                          f"创建={task['create_time']}, 更新={task['update_time']}")
            
            # 检查用户的详细信息
            with user_conn.cursor() as cursor:
                cursor.execute("""
                    SELECT uid, phone_verify, phone, created_time, updated_time
                    FROM client_user 
                    WHERE uid = %s
                """, (problem_user_id,))
                detailed_user = cursor.fetchone()
                
                if detailed_user:
                    print(f"   用户详细信息:")
                    print(f"     phone_verify: {detailed_user['phone_verify']}")
                    print(f"     phone: {detailed_user.get('phone', 'N/A')}")
                    print(f"     created_time: {detailed_user.get('created_time', 'N/A')}")
                    print(f"     updated_time: {detailed_user.get('updated_time', 'N/A')}")
        
        # 5. 总结可能的原因
        print("\n🎯 可能的原因分析:")
        print("1. 历史数据问题：用户之前验证过手机，后来状态被重置")
        print("2. 业务逻辑变更：手机验证的判断条件发生了变化")
        print("3. 数据迁移问题：任务记录和用户状态可能来自不同的数据源")
        print("4. 测试数据：可能包含了测试用户的数据")
        print("5. 并发问题：任务记录和用户状态更新不同步")
        
    finally:
        user_conn.close()
        task_conn.close()

if __name__ == "__main__":
    analyze_extra_task_users() 