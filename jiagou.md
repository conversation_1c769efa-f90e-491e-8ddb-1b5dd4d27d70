
5月28日修改，定出第一版挖矿数据方案，按小时维度进行核算，整点下发。
经济体系内容汇报
- 1.1. 背景与目标
  - 背景： 为提升X.ME的用户活跃度、留存率，并促进内容生态的繁荣，引入基于用户行为贡献的XME激励系统。通过“多劳多得”的机制，鼓励用户深度参与平台互动。
  - 目标：
    - 提升用户活跃度。
    - 增强用户粘性与留存。
    - 促进内容贡献与互动。
    - 驱动用户增长。
    - 构建XME价值基础。
      - 每日真实暂定下发1500wXME
      - 用户展示数据位9750w
- 1.2. 名词解释
  - XME： 平台发行的激励代币，用户通过参与“宇宙远征”活动获得。
  - 能量点 (EP)： 用户行为贡献的量化积分，是决定用户每小时可获得XME数量的核心依据。能量点本身不可直接交易，仅作为计算XME分配的权重。
  - 基础能量点： 通过完成每日基础任务、成长任务、邀请好友、有效认证等行为获得的能量点，影响用户在当日剩余所有小时内的XME分配。
  - 小时能量点： 通过完成特定小时内的限时任务或即时挑战获得的能量点，仅作用于当前小时的XME分配。
  - 远征飞船/能量核心： 用户能量点的视觉化体现，能量点越多，飞船能量越强/核心越亮。
  - 宇宙星云/XME矿池： 全网XME奖池的视觉化体现。
  - “宇宙远征”/“启动远征”： 用户参与挖矿活动的总称/每日首次参与的动作。
2. 核心机制与逻辑
- 2.1. 核心概念
  - 用户通过完成指定任务获得能量点。
  - 系统根据用户能量点占全网总能量点的比例，以及其他加成因素，每小时为用户分配一定数量的XME。
- 2.2. XME每日总奖池
  - 真实每日总奖池： 15,000,000 XME (固定值，后台可配置，初期不向用户展示此真实值)。
  备注：代币总量1080亿
  - 用户感知每日总奖池： 97,500,000 XME (即真实奖池的6.5倍（可配置），用于前端展示，此放大系数用户不可知)。
- 2.3. 全网总能量点的展示与计算
  - 真实全网总能量点： 系统后台实时统计的所有用户在当前小时内累计的（基础能量点的小时部分 + 小时能量点）的总和。此数值不直接对用户展示。
  - 用户感知全网总能量点：
    - 目标值： 真实全网总能量点 * 6.5 (此6.5倍系数用户不可知)。
    - 展示逻辑：（55分钟和5分钟为可配置 ）
      - 每小时开始时，用户感知全网总能量点会从一个较低的基准值开始。
      - 在每小时的前55分钟，此“用户感知全网总能量点”会根据后台真实全网总能量点的增长情况，平滑地、动画地增长至其目标值。
      - 在每小时的最后5分钟，为了确保用户有相对稳定的预期，“用户感知全网总能量点”的更新频率可以降低，或直接显示为前55分钟结束时计算出的目标值。
- 2.4. 每小时XME分配逻辑
  - 每小时将 (真实每日总奖池 / 24) 的XME分配给当小时参与的用户。
  - 用户本小时预计可领XME (前端实时预估，仅供参考)：
用户本小时预计可领XME ≈ (用户当前小时总能量点 / 用户感知全网总能量点) * (用户感知每日总奖池 / 24)
    - 用户当前小时总能量点 = 用户当日已累积的基础能量点  + 用户当前小时已累积的小时能量点。
    - 此预估值会随着用户能量点的增加、用户感知全网总能量点的变化而实时动态变化。
  - 用户本小时实际获得XME (小时结束时结算)：
用户本小时实际获得XME = (用户本小时最终总能量点 / 本小时最终真实全网总能量点) * (真实每日总奖池 / 24)
    - 此计算结果需受限于“个人每小时XME获取上限”。
- 2.5. 个人XME获取上限控制——这部分先待定
  - 设计目的： 为了保证系统的公平性和可持续性，防止少数用户获取过多XME，同时让更多用户能获得激励。
  - 核心原则：
    1. 每个用户每日获得的XME总数有一个上限（例如，每日2000 XME或每日200 XME，此上限值由后台配置，用户界面不直接展示）。
    2. 用户在单个小时内获得的XME也受此每日上限的动态影响。
  - 单小时上限动态调整逻辑：
    - 如果一个用户在当天 HH:MM (例如，23:30) 才开始参与当天的“宇宙远征”，并且他的每日XME获取上限是 当日最大值。
    - 系统会判断他当天还剩下多少“可完整参与结算的小时数”。
    - 关键点： 即使用户在当天很晚的时候才加入（比如只剩下不到一个完整小时），他仍然有机会在当前这最后一个（可能不完整的）小时内，尽力去获取最高达到其当日总上限 XME。这意味着，如果他的能量点足够高，他可以在这短暂的时间内“冲刺”他的日上限。
    - 总量控制： 无论单小时如何分配，用户在任何一天（UTC 0点到次日UTC 0点）累计获得的XME总量，绝不会超过其配置的每日上限 当日最大值。
  - 用户体验上的“趋缓”处理：
    - 当系统预估用户“本小时预计可领XME”快要接近其（由每日上限推算出的）“本小时理论可获得上限”时，前端界面上“本小时预计可领XME”的增长速度会明显减慢。
    - 这样做是为了给用户一个平滑的体验过渡，避免因为突然触达上限而感到困惑。这可以通过调整公式中能量点的权重或引入一个衰减函数来实现视觉上的趋缓效果。
  - 最终结算： 小时结束时，实际发放给用户的XME，既不能超过其“动态计算出的本小时可获得上限”，也不能导致其当日累计获得的XME超过“每日总上限”。
- 2.6. 锁仓奖励机制——这部分先不考虑
  - 用户可以将已获得的XME进行锁仓，以获得基础能量点加成。
  - 锁仓能量点加成计算公式 (示例)：
锁仓能量点加成 = 锁仓数量因子 * 锁仓时长因子 * log(历史参与次数 + 1) 
    - 锁仓数量因子: 基于锁仓XME数量的因子。
    - 锁仓时长因子: 基于锁仓时长的因子。
    - log(历史参与次数 + 1): 基于用户历史完成的挖矿会话次数 (每日启动一次远征视为一次会话) 的对数因子。(上所前开放该系数) - 理解为这个因子或整个锁仓奖励机制可能在特定里程碑才完全激活或调整其权重。
  - 锁仓产生的能量点加成，作为“基础能量点”每日固定增加到用户的总基础能量点中。
3. 功能模块
- 3.1. 能量星球/宇宙远征主界面
  - 3.1.1. 核心信息展示 (显示优先级从高到低)
    - P0 (最核心)： “本小时预计可领XME”， “启动远征/加速引擎/当前远征状态”按钮/指示器， 用户的“远征飞船/能量核心”。
    - P1 (重要信息)： “本小时挖矿倒计时”， “今日已获得总XME”， “我的当前总能量点”， 任务入口/快捷任务指示。
    - P2 (辅助信息)： “用户感知全网总能量点”， “今日挖矿总倒计时”， 邀请好友入口， XME钱包/资产入口， 规则说明/帮助入口。
  - 3.1.2. 核心交互元素 (宇宙远征主题示意)
    - 远征飞船/能量核心： 静止/待命状态，启动/加速动画，能量点增长反馈（粒子飞向飞船/核心，视觉增强），悬停提示。
    - “本小时预计可领XME”显示： 数字动态变化，强调动画。
    - 任务入口： 任务列表图标或“能量任务板”，高优先级任务特殊标记。
  - 3.1.3. 倒计时结束处理
    - “本小时挖矿倒计时”结束： 提示结算，显示本小时最终获得XME，播放收获动画，更新今日总获得，飞船/核心状态变化，新小时倒计时开始。
    - “今日挖矿总倒计时”结束 (UTC 0点)： 提示今日结束，明日开启，日相关状态重置，可选日结动画。
- 3.2. 任务系统
  - 3.2.1. 任务类型与能量点奖励 (任务内容仅为示例，能量点值参考附录)
    - 基础任务 (每日可重复，贡献基础能量点)： 每日签到/启动远征，浏览内容，点赞，评论，关注，分享。
    - 小时激励任务 (限时随机/特定时段，贡献小时能量点)： 特定小时内完成指定任务。
      - 衰减任务系数：
    - 成长任务/一次性任务 (贡献基础能量点或直接XME)： 完善个人资料，选择兴趣偏好（至少3个），首次发布内容，首次邀请好友，首次锁仓，完成新手引导。
    - 循环任务： 例如，每成功邀请N位有效好友，额外奖励。
    - 累积任务： 例如，累计登录X天，奖励。
  - 3.2.2. 任务状态与跳转
    - 待完成： 显示任务信息、奖励、进度，点击跳转。
    - 已完成，待领取： 按钮变为“领取能量点”，视觉提示。
    - 已领取/已完成： 按钮置灰或显示“已完成”。
  - 3.2.3. 任务领取机制 (混合式)
    - 主要方式：手动领取。
    - 一键领取优化： 提供“一键领取全部”按钮。
    - 小时结束自动归集： 小时结束时，系统自动归集所有“已完成，待领取”任务的能量点。
  - 3.2.4. 任务有效性定义 (示例)
    - 浏览内容： 停留时长或滑动到底部。
    - 点赞/评论： 真实操作，后台风控。
    - 分享： 成功唤起分享面板。
    - 选择兴趣偏好： 至少选择3个兴趣标签并保存。
  - 3.2.5. 任务管理与配置 (后台)
    - 任务名称、描述、类型、能量点奖励值、能量点类型、完成上限、跳转链接、起止时间、前置条件、启用/停用状态、更改生效机制。
  - 3.2.6. 任务开发优先级 (第一版核心)
    - P0 (必须实现)： 基础任务框架及核心任务，成长任务框架及核心任务，手动与自动领取，状态流转，后台配置。
    - P1 (重要功能)： 小时激励任务，邀请好友相关任务，XME锁仓任务。
    - P2 (后续迭代)： 循环任务，累积任务。
- 3.3. 邀请好友系统
  - 3.3.1. 邀请流程（可与创世大使保持一致）
    - 生成专属邀请链接/邀请码。
    - 好友通过链接注册或填写邀请码。
    - 好友完成有效认证后，邀请关系绑定。
  - 3.3.2. 邀请奖励
    - 一次性-邀请奖励： 邀请人成功邀请一位有效新用户后，获得一次性基础能量点奖励。
  - 3.3.3. 好友活跃能量点加成
    - 邀请人可以从其一级活跃好友产生的能量点中获得一定比例的额外基础能量点加成。
    - 此加成由平台额外提供，不从被邀请好友的能量点中扣除。
    - 每日通过好友活跃能量点加成的上限可进行配置 (后台可配)。
  - 3.3.4. “ping”好友功能 (提醒非活跃好友)
    - 用户可以对长时间未活跃的好友进行“呼叫”操作。
    - 被“呼叫”的好友会收到系统推送。
    - “ping”操作有时间间隔限制： 对同一好友的“呼叫”操作，至少间隔 M 分钟 (后台可配置)。
    - 每日“ping”总次数可设上限。
  - 3.3.5. 有效新用户定义
    - 通过邀请链接/邀请码注册。
    - 完成新用户核心认证步骤：完成人脸识别。
    - 设备唯一性校验。---后置
- 3.4. XME锁仓系统——这部分先不考虑
  - 3.4.1. 锁仓配置 (用户可选择)
    - 锁仓比例/数量。
    - 锁仓时长： 提供多个固定时长选项。
  - 3.4.2. 锁仓流程
    - 选择数量（百分比最大可到200%）和时长，确认锁仓，XME冻结，计算能量点加成，到期解锁。
  - 3.4.3. 锁仓奖励计算 (能量点加成)
    - 公式 (同4.6)。 各因子参数后台配置，向用户清晰展示预期加成。
  - 3.4.4. 锁仓能量点发放
    - 作为“基础能量点”每日固定增加。
4. 新用户引导
- 4.1. 目的
  - 帮助新用户快速理解玩法和价值。
  - 引导完成首次操作和关键任务。
  - 提升转化率和早期留存。
- 4.2. 引导流程 (示例)
  - 步骤1：首次进入欢迎。
  - 步骤2：引导点击“启动远征/加速引擎”。-
  - 步骤3：引导进入任务列表。
  - 步骤4：引导完成一个简单基础任务并领取。
  - 步骤5：引导完成兴趣选择任务并领取。
  - 步骤6：解释XME的意义 (可选)。
  - 步骤7：引导邀请好友 (可选)。
  - 步骤8：完成引导系列任务本身给予奖励。
- 4.3. 引导形式
  - 强制与非强制结合，高亮，聚焦，手势，说明。
5. 其他全局设定
- 5.1. 时间显示
  - 所有与“天”相关的周期，以 UTC 00:00 作为一天的开始和结束。
  - 客户端界面可转换为用户本地时间，但需明确标注。
  - “本小时倒计时”基于服务器的整点小时。
- 5.2. 数值精度
  - XME代币数量显示：小数点后4-8位。
  - 能量点数量显示：可为整数。
- 5.3. 反作弊
  - 设备指纹、IP限制、行为模式分析，人脸识别。
6. 数据埋点建议
- 主界面访问，启动按钮点击，各任务参与/完成/领取，邀请相关数据，呼叫功能使用，锁仓参与，能量点/XME获取分布，新手引导转化。
7. 附录一: 建议的初版数值配置 (示例，需仔细测算和调整)
配置项/任务名称	类型/目标	建议能量点奖励 (类型: 数值)	每日/每小时上限	备注/有效性定义
全局配置				
真实每日XME总奖池	系统产出	15,000,000 XME	固定	
用户感知每日XME总奖池放大系数	用户体验	6.5 倍	固定	用户不可知
用户每日XME获取上限 (示例1)	个体产出控制	2,000 XME	1次/日/用户	后台可配
用户每日XME获取上限 (示例2)	个体产出控制	200 XME	1次/日/用户	后台可配
好友活跃能量点加成比例	邀请激励	10% (好友基础能量点)	-	后台可配
每日好友活跃能量点加成上限/邀请人	邀请激励上限	500 能量点	1次/日/邀请人	后台可配
“呼叫”好友间隔时间	防止骚扰	240 分钟	-	后台可配
基础任务 (每日)				能量点类型：基础能量点
每日签到/启动远征	用户活跃	基础: 50 能量点	1次/日	点击“启动远征”或进入主界面视为完成。
浏览文章/视频 (每篇/个)	内容消费	基础: 10 能量点	10次/日	停留 >15秒或滑到底部。
点赞内容 (每次)	内容互动	基础: 5 能量点	20次/日	真实点赞。
评论内容 (每次有效)	内容互动/贡献	基础: 20 能量点	5次/日	评论字数 >10字，内容相关，非灌水。
关注用户 (每次)	社交关系构建	基础: 15 能量点	5次/日	真实关注。
分享内容到外部 (每次)	用户增长	基础: 30 能量点	3次/日	成功唤起分享面板并选择渠道。
小时激励任务 (示例)				能量点类型：小时能量点
限时点赞挑战 (1小时内)	短期激励	小时: 50 能量点	1次/指定小时	在指定小时内点赞5篇指定标签文章。
成长/一次性任务				能量点类型：基础能量点 (或直接XME)
完善个人资料	用户信息完整度	基础: 100 能量点	1次/账号	完成度达到80%以上。
选择兴趣偏好	用户画像/个性化推荐	基础: 80 能量点	1次/账号	用户至少选择3个兴趣标签并成功保存。
首次发布内容	用户原创内容激励	基础: 150 能量点	1次/账号	发布符合社区规范的内容。
首次邀请好友 (奖励给邀请人)	裂变引导	基础: 200 能量点	1次/账号	成功邀请第一位完成人脸识别的有效新用户后额外奖励。
首次完成XME锁仓	锁仓引导	基础: 100 能量点	1次/账号	完成任意一笔锁仓操作。
XME锁仓任务 (能量点加成因子)	这部分先不考虑			能量点类型：基础能量点 (通过公式增加)
锁仓数量因子	锁仓激励	示例: 每1000XME增加0.01	-	具体函数或分档设计
锁仓时长因子	锁仓激励	示例: 1个月=1.2, 1年=2.5	-	具体函数或分档设计
挖矿会话对数因子	长期参与激励	示例: log10(历史参与次数+1)	-	历史参与次数为历史挖矿会话次数
8. 附录二: 建议的设计风格与主题 (宇宙远征 - 用户体验与界面备注)
- 核心主题： “宇宙远征”、“星际探索”、“能量收集”。
- 视觉风格： 科技感、未来感、深邃宇宙背景、星云、粒子效果、光效。
- 色彩搭配： 深蓝、紫色、黑色作为背景主色调，搭配亮蓝、金色、橙色等作为能量、XME、按钮等高亮元素的颜色。
- 核心元素视觉化：
  - 能量点： 用户的“远征飞船”的能量等级。飞船可以有不同形态或装饰，随能量点等级提升而进化/升级。能量核心的亮度、大小、动态效果。
  - XME获取： 从“宇宙星云/XME矿池”中通过飞船“吸取”或“采集”XME。
  - 任务： “星际任务板”、“远征指令”。
  - 邀请好友： “组建远征队”、“招募队员”。
  - 锁仓： “能量水晶封存”、“星际银行储蓄”。
- 动效与反馈：
  - 流畅的飞船飞行、能量聚集、XME收集动画。
  - 点击按钮、完成任务、领取奖励时的即时声光反馈。
  - 能量点增加时，飞船/能量核心的视觉增强。
  - “本小时预计可领XME”数值变化时的强调动画。
- 界面布局优化原则：
  - 信息层级清晰： 最重要的信息（预计收益、行动点）最突出。
  - 减少干扰： 避免不必要的视觉元素堆砌，保持界面清爽。
  - 引导性强： 通过视觉焦点和动效引导用户进行核心操作。
  - 氛围营造： 通过整体视觉风格和细节点缀，增强“宇宙远征”的沉浸感。
- 交互细节：
  - “本小时焦点区”： 将“本小时倒计时”、“本小时预计可领XME”、“当前小时能量点贡献”等强相关的即时信息聚合展示，与“今日累计”信息有视觉区分但又和谐统一。
  - 能量点与XME关系暗示： 通过飞船能量增强（能量点提升）后，从星云中吸取XME的动画效果更显著（量更大、速度更快）来视觉化正相关。
  - 公式展示（若采纳）： 以极简化的视觉元素（如：[我的飞船能量] / [宇宙能量波动] x [本小时星矿储备] ≈ [我的XME收获]）配合动态图标，而非复杂的数学公式。
10000人每天完成核心能量-----评估
核心能量：
配置项/任务名称	类型/目标	建议能量点奖励 (类型: 数值)	每日/每小时上限	备注/有效性定义
每日签到/启动远征	用户活跃	开关定义（计时24小时）---启动即签到	1次/日	点击“启动远征”或进入主界面视为完成。
完善个人资料	用户信息完整度	基础: 100 能量点	1次/账号	完成度达到80%以上。
选择兴趣偏好	用户画像/个性化推荐	基础: 80 能量点	1次/账号	用户至少选择3个兴趣标签并成功保存。
首次发布内容	用户原创内容激励	基础: 150 能量点	1次/账号	发布符合社区规范的内容。
首次邀请好友 (奖励给邀请人)	裂变引导	基础: 200 能量点	1次/账号	成功邀请第一位完成人脸识别的有效新用户后额外奖励。
首次完成XME锁仓（后置）	锁仓引导	基础: 100 能量点	1次/账号	完成任意一笔锁仓操作。
twitter		200能量点		
手机号		200能量点		验证后加
邮箱		200能量点		验证后加
影响力能量：每小时衰减200---挖矿周期清零
配置项/任务名称	类型/目标	建议能量点奖励 (类型: 数值)	每日/每小时上限	备注/有效性定义
浏览文章/视频 时间累积	内容消费	基础: 100能量点，200能量点，700能量点，1000能量点，1100，2000	60s- 3分钟，10分钟，20分钟，45分钟，90分钟	累积
点赞内容 (每次)	内容互动	基础: 50能量点	20次一个挖矿周期	真实点赞。
评论内容 (每次有效)	内容互动/贡献	基础: 200 能量点	5次一个挖矿周期	评论字数 /字母>10字，内容相关，非灌水。这条需讨论，多语言可能无法恒定
关注用户 (每次)	社交关系构建	基础: 150 能量点	5次一个挖矿周期	真实关注。重复取消不算，一个关注只算一次
分享内容到外部 (每次)	用户增长	基础: 300能量点---500证实	3次一个挖矿周期	成功唤起分享面板并选择渠道。
好友活跃能量点加成比例	邀请激励	10% (好友能量点每小时结束)	-	后台可配
成功邀请新人	邀请激励	500 能量点	-	后台可配