# 好友能量累积流程设计文档

## 概述

好友能量累积是XME挖矿系统中的社交激励机制，通过好友间的互动和能量共享，提高用户的挖矿效率和社交粘性。用户可以通过邀请好友、与好友互动、组建挖矿团队等方式获得额外的能量点加成，形成良性的社交循环。

## 流程图

```mermaid
graph TD
    A["用户A挖矿产生能量"] --> B["系统计算好友加成"]
    B --> C["查询用户A的好友列表"]
    
    %% 好友加成计算
    C --> D{"好友数量 > 0?"}
    D -->|是| E["计算好友加成比例"]
    D -->|否| F["无好友加成"]
    
    %% 好友加成应用
    E --> G["应用好友加成到用户A的能量"]
    G --> H["更新用户A的小时能量"]
    
    %% 好友能量分享
    G --> I["计算分享给好友的能量"]
    I --> J["遍历用户A的好友列表"]
    J --> K["为每个好友创建能量分享记录"]
    
    %% 好友通知
    K --> L["发送能量分享通知"]
    L --> M["好友查看获得的分享能量"]
    
    %% 团队加成
    N["检查用户是否在挖矿团队中"] --> O{"是否有活跃团队?"}
    O -->|是| P["计算团队加成"]
    O -->|否| Q["无团队加成"]
    P --> R["应用团队加成"]
    
    %% 好友互动
    S["用户间互动"] --> T["增加好友亲密度"]
    T --> U["提高未来能量分享比例"]
```

## 详细流程说明

### 1. 好友加成机制
- 当用户产生小时能量点时，系统自动计算好友加成
- 好友加成比例根据好友数量和好友等级计算
- 加成公式：`base_bonus + (friend_count * 0.01) + (active_friend_count * 0.02)`
  - `base_bonus`: 基础加成比例（如5%）
  - `friend_count`: 好友总数
  - `active_friend_count`: 活跃好友数（24小时内有挖矿行为）

### 2. 好友能量分享
- 用户产生的部分能量点会自动分享给好友
- 分享比例根据好友关系亲密度决定
- 分享机制：
  ```
  分享能量 = 用户小时能量 * 分享比例 * 好友亲密度系数
  ```
- 分享上限：每小时每位好友最多获得用户能量的5%
- 好友能量分享不会减少用户自身的能量，是额外奖励

### 3. 好友亲密度
- 好友亲密度影响能量分享比例
- 亲密度通过以下行为提升：
  - 共同完成任务
  - 互相点赞/评论
  - 连续多天互动
  - 组队挖矿
- 亲密度等级：
  | 等级 | 亲密度值 | 能量分享系数 |
  |-----|---------|------------|
  | 1   | 0-100   | 1.0        |
  | 2   | 101-300 | 1.2        |
  | 3   | 301-600 | 1.5        |
  | 4   | 601-1000| 1.8        |
  | 5   | >1000   | 2.0        |

### 4. 团队挖矿加成
- 用户可以组建或加入挖矿团队（最多5人）
- 团队成员同时在线挖矿时获得额外加成
- 团队加成计算：
  ```
  团队加成 = 基础加成 * (在线成员数 / 总成员数) * 团队等级系数
  ```
- 团队等级根据团队总能量产出和活跃度提升

### 5. 反作弊机制
- 监控异常的好友关系（如短时间内添加大量好友）
- 限制单个用户能从好友处获得的总能量
- 防止"僵尸"好友账号：要求好友账号有真实活跃行为

## API接口设计

### 1. 获取好友加成信息

**请求**：
```
GET /energy/v1/friend/bonus
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "friend_bonus_rate": 0.15,           // 当前好友加成比例
    "friend_count": 12,                  // 好友总数
    "active_friend_count": 8,            // 活跃好友数
    "team_bonus_rate": 0.08,             // 团队加成比例
    "estimated_hourly_bonus": 25,        // 预计每小时获得的好友加成能量
    "top_contributors": [                // 贡献最多能量的好友
      {
        "user_id": "user123",
        "nickname": "挖矿达人",
        "avatar": "https://example.com/avatar.jpg",
        "contribution": 120,             // 累计贡献能量
        "intimacy_level": 3              // 亲密度等级
      }
    ]
  },
  "success": true
}
```

### 2. 查看好友分享记录

**请求**：
```
GET /energy/v1/friend/shares?page=1&size=20
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "total_received": 350,              // 总共收到的分享能量
    "total_shared": 420,                // 总共分享出去的能量
    "records": [
      {
        "record_id": "share12345",
        "friend": {
          "user_id": "user456",
          "nickname": "矿工小明",
          "avatar": "https://example.com/avatar2.jpg"
        },
        "points": 15,                   // 分享的能量点数
        "direction": "received",        // received/shared
        "timestamp": "2025-05-30T14:30:22+08:00",
        "intimacy_level": 2
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total_pages": 3,
      "total_items": 58
    }
  },
  "success": true
}
```

### 3. 提升好友亲密度

**请求**：
```
POST /friend/v1/interaction
Content-Type: application/json

{
  "friend_id": "user456",
  "interaction_type": "like",       // like, comment, gift, etc.
  "content_id": "post789"           // 互动的内容ID（可选）
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "intimacy_added": 5,            // 增加的亲密度
    "current_intimacy": 325,        // 当前亲密度
    "current_level": 3,             // 当前亲密度等级
    "next_level_threshold": 600,    // 下一级所需亲密度
    "energy_share_rate": 1.5        // 当前能量分享系数
  },
  "success": true
}
```

## 数据模型

### 好友关系表
```sql
CREATE TABLE user_friendships (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    friend_id BIGINT NOT NULL,
    intimacy_points INT NOT NULL DEFAULT 0,
    intimacy_level INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_interaction_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_friend (user_id, friend_id),
    INDEX idx_user_id (user_id),
    INDEX idx_friend_id (friend_id),
    INDEX idx_intimacy (intimacy_level)
);
```

### 好友能量分享记录表
```sql
CREATE TABLE friend_energy_shares (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    from_user_id BIGINT NOT NULL,
    to_user_id BIGINT NOT NULL,
    points_amount INT NOT NULL,
    hour_timestamp BIGINT NOT NULL,
    intimacy_level INT NOT NULL,
    share_rate DECIMAL(3,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_from_user (from_user_id, hour_timestamp),
    INDEX idx_to_user (to_user_id, hour_timestamp)
);
```

### 好友互动记录表
```sql
CREATE TABLE friend_interactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    friend_id BIGINT NOT NULL,
    interaction_type VARCHAR(20) NOT NULL,
    content_id VARCHAR(100) NULL,
    intimacy_added INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_friend (user_id, friend_id),
    INDEX idx_recent (created_at)
);
```

### 挖矿团队表
```sql
CREATE TABLE mining_teams (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    team_name VARCHAR(50) NOT NULL,
    team_level INT NOT NULL DEFAULT 1,
    team_exp INT NOT NULL DEFAULT 0,
    leader_id BIGINT NOT NULL,
    member_count INT NOT NULL DEFAULT 1,
    max_members INT NOT NULL DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_leader (leader_id),
    INDEX idx_level (team_level)
);
```

### 团队成员表
```sql
CREATE TABLE team_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    team_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'member',
    contribution_points BIGINT NOT NULL DEFAULT 0,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_team_user (team_id, user_id),
    INDEX idx_team (team_id),
    INDEX idx_user (user_id)
);
```

## 实现细节

### 1. 好友加成计算
```java
// 伪代码
public FriendBonusResult calculateFriendBonus(Long userId, int baseEnergyPoints) {
    // 获取用户好友列表
    List<Friendship> friendships = friendshipRepository.findByUserId(userId);
    
    // 计算好友加成比例
    int friendCount = friendships.size();
    int activeFriendCount = countActiveFriends(friendships);
    
    double bonusRate = 0.05 + (friendCount * 0.01) + (activeFriendCount * 0.02);
    // 设置上限
    bonusRate = Math.min(bonusRate, 0.30);
    
    // 计算好友加成能量
    int bonusPoints = (int)(baseEnergyPoints * bonusRate);
    
    // 计算分享给好友的能量
    distributeEnergyToFriends(userId, friendships, baseEnergyPoints);
    
    return new FriendBonusResult(bonusRate, bonusPoints, friendCount, activeFriendCount);
}
```

### 2. 好友能量分享
```java
// 伪代码
private void distributeEnergyToFriends(Long userId, List<Friendship> friendships, int baseEnergyPoints) {
    // 为每个好友计算分享能量
    for (Friendship friendship : friendships) {
        // 根据亲密度等级获取分享系数
        double shareRate = getIntimacyShareRate(friendship.getIntimacyLevel());
        
        // 计算分享给该好友的能量
        int sharedPoints = (int)(baseEnergyPoints * 0.05 * shareRate);
        
        // 创建分享记录
        createEnergyShareRecord(userId, friendship.getFriendId(), sharedPoints, 
                               friendship.getIntimacyLevel(), shareRate);
        
        // 更新好友的小时能量
        updateFriendHourlyEnergy(friendship.getFriendId(), sharedPoints);
    }
}
```

### 3. 团队加成计算
```java
// 伪代码
public TeamBonusResult calculateTeamBonus(Long userId, int baseEnergyPoints) {
    // 查询用户所在团队
    TeamMember membership = teamMemberRepository.findByUserId(userId);
    if (membership == null) {
        return new TeamBonusResult(0, 0);
    }
    
    // 获取团队信息
    Team team = teamRepository.findById(membership.getTeamId());
    
    // 获取当前在线团队成员数
    int onlineMembers = countOnlineTeamMembers(team.getId());
    
    // 计算团队加成
    double teamLevelFactor = 1.0 + (team.getTeamLevel() * 0.1);
    double onlineRatio = (double)onlineMembers / team.getMemberCount();
    double bonusRate = 0.05 * onlineRatio * teamLevelFactor;
    
    // 计算加成能量
    int bonusPoints = (int)(baseEnergyPoints * bonusRate);
    
    // 更新团队贡献度
    updateTeamContribution(userId, membership.getTeamId(), bonusPoints);
    
    return new TeamBonusResult(bonusRate, bonusPoints);
}
```

## 客户端实现建议

1. **好友加成UI**：
   - 在挖矿页面显示当前好友加成比例
   - 可视化展示好友贡献排行榜
   - 提供邀请好友的快捷入口

2. **好友互动提醒**：
   - 收到好友能量时推送通知
   - 提示用户与久未互动的好友进行互动
   - 展示互动可获得的亲密度和能量加成

3. **团队协作UI**：
   - 显示团队成员在线状态
   - 团队能量贡献排行
   - 团队等级进度条

## 性能优化建议

1. **批量处理**：
   - 批量计算和更新好友能量分享
   - 使用定时任务处理非实时计算

2. **缓存策略**：
   - 缓存好友列表和亲密度数据
   - 缓存团队信息和在线状态
   - 定期刷新缓存数据到数据库

3. **分布式计算**：
   - 对于大量用户的好友能量计算，采用分布式处理
   - 使用消息队列异步处理能量分享

## 扩展功能

1. **好友能量礼物**：
   - 允许用户将部分能量打包成礼物发送给好友
   - 好友接收后获得额外奖励

2. **好友挖矿排行榜**：
   - 展示好友间的挖矿效率排名
   - 提供周期性奖励给排名靠前的用户

3. **好友助力**：
   - 好友可以提供一次性"助力"，临时提高用户挖矿效率
   - 每天限制助力次数

4. **能量红包**：
   - 用户可以发送能量红包给好友群
   - 增加社交互动和趣味性
