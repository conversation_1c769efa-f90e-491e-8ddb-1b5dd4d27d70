# XME挖矿系统 - 技术设计文档

## 1. 系统概述

### 1.1 目的
本文档概述了X.ME平台XME挖矿系统的技术实现，该系统通过每小时挖矿机制根据用户平台参与度奖励XME代币。

### 1.2 范围
系统包括：
- 每小时挖矿计算和代币分配
- 能量点累积机制
- 任务完成跟踪
- 挖矿活动的用户界面
- 数据存储和处理架构

### 1.3 核心概念
- **XME代币**：平台发行的激励代币，分发给用户
- **能量点(EP)**：量化的用户贡献指标
- **基础能量点**：影响所有小时分配的长期点数
- **小时能量点**：仅影响当前小时分配的短期点数
- **挖矿池**：每日分配的XME代币（真实1500万，显示9750万）

## 2. 系统架构

### 2.1 架构图


### 2.2 组件描述

#### 2.2.1 客户端应用
- 显示带有宇宙远征主题的挖矿界面
- 显示实时能量点和预估XME收益
- 提供任务完成界面
- 可视化挖矿状态和倒计时

#### 2.2.2 API网关
- 将请求路由到适当的微服务
- 处理认证和速率限制
- 为客户端应用提供统一的API端点

#### 2.2.3 挖矿服务
- 管理挖矿生命周期的核心服务
- 收集和统计用户能量点数据
- 定时触发每小时挖矿计算
- 将能量数据推送到Kafka消息队列

#### 2.2.4 消息队列(Kafka)
- 提供服务间的异步通信
- 缓冲挖矿服务和Point服务之间的数据交换
- 确保消息可靠传递和处理
- 支持高吞吐量的数据流

#### 2.2.5 Point服务
- 管理用户XME钱包和交易记录
- 根据挖矿服务提供的能量数据计算XME分配
- 处理XME代币的发放和流水记录
- 提供钱包余额查询和交易历史

#### 2.2.6 任务服务(独立服务)
- 定义和管理可用任务
- 跟踪任务完成状态
- 验证任务完成要求
- 完成任务后奖励能量点
- 提供任务推荐和个性化任务列表

#### 2.2.7 用户服务
- 管理用户资料和认证
- 跟踪用户注册日期以确定挖矿资格
- 处理兴趣选择和资料完善
- 管理用户关系和社交功能

#### 2.2.8 数据库层
- 存储系统的所有持久数据
- 包括用户数据、挖矿历史、任务完成记录、XME交易记录
- 针对高频读/写操作进行优化

## 3. 数据库设计

### 3.1 架构设计

仅供参考，以实际开发逻辑为准

#### 3.1.1 用户挖矿状态表
```sql
CREATE TABLE user_mining_status (
    user_id BIGINT PRIMARY KEY COMMENT '用户ID',
    device_id VARCHAR(128) NOT NULL COMMENT '设备唯一标识，用于反作弊',
    device_type ENUM('ios', 'android', 'web') NOT NULL COMMENT '设备类型',
    device_info TEXT COMMENT '设备详细信息（型号、系统版本等）',
    mining_active BOOLEAN DEFAULT FALSE COMMENT '挖矿是否激活状态',
    mining_start_time TIMESTAMP COMMENT '挖矿周期开始时间',
    mining_end_time TIMESTAMP COMMENT '挖矿周期结束时间',
    daily_xme_earned DECIMAL(20,8) DEFAULT 0 COMMENT '当日已获得XME总量',
    last_mining_date DATE COMMENT '最近一次挖矿日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    INDEX idx_mining_active (mining_active) COMMENT '挖矿状态索引',
    INDEX idx_mining_time (mining_start_time, mining_end_time) COMMENT '挖矿周期时间索引',
    INDEX idx_device_id (device_id) COMMENT '设备ID索引'
);
```
/**
 * 反作弊逻辑说明：
 * 1. 当用户启动挖矿时，检查当前设备是否已被其他用户用于挖矿，检查X-Device-Is是否符合
 * 2. 查询条件：
 *    - 查找与当前设备相同且挖矿周期有重叠的记录
 *    - SQL: SELECT * FROM user_mining_status 
 *           WHERE device_id = ? AND user_id != ? AND mining_active = TRUE 
 *           AND NOW() BETWEEN mining_start_time AND mining_end_time
 * 3. 如果存在这样的记录，说明该设备已被其他用户用于挖矿，拒绝当前用户的挖矿请求
 * 4. 如果不存在这样的记录，则允许用户开始挖矿
 */

#### 3.1.2 能量点汇总表
```sql
CREATE TABLE user_bāse_energy (
    user_id BIGINT PRIMARY KEY COMMENT '用户ID',
    total_base_points INT NOT NULL DEFAULT 0 COMMENT '累积的基础能量点总数',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) COMMENT '用户能量点汇总表，只记录总量';
```

#### 3.1.2 用户小时能量表
```sql
CREATE TABLE user_hourly_energy (
    user_id BIGINT NOT NULL COMMENT '用户ID',
    hour_timestamp TIMESTAMP NOT NULL COMMENT '小时时间戳，精确到小时',
    hour_points INT NOT NULL DEFAULT 0 COMMENT '该小时累积的能量点',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (user_id, hour_timestamp),
    INDEX idx_hour_timestamp (hour_timestamp) COMMENT '小时时间戳索引'
) COMMENT '用户小时能量点表，用于整点计算';
```

#### 3.1.5 能量点记录表
```sql
CREATE TABLE energy_points_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID，自增主键',
    user_id BIGINT NOT NULL COMMENT '用户ID，关联用户表',
    points_type ENUM('base', 'hourly') NOT NULL COMMENT '能量点类型：base(基础能量点)、hourly(小时能量点)',
    points_amount INT NOT NULL COMMENT '能量点数量',
    source_type VARCHAR(50) NOT NULL COMMENT '能量点来源类型，如：task(任务)、login(登录)、social(社交行为)',
    source_id BIGINT COMMENT '来源ID，根据source_type对应不同表的ID',
    task_id BIGINT COMMENT '关联的任务ID，如果来源是任务',
    hour_timestamp TIMESTAMP NOT NULL COMMENT '小时时间戳，标识该能量点属于哪个小时周期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引，用于快速查询用户的能量点记录',
    INDEX idx_hour_timestamp (hour_timestamp) COMMENT '小时时间戳索引，用于按时间段查询和数据分区'
);
```

#### 3.1.7 任务定义表
```sql
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID，自增主键',
    name VARCHAR(100) NOT NULL COMMENT '任务名称',
    description TEXT COMMENT '任务描述',
    task_type ENUM('base', 'hourly', 'growth', 'invitation') NOT NULL COMMENT '任务类型：base(基础任务)、hourly(小时任务)、growth(成长任务)、invitation(邀请任务)',
    points_type ENUM('base', 'hourly') NOT NULL COMMENT '奖励能量点类型：base(基础能量点)、hourly(小时能量点)',
    points_amount INT NOT NULL COMMENT '奖励能量点数量',
    daily_limit INT DEFAULT 1 COMMENT '每日完成次数限制，默认1次',
    hourly_limit INT DEFAULT 1 COMMENT '每小时完成次数限制，默认1次',
    lifetime_limit INT DEFAULT NULL COMMENT '用户累计完成次数限制，NULL表示无限制',
    start_time TIMESTAMP COMMENT '任务开始时间，任务仅在此时间后可用',
    end_time TIMESTAMP COMMENT '任务结束时间，任务仅在此时间前可用',
    status TINYINT DEFAULT 1 COMMENT '任务状态：1-激活，0-停用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'
);
```

#### 3.1.8 用户任务完成表
```sql
CREATE TABLE user_task_completions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID，自增主键',
    user_id BIGINT NOT NULL COMMENT '用户ID，关联用户表',
    task_id BIGINT NOT NULL COMMENT '任务ID，关联任务定义表',
    completion_time TIMESTAMP NOT NULL COMMENT '任务完成时间',
    points_awarded INT NOT NULL COMMENT '奖励的能量点数量',
    is_claimed BOOLEAN DEFAULT FALSE COMMENT '是否已领取奖励，FALSE-未领取，TRUE-已领取',
    claim_time TIMESTAMP COMMENT '奖励领取时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引，用于快速查询用户的任务完成记录',
    INDEX idx_task_id (task_id) COMMENT '任务ID索引，用于快速查询特定任务的完成情况',
    INDEX idx_completion_time (completion_time) COMMENT '完成时间索引，用于按时间查询和数据分析'
);
```


#### 3.1.10 用户社交媒体账号表
```sql
CREATE TABLE user_social_accounts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID，自增主键',
    user_id BIGINT NOT NULL COMMENT '用户ID，关联用户表',
    platform_type ENUM('twitter', 'telegram') NOT NULL COMMENT '社交平台类型：twitter、telegram等',
    platform_user_id VARCHAR(100) COMMENT '用户在社交平台上的唯一ID',
    username VARCHAR(100) COMMENT '用户在社交平台上的用户名',
    access_token VARCHAR(255) COMMENT '访问令牌，用于调用社交平台API',
    refresh_token VARCHAR(255) COMMENT '刷新令牌，用于获取新的access_token',
    token_expiry TIMESTAMP COMMENT '令牌过期时间',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证，TRUE-已验证，FALSE-未验证',
    verification_time TIMESTAMP COMMENT '验证完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引，用于快速查询用户的社交账号',
    INDEX idx_platform (platform_type) COMMENT '平台类型索引，用于按平台类型查询',
    UNIQUE KEY uk_user_platform (user_id, platform_type) COMMENT '用户ID和平台类型的唯一索引，确保用户在每个平台只有一个账号'
);
```

##### 3.2.4.3 数据一致性保障
- 使用分布式锁确保同步任务不会重复执行
- 实现幂等性设计，防止重复计算
- 设置监控告警，及时发现同步异常
- 提供手动触发同步的管理接口，用于应急处理

## 4. API设计

### 4.1 挖矿服务API

#### 4.1.1 检查挖矿状态
```
GET /api/v1/mining/status
```
响应：
```json
{
  "code": 200,
  "message": "success",
  "result": {
    "hourly_countdown_seconds": 1800,
    "daily_countdown_seconds": 38400,
    "user_base_energy_points": 500,
    "user_hourly_energy_points": 350,
    "user_total_energy_points": 850,
    "network_energy_points": 5525000,
    "displayed_network_energy_points": 35912500,
    "estimated_hourly_xme": 245.8765,
    "daily_xme_earned": 1250.4321,
    "mining_active": true
  },
  "success": true
}
```

#### 4.1.2 开始启动挖矿任务周期
```
POST /task/start
```
响应：
```json
{
  "code": 200,
  "message": "success",
  "result": {
    "mining_active": true,
    "energy_points_awarded": 50,
    "message": "挖矿成功启动"
  },
  "success": true
}
```

#### 4.1.3 领取每小时XME
```
POST /task/claim
```
响应：
```json
{
  "code": 200,
  "message": "success",
  "result": {
    "xme_amount": 245.8765,
    "total_daily_xme": 1496.3086,
    "message": "XME领取成功"
  },
  "success": true
}
```

### 4.2 任务服务API

#### 4.2.1 获取可用任务
```
GET /task/list
```
响应：
```json
{
  "code": 200,
  "message": "success",
  "result": {
    "base_tasks": [
      {
        "id": 1,
        "name": "每日签到",
        "description": "开始您今天的远征",
        "points_amount": 50,
        "points_type": "base",
        "status": "completed",
        "daily_limit": 1,
        "completion_count": 1
      },
      {
        "id": 2,
        "name": "完善个人资料",
        "description": "完善您的个人资料信息",
        "points_amount": 100,
        "points_type": "base",
        "status": "completed",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 1
      },
      {
        "id": 3,
        "name": "选择兴趣标签",
        "description": "选择至少3个您感兴趣的标签",
        "points_amount": 100,
        "points_type": "base",
        "status": "completed",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 1
      },
      {
        "id": 4,
        "name": "绑定手机号",
        "description": "绑定您的手机号以提高账号安全性",
        "points_amount": 100,
        "points_type": "base",
        "status": "completed",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 1
      },
      {
        "id": 5,
        "name": "连接Twitter",
        "description": "连接您的Twitter账号并关注官方账号",
        "points_amount": 100,
        "points_type": "base",
        "status": "available",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 0
      },
      {
        "id": 6,
        "name": "加入Telegram群组",
        "description": "加入官方Telegram群组",
        "points_amount": 100,
        "points_type": "base",
        "status": "available",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 0
      },
      {
        "id": 7,
        "name": "关注Discord频道",
        "description": "加入并关注官方Discord频道",
        "points_amount": 100,
        "points_type": "base",
        "status": "available",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 0
      },
      {
        "id": 8,
        "name": "连接钱包",
        "description": "连接您的Web3钱包",
        "points_amount": 150,
        "points_type": "base",
        "status": "available",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 0
      },
      {
        "id": 9,
        "name": "邀请好友",
        "description": "邀请好友加入平台",
        "points_amount": 200,
        "points_type": "base",
        "status": "available",
        "daily_limit": null,
        "lifetime_limit": null,
        "completion_count": 0,
        "progress": 0,
        "target": null
      },
      {
        "id": 10,
        "name": "分享内容",
        "description": "分享平台内容到社交媒体",
        "points_amount": 50,
        "points_type": "base",
        "status": "available",
        "daily_limit": 3,
        "completion_count": 0
      }
    ],
    "hourly_tasks": [
      {
        "id": 101,
        "name": "点赞挑战",
        "description": "在下一小时内点赞5篇文章",
        "points_amount": 50,
        "points_type": "hourly",
        "status": "in_progress",
        "hourly_limit": 1,
        "completion_count": 0,
        "progress": 3,
        "target": 5
      },
      {
        "id": 102,
        "name": "评论挑战",
        "description": "在下一小时内发表3条评论",
        "points_amount": 75,
        "points_type": "hourly",
        "status": "available",
        "hourly_limit": 1,
        "completion_count": 0,
        "progress": 0,
        "target": 3
      },
      {
        "id": 103,
        "name": "发布内容",
        "description": "在下一小时内发布1篇原创内容",
        "points_amount": 100,
        "points_type": "hourly",
        "status": "available",
        "hourly_limit": 1,
        "completion_count": 0,
        "progress": 0,
        "target": 1
      },
      {
        "id": 104,
        "name": "浏览发现页",
        "description": "在下一小时内浏览发现页至少3分钟",
        "points_amount": 30,
        "points_type": "hourly",
        "status": "available",
        "hourly_limit": 1,
        "completion_count": 0,
        "progress": 0,
        "target": 180
      },
      {
        "id": 105,
        "name": "关注新用户",
        "description": "在下一小时内关注3个新用户",
        "points_amount": 40,
        "points_type": "hourly",
        "status": "available",
        "hourly_limit": 1,
        "completion_count": 0,
        "progress": 0,
        "target": 3
      }
    ],
    "growth_tasks": [
      {
        "id": 201,
        "name": "内容创作者",
        "description": "发布10篇原创内容",
        "points_amount": 500,
        "points_type": "base",
        "status": "in_progress",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 0,
        "progress": 3,
        "target": 10
      },
      {
        "id": 202,
        "name": "社区活跃者",
        "description": "连续7天登录平台",
        "points_amount": 300,
        "points_type": "base",
        "status": "in_progress",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 0,
        "progress": 4,
        "target": 7
      },
      {
        "id": 203,
        "name": "影响力扩散",
        "description": "累计获得100个点赞",
        "points_amount": 400,
        "points_type": "base",
        "status": "in_progress",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 0,
        "progress": 42,
        "target": 100
      },
      {
        "id": 204,
        "name": "社交达人",
        "description": "拥有50个粉丝",
        "points_amount": 450,
        "points_type": "base",
        "status": "in_progress",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 0,
        "progress": 12,
        "target": 50
      },
      {
        "id": 205,
        "name": "Web3探索者",
        "description": "完成5次NFT互动",
        "points_amount": 600,
        "points_type": "base",
        "status": "available",
        "daily_limit": null,
        "lifetime_limit": 1,
        "completion_count": 0,
        "progress": 0,
        "target": 5
      }
    ]
  },
  "success": true
}
```

#### 4.2.2 完成任务
```
POST /api/v1/tasks/{taskId}/complete
```
响应：
```json
{
  "code": 200,
  "message": "success",
  "result": {
    "task_id": 101,
    "points_awarded": 50,
    "points_type": "hourly",
    "message": "任务完成成功"
  },
  "success": true
}
```

#### 4.2.3 领取任务奖励
```
POST /api/v1/tasks/{taskId}/claim
```
响应：
```json
{
  "code": 200,
  "message": "success",
  "result": {
    "task_id": 101,
    "points_awarded": 50,
    "points_type": "hourly",
    "message": "奖励领取成功"
  },
  "success": true
}
```



## 7. 性能优化

### 7.1 整点计算优化

整点计算是系统的主要性能瓶颈，需要特别关注其优化：

#### 7.1.1 分批处理策略

```
1. 用户分片处理
   - 根据用户ID取模分为10个分片
   - 每个分片单独处理，并行执行
   - 示例：用户ID % 10 = 0的用户在第一批处理

2. 时间错开策略
   - 整点后的前5分钟内完成所有分片处理
   - 每30秒处理一个分片
   - 使用分布式锁确保分片只被处理一次

3. 优先级处理
   - 活跃用户（过去24小时有活动）优先处理
   - 能量点高的用户优先处理
```

#### 7.1.2 延迟初始化策略

```
1. 活跃用户即时初始化
   - 对过去24小时有活动的用户，在整点后立即初始化新小时能量点

2. 低活跃用户延迟初始化
   - 对于低活跃用户，采用懒加载策略
   - 在用户下次活动时才初始化新小时能量点

3. 批量预初始化
   - 在整点前5分钟开始预创建下一小时的记录
   - 整点时只需激活这些记录，减少写入压力
```

#### 7.1.3 缓存优化

```
1. 多级缓存策略
   - L1缓存：本地内存缓存，缓存热点数据
   - L2缓存：Redis分布式缓存，缓存用户能量点和全网能量点
   - L3缓存：数据库持久化存储

2. 缓存预热策略
   - 整点前预热活跃用户的能量点数据
   - 使用滑动窗口预测算法预测用户活跃度

3. 分布式缓存集群
   - 采用Redis Cluster模式，确保缓存的高可用性
   - 根据用户ID哈希分片，均衡负载
```

### 7.2 数据库优化

#### 7.2.1 分表分区策略

##### 10万DAU规模分表分区方案

| 表名 | 分表/分区策略 | 每日新增记录数 | 每日数据增长 | 每月数据量 | 分片数量 | 查询性能提升 |
|---------|------------|-------------|------------|----------|----------|----------|
| energy_points_records | 按月分表 | 480万条 | 约960MB | 约30GB | 每月1个表 | 查询时间减少70% |
| user_hourly_energy | 按月分表 | 240万条 | 约240MB | 约7.2GB | 每月1个表 | 查询时间减少75% |
| user_bāse_energy | 不分表 | 变动很小 | 约1MB | 约0.5GB | 1 | 不适用 |
| user_task_completions | 按用户ID范围分表 | 40万条 | 约40MB | 约1.2GB | 5个分片，每分片约280MB | 查询时间减少70% |

计算说明：
- energy_points_records: 10万DAU × 平均48条/天/用户 = 480万条/天
- user_hourly_energy: 10万DAU × 24小时 = 240万条/天（考虑能量衰减机制，所有用户每小时都需要一条记录）
- user_task_completions: 10万DAU × 平均4任务/天/用户 = 40万条/天

##### 100万DAU规模分表分区方案

| 表名 | 分表/分区策略 | 每日新增记录数 | 每日数据增长 | 每月数据量 | 分片数量 | 查询性能提升 |
|---------|------------|-------------|------------|----------|----------|----------|
| energy_points_records | 按月+用户ID范围分表 | 4800万条 | 约9.6GB | 约300GB | 每月5个分片，每分片约60GB | 查询时间减少85% |
| user_hourly_energy | 按月+用户ID范围分表 | 2400万条 | 约2.4GB | 约72GB | 每月5个分片，每分片约14.4GB | 查询时间减少80% |
| user_bāse_energy | 按用户ID范围分表 | 变动很小 | 约10MB | 约5GB | 5个分片，每分片约1GB | 查询时间减少50% |
| user_task_completions | 按用户ID范围分表 | 400万条 | 约600MB | 约18GB | 10个分片，每分片约1.8GB | 查询时间减少80% |

计算说明：
- energy_points_records: 100万DAU × 平均48条/天/用户 = 4800万条/天
- user_hourly_energy: 100万DAU × 24小时 = 2400万条/天（考虑能量衰减机制，所有用户每小时都需要一条记录）
- user_task_completions: 100万DAU × 平均4任务/天/用户 = 400万条/天


### 7.5 数据量评估

#### 7.5.1 用户规模预估

##### 10万DAU规模

| 指标 | 数值 |
|---------|--------|
| 日活用户(DAU) | 10万 |
| 小时活跃用户 | 2.5万 |
| 同时在线用户峰值 | 1.5万 |
| 平均每用户每天完成任务数 | 3个 |
| 平均每用户每小时能量点记录 | 2条 |

##### 100万DAU规模

| 指标 | 数值 |
|---------|--------|
| 日活用户(DAU) | 100万 |
| 小时活跃用户 | 25万 |
| 同时在线用户峰值 | 15万 |
| 平均每用户每天完成任务数 | 4个 |
| 平均每用户每小时能量点记录 | 3条 |

#### 7.5.2 数据存储评估

##### 10万DAU规模

| 表名 | 单条记录大小 | 每日新增记录数 | 每月存储增长 | 年存储需求 |
|---------|------------|-------------|------------|----------|
| user_mining_status | 约500字节 | 10万条 | 约5GB | 约60GB |
| energy_points_records | 约200字节 | 480万条 | 约96GB | 约1.15TB |
| user_energy_summary | 约300字节 | 10万条 | 约3GB | 约36GB |
| xme_distribution_records | 约150字节 | 240万条 | 约36GB | 约432GB |
| user_task_completions | 约150字节 | 30万条 | 约4.5GB | 约54GB |

计算说明：
- energy_points_records: 10万DAU × 2条/小时/用户 × 24小时 = 480万条/天
- xme_distribution_records: 10万DAU × 24小时 = 240万条/天
- user_task_completions: 10万DAU × 3任务/天/用户 = 30万条/天

##### 100万DAU规模

| 表名 | 单条记录大小 | 每日新增记录数 | 每月存储增长 | 年存储需求 |
|---------|------------|-------------|------------|----------|
| user_mining_status | 约500字节 | 100万条 | 约50GB | 约600GB |
| energy_points_records | 约200字节 | 7200万条 | 约1.44TB | 约17.3TB |
| user_energy_summary | 约300字节 | 100万条 | 约30GB | 约360GB |
| xme_distribution_records | 约150字节 | 2400万条 | 约360GB | 约4.3TB |
| user_task_completions | 约150字节 | 400万条 | 约60GB | 约720GB |

计算说明：
- energy_points_records: 100万DAU × 3条/小时/用户 × 24小时 = 7200万条/天
- xme_distribution_records: 100万DAU × 24小时 = 2400万条/天
- user_task_completions: 100万DAU × 4任务/天/用户 = 400万条/天

#### 7.5.3 整点计算负载评估

##### 10万DAU规模

| 指标 | 数值 |
|---------|--------|
| 单次整点计算数据量 | 约25万条记录 |
| 数据库查询数 | 约8万次 |
| 内存消耗 | 约1.5GB |
| 预计计算时间 | <15秒 |
| Redis缓存大小 | 约800MB |

##### 100万DAU规模

| 指标 | 数值 |
|---------|--------|
| 单次整点计算数据量 | 约250万条记录 |
| 数据库查询数 | 约80万次 |
| 内存消耗 | 约15GB |
| 预计计算时间 | <90秒 |
| Redis缓存大小 | 约8GB |

#### 7.5.4 网络流量评估

##### 10万DAU规模

| 场景 | 平均请求大小 | 峰值 QPS | 平均 QPS | 日流量总量 |
|---------|------------|----------|----------|----------|
| 用户查询挖矿状态 | 2KB | 1500 | 400 | 约69GB |
| 用户启动挖矿 | 1KB | 800 | 150 | 约13GB |
| 用户完成任务 | 1.5KB | 2500 | 600 | 约78GB |
| 整点XME分配 | 1KB | 4000 | 100 | 约9GB |

##### 100万DAU规模

| 场景 | 平均请求大小 | 峰值 QPS | 平均 QPS | 日流量总量 |
|---------|------------|----------|----------|----------|
| 用户查询挖矿状态 | 2KB | 15000 | 4000 | 约690GB |
| 用户启动挖矿 | 1KB | 8000 | 1500 | 约130GB |
| 用户完成任务 | 1.5KB | 25000 | 6000 | 约778GB |
| 整点XME分配 | 1KB | 40000 | 1000 | 约86GB |

#### 7.5.5 资源需求评估

##### 10万DAU规模

| 服务 | 实例数 | 单实例配置 |
|---------|-----------|----------|
| API网关 | 3 | 4核CPU, 8GB内存 |
| 挖矿服务 | 4 | 8核CPU, 16GB内存 |
| 任务服务 | 4 | 8核CPU, 16GB内存 |
| 用户服务 | 2 | 4核CPU, 8GB内存 |
| Point服务 | 2 | 4核CPU, 8GB内存 |
| Redis集群 | 3主3从 | 8核CPU, 16GB内存 |
| 数据库集群 | 2主2从 | 16核CPU, 32GB内存 |
| Kafka集群 | 3 | 8核CPU, 16GB内存 |

##### 100万DAU规模

| 服务 | 实例数 | 单实例配置 |
|---------|-----------|----------|
| API网关 | 8 | 8核CPU, 16GB内存 |
| 挖矿服务 | 12 | 16核CPU, 32GB内存 |
| 任务服务 | 12 | 16核CPU, 32GB内存 |
| 用户服务 | 6 | 8核CPU, 16GB内存 |
| Point服务 | 6 | 8核CPU, 16GB内存 |
| Redis集群 | 5主5从 | 16核CPU, 64GB内存 |
| 数据库集群 | 3主3从 | 32核CPU, 128GB内存 |
| Kafka集群 | 5 | 16核CPU, 32GB内存 |

### 7.6 监控与告警

#### 7.6.1 性能监控

```
1. 关键指标监控
   - 整点计算耗时
   - 数据库查询响应时间
   - API响应时间分布
   - 内存使用率

2. 实时分析
   - 实时监控整点计算进度
   - 负载分析和瓶颈检测

3. 历史趋势分析
   - 性能指标的历史趋势分析
   - 容量规划预测
```

#### 7.6.2 告警策略

```
1. 多级告警
   - P0：整点计算失败或超时，电话告警
   - P1：性能下降超过50%，SMS告警
   - P2：性能下降超过20%，邮件告警

2. 自动恢复策略
   - 服务实例健康检查失败时自动重启
   - 数据库连接池自动刷新
   - 缓存自动预热

3. 降级策略
   - 当系统负载过高时，自动启动降级策略
   - 减少非关键功能的计算频率
   - 增加缓存过期时间
```

### 6.4 第4阶段：用户界面（第7-8周）
- 挖矿主界面
- 任务列表和完成UI
- 能量可视化
- 倒计时和动画

### 6.5 第5阶段：兴趣系统（第9-10周）
- 兴趣分类和选择UI
- 兴趣弹窗逻辑
- 基于兴趣的任务推荐
- 兴趣统计

### 6.6 第6阶段：测试和优化（第11-12周）
- 负载测试
- 性能优化
- 安全审查
- 最终调整

## 7. 监控和分析

### 7.1 需要跟踪的关键指标
- 每日活跃挖矿用户
- 每用户平均能量点
- XME分配统计
- 任务完成率
- 兴趣选择模式
- 弹窗转化率

### 7.2 日志策略
- 用户挖矿行为
- 能量点交易
- XME分配
- 任务完成
- 系统性能指标

### 7.3 告警机制
- 异常挖矿模式
- 系统性能下降
- 数据库负载阈值
- API错误率

## 8. 安全考虑

### 8.1 反作弊措施
- 设备指纹
- IP地址监控
- 行为分析
- 任务完成验证
- 速率限制

### 8.2 数据保护
- 敏感数据加密
- 管理操作的访问控制
- 所有修改的审计日志

### 8.3 速率限制
- API端点速率限制
- 任务完成速率限制
- 挖矿行为速率限制

## 9. 可扩展性考虑

### 9.1 数据库扩展
- 高容量查询的读取副本
- 用户数据的分片策略
- 挖矿记录的时间序列优化

### 9.2 服务扩展
- API服务的水平扩展
- 挖矿分配的专用计算节点
- 频繁访问数据的缓存层

### 9.3 负载处理
- 高峰时段容量规划
- 优雅降级策略
- 非关键操作的基于队列的处理

## 6. 社交媒体任务实现

### 6.1 Twitter关注任务

#### 6.1.1 功能概述
- 用户连接Twitter账号并关注官方账号
- 验证关注状态并奖励能量点
- 一次性任务，完成后获得基础能量点

#### 6.1.2 实现流程

```
┌─────────┐      ┌─────────┐      ┌─────────┐      ┌─────────┐
│         │      │         │      │         │      │         │
│ 客户端  │      │ API网关 │      │任务服务 │      │Twitter  │
│         │      │         │      │         │      │   API   │
└────┬────┘      └────┬────┘      └────┬────┘      └────┬────┘
     │                │                │                │
     │  请求Twitter   │                │                │
     │  授权链接      │                │                │
     │───────────────>│                │                │
     │                │   转发请求     │                │
     │                │───────────────>│                │
     │                │                │  生成授权URL   │
     │                │                │───────────────>│
     │                │                │                │
     │                │                │  返回授权URL   │
     │                │                │<───────────────│
     │                │   返回URL      │                │
     │                │<───────────────│                │
     │  授权URL       │                │                │
     │<───────────────│                │                │
     │                │                │                │
     │  用户授权      │                │                │
     │───────────────────────────────────────────────>│
     │                │                │                │
     │  授权回调      │                │                │
     │<───────────────────────────────────────────────│
     │                │                │                │
     │  提交授权码    │                │                │
     │───────────────>│                │                │
     │                │   转发授权码   │                │
     │                │───────────────>│                │
     │                │                │  交换访问令牌  │
     │                │                │───────────────>│
     │                │                │                │
     │                │                │  返回访问令牌  │
     │                │                │<───────────────│
     │                │                │                │
     │                │                │  验证关注状态  │
     │                │                │───────────────>│
     │                │                │                │
     │                │                │  返回关注状态  │
     │                │                │<───────────────│
     │                │   返回结果     │                │
     │                │<───────────────│                │
     │  任务完成状态  │                │                │
     │<───────────────│                │                │
     │                │                │                │
```

#### 6.1.3 API端点

##### 获取Twitter授权URL
```
GET /api/v1/social/twitter/auth-url
```
响应：
```json
{
  "auth_url": "https://twitter.com/i/oauth2/authorize?...",
  "state": "xyz123"
}
```

##### 提交Twitter授权码
```
POST /api/v1/social/twitter/callback
```
请求：
```json
{
  "code": "授权码",
  "state": "xyz123"
}
```
响应：
```json
{
  "success": true,
  "is_following": true,
  "points_awarded": 100,
  "message": "Twitter账号连接成功并已关注官方账号"
}
```

##### 验证Twitter关注状态
```
GET /api/v1/social/twitter/verify-follow
```
响应：
```json
{
  "is_connected": true,
  "is_following": true,
  "username": "@user123",
  "task_completed": true
}
```

#### 6.1.4 数据流
1. 用户请求Twitter授权URL
2. 任务服务生成授权URL并返回给客户端
3. 用户在Twitter上授权应用访问
4. 授权完成后，客户端将授权码提交给任务服务
5. 任务服务使用授权码交换访问令牌
6. 任务服务验证用户是否关注官方账号
7. 如果关注验证成功，记录任务完成并奖励能量点

### 6.2 Telegram群组任务

#### 6.2.1 功能概述
- 用户加入官方Telegram群组
- 验证群组成员身份并奖励能量点
- 一次性任务，完成后获得基础能量点

#### 6.2.2 实现流程

```
┌─────────┐      ┌─────────┐      ┌─────────┐      ┌─────────┐
│         │      │         │      │         │      │         │
│ 客户端  │      │ API网关 │      │任务服务 │      │Telegram │
│         │      │         │      │         │      │ Bot API │
└────┬────┘      └────┬────┘      └────┬────┘      └────┬────┘
     │                │                │                │
     │  获取Telegram  │                │                │
     │  群组链接      │                │                │
     │───────────────>│                │                │
     │                │   转发请求     │                │
     │                │───────────────>│                │
     │                │   返回链接     │                │
     │                │<───────────────│                │
     │  群组链接      │                │                │
     │<───────────────│                │                │
     │                │                │                │
     │  用户加入群组  │                │                │
     │  (Telegram应用)│                │                │
     │                │                │                │
     │  提交Telegram  │                │                │
     │  用户名        │                │                │
     │───────────────>│                │                │
     │                │   转发用户名   │                │
     │                │───────────────>│                │
     │                │                │  验证群组成员  │
     │                │                │───────────────>│
     │                │                │                │
     │                │                │  返回验证结果  │
     │                │                │<───────────────│
     │                │   返回结果     │                │
     │                │<───────────────│                │
     │  任务完成状态  │                │                │
     │<───────────────│                │                │
     │                │                │                │
```

#### 6.2.3 API端点

##### 获取Telegram群组链接
```
GET /api/v1/social/telegram/group-link
```
响应：
```json
{
  "group_link": "https://t.me/xme_official",
  "deep_link": "tg://join?invite=..."
}
```

##### 验证Telegram群组成员身份
```
POST /api/v1/social/telegram/verify-membership
```
请求：
```json
{
  "username": "@user123"
}
```
响应：
```json
{
  "success": true,
  "is_member": true,
  "points_awarded": 100,
  "message": "Telegram群组成员身份验证成功"
}
```

#### 6.2.4 数据流
1. 用户请求Telegram群组链接
2. 任务服务返回官方群组链接
3. 用户通过链接加入Telegram群组
4. 用户提交Telegram用户名进行验证
5. 任务服务通过Telegram Bot API验证用户是否为群组成员
6. 如果验证成功，记录任务完成并奖励能量点

### 6.3 实现注意事项

#### 6.3.1 安全考虑
- OAuth令牌安全存储，使用加密方式存储在数据库中
- 防止CSRF攻击，使用state参数验证OAuth回调
- 限制API调用频率，防止滥用

#### 6.3.2 错误处理
- 授权失败处理机制
- API限流处理
- 用户名验证失败的友好提示

#### 6.3.3 客户端实现要点
- Twitter授权需要使用WebView或系统浏览器
- Telegram链接应支持直接打开Telegram应用
- 提供清晰的用户指引和状态反馈




## 10. 附录

### 10.1 能量点值参考
| 任务类型 | 能量点 | 点数类型 | 每日/每小时限制 |
|-----------|--------------|-------------|-------------------|
| 每日签到 | 50 | 基础 | 1/天 |
| 浏览内容 | 10 | 基础 | 10/天 |
| 点赞内容 | 5 | 基础 | 20/天 |
| 评论 | 20 | 基础 | 5/天 |
| 关注用户 | 15 | 基础 | 5/天 |
| 分享内容 | 30 | 基础 | 3/天 |
| 小时挑战 | 50 | 小时 | 1/小时 |
| 完善资料 | 100 | 基础 | 1/终身 |
| 选择兴趣 | 80 | 基础 | 1/终身 |
| 首次发帖 | 150 | 基础 | 1/终身 |
| 首次邀请 | 200 | 基础 | 1/终身 |
| 连接Twitter | 100 | 基础 | 1/终身 |
| 加入Telegram群组 | 100 | 基础 | 1/终身 |

### 10.2 配置参数
| 参数 | 默认值 | 描述 |
|-----------|--------------|-------------|
| DAILY_XME_POOL | 15000000 | 真实每日XME分配 |
| DISPLAY_MULTIPLIER | 6.5 | 前端显示倍数 |
| HOURLY_ANIMATION_MINUTES | 55 | 平滑动画的分钟数 |
| HOURLY_STABLE_MINUTES | 5 | 稳定显示的分钟数 |
| MAX_DAILY_XME_PER_USER | 2000 | 每用户每天最大XME |
| ZERO_INTERESTS_MAX_POPUPS | 3 | 零兴趣最大弹窗次数 |
| ZERO_INTERESTS_POPUP_INTERVAL_DAYS | 2 | 零兴趣弹窗间隔天数 |
| FEW_INTERESTS_POPUP_INTERVAL_DAYS | 7 | 少量兴趣弹窗间隔天数 |
| SKIPPED_INTERESTS_POPUP_INTERVAL_DAYS | 3 | 跳过用户弹窗间隔天数 |
