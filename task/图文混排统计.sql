-- 图文混排内容统计脚本（完整版）
-- 统计posts表中的图文混排、图片视频文字混排内容情况

-- 1. 按内容类型统计总体情况
SELECT 
    content_type,
    CASE 
        WHEN content_type = 1 THEN '纯文本'
        WHEN content_type = 2 THEN '图片'
        WHEN content_type = 3 THEN '视频'
        WHEN content_type = 4 THEN '图文混排'
        ELSE '其他'
    END AS content_type_name,
    COUNT(*) AS total_posts,
    COUNT(DISTINCT user_id) AS unique_users
FROM posts 
WHERE is_del = 0
GROUP BY content_type
ORDER BY content_type;

-- 2. 精确的图文混排统计（基于media_data字段和内容分析）
SELECT 
    CASE 
        WHEN content_type = 4 THEN '标准图文混排'
        WHEN content_type = 2 AND (content IS NOT NULL AND TRIM(content) != '') THEN '图片配文字'
        WHEN content_type = 3 AND (content IS NOT NULL AND TRIM(content) != '') THEN '视频配文字'
        ELSE '其他混排类型'
    END AS mixed_content_type,
    COUNT(*) AS total_posts,
    COUNT(DISTINCT user_id) AS unique_users,
    COUNT(CASE WHEN status = 2 THEN 1 END) AS approved_posts
FROM posts 
WHERE is_del = 0
AND (
    -- 标准混合内容
    content_type = 4 
    -- 或图片内容但有文字描述
    OR (content_type = 2 AND content IS NOT NULL AND TRIM(content) != '')
    -- 或视频内容但有文字描述
    OR (content_type = 3 AND content IS NOT NULL AND TRIM(content) != '')
    -- 或包含媒体数据的内容
    OR (media_data IS NOT NULL AND media_data != '' AND media_data != '[]')
)
GROUP BY mixed_content_type
ORDER BY total_posts DESC;

-- 3. 基于media_data字段的详细混排分析
SELECT 
    CASE 
        WHEN media_data IS NULL OR media_data = '' OR media_data = '[]' THEN '无媒体内容'
        WHEN media_data LIKE '%"type":1%' AND media_data LIKE '%"type":2%' THEN '图片+视频混排'
        WHEN media_data LIKE '%"type":1%' AND media_data NOT LIKE '%"type":2%' THEN '纯图片'
        WHEN media_data LIKE '%"type":2%' AND media_data NOT LIKE '%"type":1%' THEN '纯视频'
        ELSE '其他媒体类型'
    END AS media_composition,
    CASE 
        WHEN content IS NOT NULL AND TRIM(content) != '' THEN '包含文字'
        ELSE '无文字'
    END AS text_status,
    COUNT(*) AS total_posts,
    COUNT(DISTINCT user_id) AS unique_users
FROM posts 
WHERE is_del = 0
GROUP BY media_composition, text_status
ORDER BY total_posts DESC;

-- 4. 真正的图文混排统计（图片+文字）
SELECT 
    '图片+文字混排' AS content_category,
    COUNT(*) AS total_posts,
    COUNT(DISTINCT user_id) AS unique_users,
    COUNT(CASE WHEN status = 2 THEN 1 END) AS approved_posts,
    COUNT(CASE WHEN post_type = 1 THEN 1 END) AS original_posts,
    AVG(CHAR_LENGTH(TRIM(content))) AS avg_text_length
FROM posts 
WHERE is_del = 0
AND (
    -- 标准图文混排
    content_type = 4
    -- 或图片类型且有有效文字内容
    OR (content_type = 2 AND content IS NOT NULL AND TRIM(content) != '')
    -- 或media_data包含图片且有文字
    OR (media_data LIKE '%"type":1%' AND content IS NOT NULL AND TRIM(content) != '')
);

-- 5. 真正的视频文字混排统计（视频+文字）
SELECT 
    '视频+文字混排' AS content_category,
    COUNT(*) AS total_posts,
    COUNT(DISTINCT user_id) AS unique_users,
    COUNT(CASE WHEN status = 2 THEN 1 END) AS approved_posts,
    COUNT(CASE WHEN post_type = 1 THEN 1 END) AS original_posts,
    AVG(CHAR_LENGTH(TRIM(content))) AS avg_text_length
FROM posts 
WHERE is_del = 0
AND (
    -- 视频类型且有有效文字内容
    (content_type = 3 AND content IS NOT NULL AND TRIM(content) != '')
    -- 或media_data包含视频且有文字
    OR (media_data LIKE '%"type":2%' AND content IS NOT NULL AND TRIM(content) != '')
);

-- 6. 图片+视频+文字三重混排统计
SELECT 
    '图片+视频+文字混排' AS content_category,
    COUNT(*) AS total_posts,
    COUNT(DISTINCT user_id) AS unique_users,
    COUNT(CASE WHEN status = 2 THEN 1 END) AS approved_posts,
    COUNT(CASE WHEN post_type = 1 THEN 1 END) AS original_posts,
    AVG(CHAR_LENGTH(TRIM(content))) AS avg_text_length
FROM posts 
WHERE is_del = 0
AND content IS NOT NULL 
AND TRIM(content) != ''
AND (
    -- media_data同时包含图片和视频
    (media_data LIKE '%"type":1%' AND media_data LIKE '%"type":2%')
    -- 或content_type为4且有复杂媒体数据
    OR (content_type = 4 AND media_data LIKE '%"type":1%' AND media_data LIKE '%"type":2%')
);

-- 7. 使用post_medias表的精确混排统计
SELECT 
    media_types.type_combination,
    CASE 
        WHEN p.content IS NOT NULL AND TRIM(p.content) != '' THEN '包含文字'
        ELSE '无文字'
    END AS text_status,
    COUNT(DISTINCT p.id) AS total_posts,
    COUNT(DISTINCT p.user_id) AS unique_users,
    AVG(CHAR_LENGTH(TRIM(p.content))) AS avg_text_length
FROM posts p
JOIN (
    SELECT 
        post_id,
        CASE 
            WHEN GROUP_CONCAT(DISTINCT type ORDER BY type) = '1' THEN '纯图片'
            WHEN GROUP_CONCAT(DISTINCT type ORDER BY type) = '2' THEN '纯视频'
            WHEN GROUP_CONCAT(DISTINCT type ORDER BY type) = '1,2' THEN '图片+视频'
            ELSE '其他媒体组合'
        END AS type_combination
    FROM post_medias 
    WHERE is_del = 0
    GROUP BY post_id
) AS media_types ON p.id = media_types.post_id
WHERE p.is_del = 0
GROUP BY media_types.type_combination, text_status
ORDER BY total_posts DESC;

-- 8. 混排内容的媒体文件数量分析
SELECT 
    CASE 
        WHEN media_count = 1 THEN '单个媒体文件'
        WHEN media_count BETWEEN 2 AND 5 THEN '2-5个媒体文件'
        WHEN media_count BETWEEN 6 AND 10 THEN '6-10个媒体文件'
        WHEN media_count > 10 THEN '10个以上媒体文件'
        ELSE '未知'
    END AS media_count_range,
    CASE 
        WHEN has_text = 1 THEN '包含文字'
        ELSE '无文字'
    END AS text_status,
    COUNT(*) AS total_posts,
    COUNT(DISTINCT user_id) AS unique_users,
    AVG(avg_text_length) AS avg_text_length
FROM (
    SELECT 
        p.id,
        p.user_id,
        COUNT(pm.id) AS media_count,
        CASE WHEN p.content IS NOT NULL AND TRIM(p.content) != '' THEN 1 ELSE 0 END AS has_text,
        CHAR_LENGTH(TRIM(p.content)) AS avg_text_length
    FROM posts p
    LEFT JOIN post_medias pm ON p.id = pm.post_id AND pm.is_del = 0
    WHERE p.is_del = 0
    AND (
        p.content_type IN (2, 3, 4)
        OR pm.id IS NOT NULL
    )
    GROUP BY p.id, p.user_id, p.content
) AS post_media_summary
GROUP BY media_count_range, text_status
ORDER BY total_posts DESC;

-- 9. 混排内容的文字长度与媒体类型交叉分析
SELECT 
    CASE 
        WHEN CHAR_LENGTH(TRIM(p.content)) = 0 THEN '无文字'
        WHEN CHAR_LENGTH(TRIM(p.content)) BETWEEN 1 AND 20 THEN '1-20字符'
        WHEN CHAR_LENGTH(TRIM(p.content)) BETWEEN 21 AND 50 THEN '21-50字符'
        WHEN CHAR_LENGTH(TRIM(p.content)) BETWEEN 51 AND 100 THEN '51-100字符'
        WHEN CHAR_LENGTH(TRIM(p.content)) > 100 THEN '100字符以上'
        ELSE '未知'
    END AS text_length_range,
    media_summary.media_type_desc,
    COUNT(DISTINCT p.id) AS total_posts,
    COUNT(DISTINCT p.user_id) AS unique_users
FROM posts p
JOIN (
    SELECT 
        post_id,
        CASE 
            WHEN COUNT(CASE WHEN type = 1 THEN 1 END) > 0 AND COUNT(CASE WHEN type = 2 THEN 1 END) > 0 THEN '图片+视频'
            WHEN COUNT(CASE WHEN type = 1 THEN 1 END) > 0 THEN '仅图片'
            WHEN COUNT(CASE WHEN type = 2 THEN 1 END) > 0 THEN '仅视频'
            ELSE '其他'
        END AS media_type_desc,
        COUNT(*) AS media_count
    FROM post_medias 
    WHERE is_del = 0
    GROUP BY post_id
) AS media_summary ON p.id = media_summary.post_id
WHERE p.is_del = 0
GROUP BY text_length_range, media_summary.media_type_desc
ORDER BY total_posts DESC;

-- 10. 混排内容的时间趋势分析（按天）
SELECT 
    DATE(p.created_at) AS publish_date,
    CASE 
        WHEN media_summary.media_type_desc = '图片+视频' AND p.content IS NOT NULL AND TRIM(p.content) != '' THEN '图片+视频+文字'
        WHEN media_summary.media_type_desc = '仅图片' AND p.content IS NOT NULL AND TRIM(p.content) != '' THEN '图片+文字'
        WHEN media_summary.media_type_desc = '仅视频' AND p.content IS NOT NULL AND TRIM(p.content) != '' THEN '视频+文字'
        ELSE '其他混排'
    END AS mixed_content_type,
    COUNT(DISTINCT p.id) AS daily_posts,
    COUNT(DISTINCT p.user_id) AS daily_users
FROM posts p
JOIN (
    SELECT 
        post_id,
        CASE 
            WHEN COUNT(CASE WHEN type = 1 THEN 1 END) > 0 AND COUNT(CASE WHEN type = 2 THEN 1 END) > 0 THEN '图片+视频'
            WHEN COUNT(CASE WHEN type = 1 THEN 1 END) > 0 THEN '仅图片'
            WHEN COUNT(CASE WHEN type = 2 THEN 1 END) > 0 THEN '仅视频'
            ELSE '其他'
        END AS media_type_desc
    FROM post_medias 
    WHERE is_del = 0
    GROUP BY post_id
) AS media_summary ON p.id = media_summary.post_id
WHERE p.is_del = 0
AND p.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(p.created_at), mixed_content_type
ORDER BY publish_date DESC, daily_posts DESC;

-- 11. 混排内容活跃创作者排行榜
SELECT 
    p.user_id,
    COUNT(DISTINCT p.id) AS total_mixed_posts,
    COUNT(DISTINCT CASE WHEN media_summary.media_type_desc = '图片+视频' THEN p.id END) AS image_video_posts,
    COUNT(DISTINCT CASE WHEN media_summary.media_type_desc = '仅图片' AND p.content IS NOT NULL AND TRIM(p.content) != '' THEN p.id END) AS image_text_posts,
    COUNT(DISTINCT CASE WHEN media_summary.media_type_desc = '仅视频' AND p.content IS NOT NULL AND TRIM(p.content) != '' THEN p.id END) AS video_text_posts,
    COUNT(CASE WHEN p.status = 2 THEN 1 END) AS approved_posts,
    MIN(p.created_at) AS first_post_time,
    MAX(p.created_at) AS latest_post_time
FROM posts p
JOIN (
    SELECT 
        post_id,
        CASE 
            WHEN COUNT(CASE WHEN type = 1 THEN 1 END) > 0 AND COUNT(CASE WHEN type = 2 THEN 1 END) > 0 THEN '图片+视频'
            WHEN COUNT(CASE WHEN type = 1 THEN 1 END) > 0 THEN '仅图片'
            WHEN COUNT(CASE WHEN type = 2 THEN 1 END) > 0 THEN '仅视频'
            ELSE '其他'
        END AS media_type_desc
    FROM post_medias 
    WHERE is_del = 0
    GROUP BY post_id
) AS media_summary ON p.id = media_summary.post_id
WHERE p.is_del = 0
AND (
    p.content IS NOT NULL AND TRIM(p.content) != ''  -- 必须包含文字
)
GROUP BY p.user_id
HAVING total_mixed_posts >= 3  -- 至少发布3篇混排内容
ORDER BY total_mixed_posts DESC, approved_posts DESC
LIMIT 100; 