#!/usr/bin/env python3
import pymysql
import logging
import redis
from datetime import datetime
from redis.cluster import RedisCluster
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("redis_check_invitations.log"),
        logging.StreamHandler()
    ]
)

# 媒体用户数据库配置
USER_DB = {
    'host': 'xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user'
}

def get_db_config(**kwargs):
    """
    Helper function to prepare database configuration.
    Returns the database configuration dictionary with cursor settings.

    Args:
        **kwargs: Database connection parameters (host, user, password, database)

    Returns:
        dict: Database configuration with cursor settings
    """
    # Create a copy of the input config
    config = dict(kwargs)

    # Add cursor class configuration for dictionary results
    config['cursorclass'] = pymysql.cursors.DictCursor

    # Add other common configuration parameters if needed
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False

    return config

def get_redis_connection():
    """
    Get Redis connection instance

    Returns:
        redis.Redis: Redis connection instance
    """
    try:
        # Redis connection parameters
        host = "xme-prod-task.otyftu.clustercfg.apse1.cache.amazonaws.com"
        port = 6379
        
        # Create RedisCluster connection
        redis_conn = RedisCluster(
            host=host,
            port=port,
            decode_responses=True,
            ssl=False
        )
        
        redis_conn.ping()
        logging.info("成功连接到Redis集群")
        return redis_conn
    except Exception as e:
        logging.error(f"连接Redis时出错: {str(e)}")
        return None

def get_database_invitations():
    """
    从数据库获取邀请数据
    
    Returns:
        dict: 以 (from_uid, to_uid) 为key的邀请数据字典
    """
    user_db_config = get_db_config(**USER_DB)
    
    try:
        conn = pymysql.connect(**user_db_config)
        db_invitations = {}
        
        try:
            with conn.cursor() as cursor:
                query = """
                SELECT DISTINCT from_uid, to_uid, created_time
                FROM user_invite_face_record
                WHERE face_type = 2
                AND from_uid IS NOT NULL
                """
                cursor.execute(query)
                results = cursor.fetchall()
                
                for row in results:
                    key = (row['from_uid'], row['to_uid'])
                    db_invitations[key] = row
                    
                logging.info(f"从数据库获取到 {len(db_invitations)} 条邀请记录")
        finally:
            conn.close()
            
        return db_invitations
    except Exception as e:
        logging.error(f"获取数据库邀请数据时出错: {str(e)}")
        return {}

def get_redis_invitations(redis_conn):
    """
    从Redis获取邀请数据
    
    Args:
        redis_conn: Redis连接实例
        
    Returns:
        dict: Redis中的邀请数据
    """
    redis_invitations = {}
    redis_invite_mappings = {}
    
    try:
        # 获取所有friend:expedition:end:time:* 键
        pattern = "friend:expedition:end:time:*"
        keys = []
        
        # 由于是集群模式，需要从所有节点获取键
        for key in redis_conn.scan_iter(match=pattern):
            keys.append(key)
        
        logging.info(f"找到 {len(keys)} 个邀请者的Redis键")
        
        for key in keys:
            # 从键名中提取from_uid
            from_uid = key.split(':')[-1]
            
            # 获取该邀请者的所有被邀请者
            invited_users = redis_conn.zrange(key, 0, -1, withscores=True)
            
            for to_uid, timestamp in invited_users:
                redis_key = (from_uid, str(to_uid))
                redis_invitations[redis_key] = {
                    'from_uid': from_uid,
                    'to_uid': str(to_uid),
                    'timestamp': int(timestamp)
                }
        
        # 获取所有invite:user:* 键的映射
        invite_pattern = "invite:user:*"
        invite_keys = []
        
        for key in redis_conn.scan_iter(match=invite_pattern):
            invite_keys.append(key)
            
        logging.info(f"找到 {len(invite_keys)} 个被邀请者的Redis键")
        
        for key in invite_keys:
            to_uid = key.split(':')[-1]
            from_uid = redis_conn.get(key)
            if from_uid:
                redis_invite_mappings[to_uid] = from_uid
        
        logging.info(f"从Redis获取到 {len(redis_invitations)} 条邀请记录")
        logging.info(f"从Redis获取到 {len(redis_invite_mappings)} 条邀请映射记录")
        
        return redis_invitations, redis_invite_mappings
    except Exception as e:
        logging.error(f"获取Redis邀请数据时出错: {str(e)}")
        return {}, {}

def compare_data(db_invitations, redis_invitations, redis_invite_mappings):
    """
    比较数据库和Redis中的邀请数据
    
    Args:
        db_invitations: 数据库中的邀请数据
        redis_invitations: Redis中的邀请数据
        redis_invite_mappings: Redis中的邀请映射数据
    """
    logging.info("=" * 60)
    logging.info("开始数据一致性检查")
    logging.info("=" * 60)
    
    # 统计信息
    stats = {
        'db_total': len(db_invitations),
        'redis_total': len(redis_invitations),
        'missing_in_redis': 0,
        'extra_in_redis': 0,
        'timestamp_mismatch': 0,
        'invite_mapping_missing': 0,
        'invite_mapping_mismatch': 0
    }
    
    missing_in_redis = []
    extra_in_redis = []
    timestamp_mismatches = []
    invite_mapping_issues = []
    
    # 检查数据库中的记录是否在Redis中存在
    for db_key, db_record in db_invitations.items():
        from_uid, to_uid = db_key
        redis_key = (str(from_uid), str(to_uid))
        
        if redis_key not in redis_invitations:
            missing_in_redis.append(db_key)
            stats['missing_in_redis'] += 1
        else:
            # 检查时间戳是否匹配
            redis_record = redis_invitations[redis_key]
            
            if isinstance(db_record['created_time'], datetime):
                db_timestamp = int(db_record['created_time'].timestamp() * 1000)
            else:
                db_timestamp = int(datetime.now().timestamp() * 1000)
            
            if abs(db_timestamp - redis_record['timestamp']) > 1000:  # 允许1秒的误差
                timestamp_mismatches.append({
                    'key': db_key,
                    'db_timestamp': db_timestamp,
                    'redis_timestamp': redis_record['timestamp']
                })
                stats['timestamp_mismatch'] += 1
        
        # 检查invite:user映射
        to_uid_str = str(to_uid)
        if to_uid_str not in redis_invite_mappings:
            invite_mapping_issues.append({
                'type': 'missing',
                'to_uid': to_uid_str,
                'expected_from_uid': str(from_uid)
            })
            stats['invite_mapping_missing'] += 1
        elif redis_invite_mappings[to_uid_str] != str(from_uid):
            invite_mapping_issues.append({
                'type': 'mismatch',
                'to_uid': to_uid_str,
                'expected_from_uid': str(from_uid),
                'actual_from_uid': redis_invite_mappings[to_uid_str]
            })
            stats['invite_mapping_mismatch'] += 1
    
    # 检查Redis中多余的记录
    for redis_key in redis_invitations:
        from_uid, to_uid = redis_key
        db_key = (int(from_uid), int(to_uid))
        
        if db_key not in db_invitations:
            extra_in_redis.append(redis_key)
            stats['extra_in_redis'] += 1
    
    # 输出检查结果
    logging.info(f"数据库总记录数: {stats['db_total']}")
    logging.info(f"Redis总记录数: {stats['redis_total']}")
    logging.info(f"Redis中缺失的记录数: {stats['missing_in_redis']}")
    logging.info(f"Redis中多余的记录数: {stats['extra_in_redis']}")
    logging.info(f"时间戳不匹配的记录数: {stats['timestamp_mismatch']}")
    logging.info(f"邀请映射缺失的记录数: {stats['invite_mapping_missing']}")
    logging.info(f"邀请映射不匹配的记录数: {stats['invite_mapping_mismatch']}")
    
    # 详细报告前10个问题
    if missing_in_redis:
        logging.warning("Redis中缺失的记录示例 (前10个):")
        for i, key in enumerate(missing_in_redis[:10]):
            logging.warning(f"  {i+1}. from_uid: {key[0]}, to_uid: {key[1]}")
    
    if extra_in_redis:
        logging.warning("Redis中多余的记录示例 (前10个):")
        for i, key in enumerate(extra_in_redis[:10]):
            logging.warning(f"  {i+1}. from_uid: {key[0]}, to_uid: {key[1]}")
    
    if timestamp_mismatches:
        logging.warning("时间戳不匹配的记录示例 (前10个):")
        for i, mismatch in enumerate(timestamp_mismatches[:10]):
            logging.warning(f"  {i+1}. key: {mismatch['key']}, DB: {mismatch['db_timestamp']}, Redis: {mismatch['redis_timestamp']}")
    
    if invite_mapping_issues:
        logging.warning("邀请映射问题示例 (前10个):")
        for i, issue in enumerate(invite_mapping_issues[:10]):
            if issue['type'] == 'missing':
                logging.warning(f"  {i+1}. 缺失映射 - to_uid: {issue['to_uid']}, 期望from_uid: {issue['expected_from_uid']}")
            else:
                logging.warning(f"  {i+1}. 映射不匹配 - to_uid: {issue['to_uid']}, 期望: {issue['expected_from_uid']}, 实际: {issue['actual_from_uid']}")
    
    # 计算数据一致性百分比
    if stats['db_total'] > 0:
        consistency_rate = ((stats['db_total'] - stats['missing_in_redis'] - stats['timestamp_mismatch']) / stats['db_total']) * 100
        logging.info(f"数据一致性: {consistency_rate:.2f}%")
    
    return stats

def check_redis_invitations():
    """
    主检查函数
    """
    logging.info("开始Redis邀请数据检查")
    
    # 获取数据库数据
    db_invitations = get_database_invitations()
    if not db_invitations:
        logging.error("无法获取数据库数据，检查终止")
        return
    
    # 连接Redis
    redis_conn = get_redis_connection()
    if not redis_conn:
        logging.error("无法连接Redis，检查终止")
        return
    
    # 获取Redis数据
    redis_invitations, redis_invite_mappings = get_redis_invitations(redis_conn)
    
    # 比较数据
    stats = compare_data(db_invitations, redis_invitations, redis_invite_mappings)
    
    # 生成检查报告
    report = {
        'check_time': datetime.now().isoformat(),
        'stats': stats
    }
    
    with open('redis_check_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logging.info("检查完成，详细报告已保存到 redis_check_report.json")

def main():
    """
    主函数
    """
    logging.info("启动Redis邀请数据检查脚本")
    check_redis_invitations()
    logging.info("Redis数据检查过程完成")

if __name__ == "__main__":
    main() 