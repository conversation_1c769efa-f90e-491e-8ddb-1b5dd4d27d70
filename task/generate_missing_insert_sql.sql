-- 生成补充缺失数据的INSERT语句
-- 找出主表中有但分表中没有的记录，生成INSERT语句

-- 方法1: 生成INSERT语句（推荐）
SELECT CONCAT(
    'INSERT INTO user_hourly_ep_20250630 ',
    '(user_id, expedition_id, hourly, hour_ep, bonus_ep, create_time, update_time, delete_status) ',
    'VALUES (',
    main.user_id, ', ',
    main.expedition_id, ', ',
    '''', main.hourly, ''', ',
    main.hour_ep, ', ',
    main.bonus_ep, ', ',
    '''', main.create_time, ''', ',
    '''', main.update_time, ''', ',
    main.delete_status,
    ');'
) as insert_sql
FROM user_hourly_ep main
WHERE DATE(main.hourly) = '2025-06-30' 
  AND main.delete_status = 0
  AND NOT EXISTS (
    SELECT 1 
    FROM user_hourly_ep_20250630 shard 
    WHERE shard.user_id = main.user_id 
      AND shard.expedition_id = main.expedition_id
      AND shard.hourly = main.hourly
      AND shard.delete_status = 0
  )
ORDER BY main.user_id, main.hourly;

-- 方法2: 使用INSERT ... SELECT（更简洁）
/*
INSERT INTO user_hourly_ep_20250630 
(user_id, expedition_id, hourly, hour_ep, bonus_ep, create_time, update_time, delete_status)
SELECT 
    main.user_id,
    main.expedition_id, 
    main.hourly,
    main.hour_ep,
    main.bonus_ep,
    main.create_time,
    main.update_time,
    main.delete_status
FROM user_hourly_ep main
WHERE DATE(main.hourly) = '2025-06-30' 
  AND main.delete_status = 0
  AND NOT EXISTS (
    SELECT 1 
    FROM user_hourly_ep_20250630 shard 
    WHERE shard.user_id = main.user_id 
      AND shard.expedition_id = main.expedition_id
      AND shard.hourly = main.hourly
      AND shard.delete_status = 0
  );
*/

-- 方法3: 先查看有多少条缺失记录
/*
SELECT COUNT(*) as missing_records_count
FROM user_hourly_ep main
WHERE DATE(main.hourly) = '2025-06-30' 
  AND main.delete_status = 0
  AND NOT EXISTS (
    SELECT 1 
    FROM user_hourly_ep_20250630 shard 
    WHERE shard.user_id = main.user_id 
      AND shard.expedition_id = main.expedition_id
      AND shard.hourly = main.hourly
      AND shard.delete_status = 0
  );
*/

-- 方法4: 查看缺失记录的详细信息
/*
SELECT 
    main.user_id,
    main.expedition_id,
    main.hourly,
    main.hour_ep,
    main.bonus_ep,
    main.create_time
FROM user_hourly_ep main
WHERE DATE(main.hourly) = '2025-06-30' 
  AND main.delete_status = 0
  AND NOT EXISTS (
    SELECT 1 
    FROM user_hourly_ep_20250630 shard 
    WHERE shard.user_id = main.user_id 
      AND shard.expedition_id = main.expedition_id
      AND shard.hourly = main.hourly
      AND shard.delete_status = 0
  )
ORDER BY main.user_id, main.hourly;
*/

-- 方法5: 安全的INSERT ON DUPLICATE KEY UPDATE（推荐用于生产）
/*
INSERT INTO user_hourly_ep_20250630 
(user_id, expedition_id, hourly, hour_ep, bonus_ep, create_time, update_time, delete_status)
SELECT 
    main.user_id,
    main.expedition_id, 
    main.hourly,
    main.hour_ep,
    main.bonus_ep,
    NOW() as create_time,
    NOW() as update_time,
    0 as delete_status
FROM user_hourly_ep main
WHERE DATE(main.hourly) = '2025-06-30' 
  AND main.delete_status = 0
  AND NOT EXISTS (
    SELECT 1 
    FROM user_hourly_ep_20250630 shard 
    WHERE shard.user_id = main.user_id 
      AND shard.expedition_id = main.expedition_id
      AND shard.hourly = main.hourly
      AND shard.delete_status = 0
  )
ON DUPLICATE KEY UPDATE
    hour_ep = VALUES(hour_ep),
    bonus_ep = VALUES(bonus_ep),
    update_time = NOW();
*/ 