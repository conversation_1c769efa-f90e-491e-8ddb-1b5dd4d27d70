#!/usr/bin/env python3
import pymysql
import logging
import time
from datetime import datetime
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("shard_data_migration.log"),
        logging.StreamHandler()
    ]
)

DB_CONFIG = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'content_behavior',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': False
}

# 数据库配置
"""
DB_CONFIG = {
    'host': 'xme-prod-rds-analysis.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-analysis-user',
    'password': 'k8xet*5YKT',
    'database': 'content_behavior',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': False
}
"""

def get_db_connection():
    """
    获取数据库连接
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logging.error(f"连接数据库失败: {str(e)}")
        return None

def create_shard_tables(conn):
    """
    创建所有分表（0-1023）
    """
    logging.info("开始创建分表...")
    
    with conn.cursor() as cursor:
        for i in range(1024):
            try:
                # 创建user_like分表
                like_table_sql = f"""
                CREATE TABLE IF NOT EXISTS `user_like_{i}` (
                  `id` bigint NOT NULL AUTO_INCREMENT,
                  `user_id` bigint NOT NULL,
                  `content_id` bigint NOT NULL,
                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `idx_user_like_user_content` (`user_id`,`content_id`),
                  KEY `idx_user_id` (`user_id`),
                  KEY `idx_content_id` (`content_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
                """
                cursor.execute(like_table_sql)
                
                # 创建user_read分表
                read_table_sql = f"""
                CREATE TABLE IF NOT EXISTS `user_read_{i}` (
                  `id` bigint NOT NULL AUTO_INCREMENT,
                  `user_id` bigint NOT NULL,
                  `content_id` bigint NOT NULL,
                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `idx_user_read_user_content` (`user_id`,`content_id`),
                  KEY `idx_user_id` (`user_id`),
                  KEY `idx_content_id` (`content_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
                """
                cursor.execute(read_table_sql)
                
                if (i + 1) % 100 == 0:
                    logging.info(f"已创建 {i + 1}/1024 张分表")
                    
            except Exception as e:
                logging.error(f"创建分表 {i} 失败: {str(e)}")
                continue
    
    conn.commit()
    logging.info("分表创建完成")

def get_table_count(conn, table_name):
    """
    获取表的记录数量
    """
    try:
        with conn.cursor() as cursor:
            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
            result = cursor.fetchone()
            return result['count']
    except Exception as e:
        logging.error(f"获取表 {table_name} 记录数失败: {str(e)}")
        return 0

def migrate_user_like_data(conn, batch_size=1000):
    """
    迁移user_like表数据到分表
    """
    logging.info("开始迁移user_like表数据...")
    
    # 获取总记录数
    total_count = get_table_count(conn, 'user_like')
    logging.info(f"user_like表总记录数: {total_count}")
    
    if total_count == 0:
        logging.warning("user_like表为空，跳过迁移")
        return
    
    processed = 0
    last_id = 0
    migration_stats = {i: 0 for i in range(1024)}
    
    while processed < total_count:
        try:
            with conn.cursor() as cursor:
                # 分批读取数据
                query = f"""
                SELECT id, user_id, content_id, created_at, updated_at 
                FROM user_like 
                WHERE id > {last_id} 
                ORDER BY id 
                LIMIT {batch_size}
                """
                cursor.execute(query)
                batch_data = cursor.fetchall()
                
                if not batch_data:
                    break
                
                # 按分表分组数据
                shard_data = {}
                for row in batch_data:
                    shard_id = row['user_id'] % 1024
                    if shard_id not in shard_data:
                        shard_data[shard_id] = []
                    shard_data[shard_id].append(row)
                    last_id = max(last_id, row['id'])
                
                # 批量插入到各个分表
                for shard_id, data in shard_data.items():
                    insert_sql = f"""
                    INSERT IGNORE INTO user_like_{shard_id} 
                    (user_id, content_id, created_at, updated_at) 
                    VALUES (%s, %s, %s, %s)
                    """
                    
                    values = [(row['user_id'], row['content_id'], 
                             row['created_at'], row['updated_at']) for row in data]
                    
                    cursor.executemany(insert_sql, values)
                    migration_stats[shard_id] += len(data)
                
                conn.commit()
                processed += len(batch_data)
                
                # 进度日志
                if processed % 10000 == 0 or processed >= total_count:
                    progress = (processed / total_count) * 100
                    logging.info(f"user_like迁移进度: {processed}/{total_count} ({progress:.2f}%)")
                    
        except Exception as e:
            logging.error(f"迁移user_like数据时出错: {str(e)}")
            conn.rollback()
            time.sleep(1)
            continue
    
    # 输出迁移统计
    logging.info("user_like表迁移完成，分表统计:")
    for shard_id in range(1024):
        if migration_stats[shard_id] > 0:
            logging.info(f"  user_like_{shard_id}: {migration_stats[shard_id]} 条记录")

def migrate_user_read_data(conn, batch_size=1000):
    """
    迁移user_read表数据到分表
    """
    logging.info("开始迁移user_read表数据...")
    
    # 获取总记录数
    total_count = get_table_count(conn, 'user_read')
    logging.info(f"user_read表总记录数: {total_count}")
    
    if total_count == 0:
        logging.warning("user_read表为空，跳过迁移")
        return
    
    processed = 0
    last_id = 0
    migration_stats = {i: 0 for i in range(1024)}
    
    while processed < total_count:
        try:
            with conn.cursor() as cursor:
                # 分批读取数据
                query = f"""
                SELECT id, user_id, content_id, created_at, updated_at 
                FROM user_read 
                WHERE id > {last_id} 
                ORDER BY id 
                LIMIT {batch_size}
                """
                cursor.execute(query)
                batch_data = cursor.fetchall()
                
                if not batch_data:
                    break
                
                # 按分表分组数据
                shard_data = {}
                for row in batch_data:
                    shard_id = row['user_id'] % 1024
                    if shard_id not in shard_data:
                        shard_data[shard_id] = []
                    shard_data[shard_id].append(row)
                    last_id = max(last_id, row['id'])
                
                # 批量插入到各个分表
                for shard_id, data in shard_data.items():
                    insert_sql = f"""
                    INSERT IGNORE INTO user_read_{shard_id} 
                    (user_id, content_id, created_at, updated_at) 
                    VALUES (%s, %s, %s, %s)
                    """
                    
                    values = [(row['user_id'], row['content_id'], 
                             row['created_at'], row['updated_at']) for row in data]
                    
                    cursor.executemany(insert_sql, values)
                    migration_stats[shard_id] += len(data)
                
                conn.commit()
                processed += len(batch_data)
                
                # 进度日志
                if processed % 10000 == 0 or processed >= total_count:
                    progress = (processed / total_count) * 100
                    logging.info(f"user_read迁移进度: {processed}/{total_count} ({progress:.2f}%)")
                    
        except Exception as e:
            logging.error(f"迁移user_read数据时出错: {str(e)}")
            conn.rollback()
            time.sleep(1)
            continue
    
    # 输出迁移统计
    logging.info("user_read表迁移完成，分表统计:")
    for shard_id in range(1024):
        if migration_stats[shard_id] > 0:
            logging.info(f"  user_read_{shard_id}: {migration_stats[shard_id]} 条记录")

def verify_migration(conn):
    """
    验证迁移结果
    """
    logging.info("开始验证迁移结果...")
    
    try:
        with conn.cursor() as cursor:
            # 统计原表记录数
            cursor.execute("SELECT COUNT(*) as count FROM user_like")
            original_like_count = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM user_read")
            original_read_count = cursor.fetchone()['count']
            
            # 统计分表记录数
            total_like_migrated = 0
            total_read_migrated = 0
            
            for i in range(1024):
                cursor.execute(f"SELECT COUNT(*) as count FROM user_like_{i}")
                total_like_migrated += cursor.fetchone()['count']
                
                cursor.execute(f"SELECT COUNT(*) as count FROM user_read_{i}")
                total_read_migrated += cursor.fetchone()['count']
            
            # 输出验证结果
            logging.info("=== 迁移验证结果 ===")
            logging.info(f"user_like原表记录数: {original_like_count}")
            logging.info(f"user_like分表总记录数: {total_like_migrated}")
            logging.info(f"user_like数据完整性: {'✓ 通过' if original_like_count == total_like_migrated else '✗ 失败'}")
            
            logging.info(f"user_read原表记录数: {original_read_count}")
            logging.info(f"user_read分表总记录数: {total_read_migrated}")
            logging.info(f"user_read数据完整性: {'✓ 通过' if original_read_count == total_read_migrated else '✗ 失败'}")
            
            # 抽样验证数据一致性
            logging.info("进行抽样数据一致性验证...")
            cursor.execute("SELECT user_id, content_id FROM user_like ORDER BY RAND() LIMIT 100")
            sample_likes = cursor.fetchall()
            
            for sample in sample_likes:
                user_id = sample['user_id']
                content_id = sample['content_id']
                shard_id = user_id % 1024
                
                cursor.execute(f"SELECT COUNT(*) as count FROM user_like_{shard_id} WHERE user_id = %s AND content_id = %s", 
                             (user_id, content_id))
                if cursor.fetchone()['count'] == 0:
                    logging.warning(f"数据一致性问题: user_like user_id={user_id}, content_id={content_id} 在分表中未找到")
            
            logging.info("验证完成")
            
    except Exception as e:
        logging.error(f"验证过程出错: {str(e)}")

def main():
    """
    主函数
    """
    logging.info("开始数据分表迁移任务")
    start_time = datetime.now()
    
    # 连接数据库
    conn = get_db_connection()
    if not conn:
        logging.error("无法连接数据库，退出程序")
        sys.exit(1)
    
    try:
        # 步骤1: 创建分表
        #create_shard_tables(conn)
        
        # 步骤2: 迁移user_like数据
        migrate_user_like_data(conn, batch_size=2000)
        
        # 步骤3: 迁移user_read数据
        migrate_user_read_data(conn, batch_size=2000)
        
        # 步骤4: 验证迁移结果
        verify_migration(conn)
        
        end_time = datetime.now()
        duration = end_time - start_time
        logging.info(f"数据迁移任务完成，总耗时: {duration}")
        
    except KeyboardInterrupt:
        logging.info("用户中断迁移任务")
        conn.rollback()
    except Exception as e:
        logging.error(f"迁移任务出错: {str(e)}")
        conn.rollback()
    finally:
        conn.close()
        logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 