-- 修改字段定义和更新数据 - push_message表 (0-255)
-- 生成时间: 2025-06-24 11:21:20.957748

-- 修改 created_time 字段默认值 (0-255)
ALTER TABLE media_message.push_message0 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message2 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message3 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message4 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message5 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message6 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message7 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message8 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message9 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message10 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message11 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message12 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message13 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message14 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message15 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message16 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message17 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message18 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message19 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message20 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message21 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message22 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message23 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message24 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message25 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message26 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message27 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message28 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message29 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message30 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message31 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message32 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message33 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message34 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message35 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message36 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message37 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message38 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message39 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message40 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message41 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message42 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message43 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message44 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message45 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message46 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message47 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message48 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message49 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message50 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message51 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message52 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message53 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message54 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message55 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message56 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message57 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message58 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message59 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message60 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message61 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message62 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message63 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message64 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message65 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message66 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message67 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message68 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message69 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message70 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message71 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message72 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message73 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message74 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message75 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message76 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message77 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message78 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message79 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message80 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message81 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message82 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message83 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message84 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message85 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message86 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message87 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message88 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message89 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message90 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message91 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message92 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message93 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message94 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message95 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message96 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message97 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message98 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message99 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message100 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message101 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message102 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message103 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message104 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message105 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message106 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message107 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message108 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message109 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message110 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message111 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message112 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message113 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message114 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message115 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message116 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message117 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message118 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message119 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message120 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message121 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message122 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message123 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message124 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message125 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message126 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message127 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message128 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message129 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message130 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message131 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message132 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message133 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message134 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message135 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message136 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message137 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message138 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message139 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message140 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message141 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message142 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message143 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message144 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message145 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message146 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message147 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message148 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message149 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message150 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message151 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message152 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message153 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message154 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message155 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message156 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message157 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message158 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message159 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message160 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message161 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message162 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message163 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message164 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message165 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message166 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message167 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message168 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message169 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message170 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message171 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message172 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message173 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message174 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message175 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message176 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message177 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message178 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message179 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message180 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message181 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message182 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message183 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message184 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message185 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message186 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message187 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message188 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message189 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message190 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message191 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message192 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message193 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message194 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message195 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message196 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message197 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message198 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message199 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message200 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message201 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message202 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message203 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message204 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message205 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message206 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message207 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message208 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message209 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message210 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message211 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message212 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message213 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message214 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message215 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message216 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message217 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message218 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message219 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message220 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message221 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message222 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message223 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message224 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message225 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message226 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message227 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message228 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message229 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message230 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message231 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message232 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message233 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message234 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message235 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message236 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message237 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message238 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message239 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message240 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message241 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message242 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message243 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message244 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message245 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message246 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message247 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message248 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message249 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message250 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message251 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message252 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message253 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message254 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message255 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 修改 updated_time 字段默认值 (0-255)
ALTER TABLE media_message.push_message0 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message2 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message3 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message4 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message5 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message6 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message7 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message8 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message9 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message10 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message11 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message12 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message13 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message14 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message15 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message16 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message17 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message18 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message19 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message20 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message21 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message22 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message23 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message24 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message25 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message26 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message27 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message28 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message29 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message30 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message31 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message32 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message33 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message34 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message35 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message36 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message37 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message38 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message39 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message40 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message41 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message42 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message43 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message44 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message45 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message46 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message47 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message48 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message49 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message50 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message51 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message52 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message53 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message54 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message55 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message56 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message57 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message58 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message59 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message60 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message61 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message62 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message63 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message64 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message65 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message66 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message67 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message68 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message69 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message70 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message71 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message72 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message73 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message74 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message75 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message76 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message77 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message78 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message79 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message80 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message81 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message82 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message83 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message84 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message85 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message86 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message87 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message88 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message89 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message90 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message91 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message92 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message93 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message94 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message95 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message96 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message97 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message98 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message99 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message100 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message101 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message102 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message103 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message104 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message105 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message106 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message107 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message108 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message109 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message110 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message111 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message112 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message113 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message114 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message115 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message116 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message117 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message118 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message119 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message120 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message121 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message122 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message123 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message124 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message125 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message126 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message127 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message128 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message129 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message130 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message131 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message132 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message133 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message134 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message135 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message136 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message137 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message138 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message139 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message140 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message141 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message142 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message143 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message144 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message145 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message146 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message147 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message148 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message149 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message150 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message151 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message152 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message153 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message154 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message155 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message156 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message157 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message158 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message159 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message160 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message161 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message162 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message163 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message164 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message165 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message166 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message167 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message168 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message169 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message170 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message171 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message172 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message173 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message174 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message175 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message176 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message177 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message178 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message179 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message180 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message181 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message182 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message183 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message184 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message185 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message186 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message187 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message188 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message189 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message190 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message191 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message192 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message193 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message194 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message195 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message196 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message197 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message198 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message199 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message200 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message201 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message202 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message203 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message204 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message205 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message206 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message207 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message208 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message209 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message210 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message211 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message212 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message213 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message214 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message215 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message216 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message217 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message218 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message219 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message220 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message221 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message222 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message223 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message224 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message225 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message226 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message227 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message228 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message229 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message230 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message231 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message232 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message233 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message234 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message235 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message236 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message237 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message238 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message239 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message240 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message241 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message242 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message243 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message244 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message245 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message246 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message247 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message248 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message249 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message250 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message251 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message252 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message253 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message254 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message255 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 更新空值数据 (0-255)
UPDATE media_message.push_message0 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message2 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message3 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message4 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message5 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message6 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message7 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message8 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message9 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message10 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message11 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message12 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message13 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message14 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message15 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message16 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message17 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message18 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message19 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message20 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message21 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message22 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message23 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message24 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message25 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message26 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message27 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message28 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message29 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message30 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message31 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message32 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message33 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message34 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message35 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message36 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message37 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message38 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message39 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message40 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message41 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message42 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message43 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message44 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message45 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message46 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message47 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message48 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message49 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message50 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message51 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message52 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message53 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message54 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message55 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message56 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message57 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message58 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message59 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message60 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message61 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message62 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message63 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message64 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message65 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message66 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message67 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message68 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message69 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message70 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message71 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message72 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message73 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message74 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message75 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message76 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message77 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message78 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message79 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message80 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message81 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message82 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message83 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message84 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message85 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message86 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message87 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message88 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message89 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message90 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message91 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message92 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message93 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message94 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message95 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message96 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message97 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message98 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message99 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message100 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message101 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message102 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message103 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message104 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message105 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message106 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message107 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message108 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message109 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message110 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message111 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message112 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message113 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message114 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message115 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message116 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message117 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message118 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message119 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message120 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message121 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message122 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message123 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message124 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message125 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message126 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message127 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message128 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message129 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message130 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message131 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message132 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message133 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message134 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message135 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message136 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message137 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message138 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message139 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message140 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message141 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message142 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message143 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message144 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message145 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message146 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message147 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message148 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message149 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message150 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message151 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message152 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message153 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message154 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message155 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message156 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message157 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message158 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message159 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message160 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message161 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message162 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message163 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message164 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message165 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message166 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message167 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message168 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message169 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message170 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message171 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message172 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message173 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message174 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message175 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message176 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message177 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message178 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message179 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message180 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message181 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message182 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message183 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message184 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message185 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message186 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message187 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message188 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message189 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message190 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message191 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message192 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message193 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message194 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message195 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message196 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message197 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message198 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message199 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message200 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message201 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message202 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message203 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message204 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message205 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message206 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message207 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message208 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message209 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message210 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message211 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message212 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message213 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message214 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message215 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message216 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message217 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message218 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message219 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message220 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message221 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message222 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message223 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message224 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message225 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message226 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message227 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message228 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message229 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message230 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message231 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message232 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message233 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message234 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message235 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message236 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message237 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message238 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message239 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message240 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message241 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message242 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message243 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message244 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message245 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message246 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message247 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message248 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message249 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message250 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message251 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message252 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message253 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message254 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message255 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;