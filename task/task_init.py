import pymysql
import logging
from datetime import datetime
import redis

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("task_init.log"),
        logging.StreamHandler()
    ]
)


# Database server configurations
# 媒体用户数据库配置
USER_DB = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_user'
}

# 任务数据库配置
TASK_DB = {
    'host': 'xme-envtest-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_task'
}

# UGC内容数据库配置
UGC_DB = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'ugc_hub'
}

# 用户行为数据库配置
BEHAVIOR_DB = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'content_behavior'
}

def get_db_config(**kwargs):
    """
    Helper function to prepare database configuration.
    Returns the database configuration dictionary with cursor settings.
    
    Args:
        **kwargs: Database connection parameters (host, user, password, database)
        
    Returns:
        dict: Database configuration with cursor settings
    """
    # Create a copy of the input config
    config = dict(kwargs)
    
    # Add cursor class configuration for dictionary results
    config['cursorclass'] = pymysql.cursors.DictCursor
    
    # Add other common configuration parameters if needed
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    
    return config

# Redis connection setup
def get_redis_connection():
    """
    Get Redis connection instance
    
    Returns:
        redis.Redis: Redis connection instance
    """
    try:
        # Redis connection parameters - adjust these as needed for your environment
        redis_host = 'xme-test-task-otyftu.serverless.apse1.cache.amazonaws.com'
        redis_port = 6379
        redis_db = 0
        
        # Create Redis connection
        redis_conn = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=True,
            ssl=True,
            ssl_cert_reqs=None  
            # Return strings instead of bytes
        )
        
        # Test connection
        redis_conn.ping()
        return redis_conn
    except Exception as e:
        logging.error(f"Error connecting to Redis: {str(e)}")
        return None

def initialize_user_energy():
    """
    Initialize user base energy based on email and phone verification status.
    - Fetches verified users from media_user.client_user
    - Inserts records into media_task.user_task_record and media_task.ep_records
    - Ensures no duplicate entries
    """
    # Connect to media_user database
    user_db_config = get_db_config(**USER_DB)
    
    # Connect to media_task database
    task_db_config = get_db_config(**TASK_DB)
    
    # Task configuration
    base_energy_amount = 200  # Base energy points for verification
    
    try:
        # Step 1: Fetch verified users from media_user database
        user_conn = pymysql.connect(**user_db_config)
        verified_users = []
        
        try:
            with user_conn.cursor() as cursor:
                # Get users with verified email or phone
                query = """
                SELECT uid, email_verify, phone_verify 
                FROM client_user 
                WHERE email_verify = 1 OR phone_verify = 1
                """
                cursor.execute(query)
                verified_users = cursor.fetchall()
                logging.info(f"Found {len(verified_users)} verified users")
        finally:
            user_conn.close()
        
        if not verified_users:
            logging.info("No verified users found. Exiting.")
            return
        
        # Step 2: Process verified users and update media_task database
        task_conn = pymysql.connect(**task_db_config)
        
        try:
            with task_conn.cursor() as cursor:
                for user in verified_users:
                    user_id = user['uid']
                    email_verified = user['email_verify'] == 1
                    phone_verified = user['phone_verify'] == 1
                    
                    # 处理邮箱验证
                    if email_verified:
                        try:
                            # 为邮箱验证单独授予能量点数
                            success, _ = award_energy_points(
                                cursor, 
                                user_id, 
                                '10006',  # 邮箱验证专用任务代码
                                base_energy_amount, 
                                '1',
                                'verification'
                            )
                            
                            if success:
                                task_conn.commit()
                                logging.info(f"Awarded {base_energy_amount} energy points to user {user_id} for email verification")
                            
                        except Exception as e:
                            task_conn.rollback()
                            logging.error(f"Error processing email verification for user {user_id}: {str(e)}")
                    
                    # 处理手机验证
                    if phone_verified:
                        try:
                            # 为手机验证单独授予能量点数
                            success, _ = award_energy_points(
                                cursor, 
                                user_id, 
                                '10005',  # 手机验证专用任务代码
                                base_energy_amount, 
                                '1',
                                'verification'
                            )
                            
                            if success:
                                task_conn.commit()
                                logging.info(f"Awarded {base_energy_amount} energy points to user {user_id} for phone verification")
                            
                        except Exception as e:
                            task_conn.rollback()
                            logging.error(f"Error processing phone verification for user {user_id}: {str(e)}")
        finally:
            task_conn.close()
            
        logging.info("User energy initialization completed")
        
    except Exception as e:
        logging.error(f"Error in initialize_user_energy: {str(e)}")

def initialize_ugc_energy():
    """
    Initialize user base energy for original content (text + image) posts.
    - Fetches approved posts with images AND text from ugc_hub.posts
    - Requires text content to be at least 15 characters long
    - Awards 150 base energy points for each user's first approved post
    - Inserts records into media_task.user_task_record and media_task.ep_records
    - Ensures no duplicate entries
    """
    # Connect to ugc_hub database
    ugc_db_config = get_db_config(**UGC_DB)
    
    # Connect to media_task database
    task_db_config = get_db_config(**TASK_DB)
    
    # Task configuration
    task_code = '10002'
    base_energy_amount = 150  # Base energy points for first UGC post
    min_text_length = 15  # Minimum text length requirement
    
    try:
        # Step 1: Fetch users with approved posts containing both images and text from ugc_hub database
        ugc_conn = pymysql.connect(**ugc_db_config)
        eligible_users = {}  # Using dict to track unique users
        
        try:
            with ugc_conn.cursor() as cursor:
                # Get users with approved posts containing both images and text
                # Status 2 = approved, content_type 4 = mixed content (text+media), post_type 1 = original
                # Also include content_type 2 (image) but check that content is not empty
                query = """
                SELECT DISTINCT user_id, id, content, content_type
                FROM posts 
                WHERE status = 2 
                AND ((content_type = 4) OR (content_type = 2 AND content != '' AND content IS NOT NULL))
                AND post_type = 1
                AND is_del = 0
                """
                cursor.execute(query)
                posts = cursor.fetchall()
                
                # Verify each post has both text and images, and text is at least 15 characters
                for post in posts:
                    user_id = post['user_id']
                    content = post['content']
                    
                    if content and content.strip():
                        # Remove hashtag topics (patterns like #xxx) from content
                        import re
                        cleaned_content = re.sub(r'#\S+', '', content)
                        cleaned_content = cleaned_content.strip()
                        
                        # Ensure cleaned content has at least 15 characters
                        if cleaned_content and len(cleaned_content) >= min_text_length:
                            # Use user_id as key to ensure uniqueness
                            eligible_users[user_id] = {'user_id': user_id}
                
                # Convert dictionary values to list for processing
                eligible_users = list(eligible_users.values())
                logging.info(f"Found {len(eligible_users)} unique users with approved posts containing text (min {min_text_length} chars) and images")
        finally:
            ugc_conn.close()
        
        if not eligible_users:
            logging.info("No eligible users found. Exiting.")
            return
        
        # Step 2: Process eligible users and update media_task database
        task_conn = pymysql.connect(**task_db_config)
        
        try:
            with task_conn.cursor() as cursor:
                for user in eligible_users:
                    user_id = user['user_id']
                    
                    # Begin transaction
                    try:
                        # Award energy points using the common function
                        success, _ = award_energy_points(
                            cursor, 
                            user_id, 
                            task_code, 
                            base_energy_amount, 
                            '1',
                            'first UGC post'
                        )
                        
                        if success:
                            task_conn.commit()
                        
                    except Exception as e:
                        task_conn.rollback()
                        logging.error(f"Error processing user {user_id}: {str(e)}")
        finally:
            task_conn.close()
            
        logging.info("UGC energy initialization completed")
        
    except Exception as e:
        logging.error(f"Error in initialize_ugc_energy: {str(e)}")

def initialize_interest_energy():
    """
    Initialize user base energy for selecting interest preferences.
    - Fetches users who have selected at least 3 interests from content_behavior.user_interests
    - Awards 100 base energy points for each user who has completed interest selection
    - Inserts records into media_task.user_task_record and media_task.ep_records
    - Ensures no duplicate entries
    """
    # Connect to content_behavior database
    behavior_db_config = get_db_config(**BEHAVIOR_DB)
    
    # Connect to media_task database
    task_db_config = get_db_config(**TASK_DB)
    
    # Task configuration
    task_code = '10001'
    base_energy_amount = 100  # Base energy points for interest selection
    
    try:
        # Step 1: Fetch users who have selected at least 3 interests from content_behavior database
        behavior_conn = pymysql.connect(**behavior_db_config)
        eligible_users = []
        
        try:
            with behavior_conn.cursor() as cursor:
                # Get users who have selected interests
                query = """
                SELECT user_id, interest_ids
                FROM user_interests
                WHERE interest_ids != '-1' AND interest_ids IS NOT NULL
                """
                cursor.execute(query)
                users_with_interests = cursor.fetchall()
                
                # Filter users with at least 3 interests
                for user in users_with_interests:
                    interests = user['interest_ids'].split(',')
                    if len(interests) >= 3:
                        eligible_users.append(user)
                
                logging.info(f"Found {len(eligible_users)} users with at least 3 interests selected")
        finally:
            behavior_conn.close()
        
        if not eligible_users:
            logging.info("No eligible users found for interest selection bonus. Exiting.")
            return
        
        # Step 2: Process eligible users and update media_task database
        task_conn = pymysql.connect(**task_db_config)
        
        try:
            with task_conn.cursor() as cursor:
                for user in eligible_users:
                    user_id = user['user_id']
                    
                    # Begin transaction
                    try:
                        # Award energy points using the common function
                        success, _ = award_energy_points(
                            cursor, 
                            user_id, 
                            task_code, 
                            base_energy_amount, 
                            '1',
                            'interest selection'
                        )
                        
                        if success:
                            task_conn.commit()
                        
                    except Exception as e:
                        task_conn.rollback()
                        logging.error(f"Error processing user {user_id}: {str(e)}")
        finally:
            task_conn.close()
            
        logging.info("Interest selection energy initialization completed")
        
    except Exception as e:
        logging.error(f"Error in initialize_interest_energy: {str(e)}")

def initialize_face_invite_energy():
    """
    Initialize user base energy for face verification invitations.
    - Fetches successful face verification invitations from media_user.user_invite_face_record
    - Awards energy points to users who successfully invited others for face verification
    - Inserts records into media_task.user_task_record and media_task.ep_records
    - Ensures no duplicate entries
    """
    # Connect to media_user database
    user_db_config = get_db_config(**USER_DB)
    
    # Connect to media_task database
    task_db_config = get_db_config(**TASK_DB)
    
    # Task configuration
    task_code = '10003'
    base_energy_amount = 200  # Base energy points for face verification invitation
    
    try:
        # Step 1: Fetch successful face verification invitations from media_user database
        user_conn = pymysql.connect(**user_db_config)
        eligible_users = []
        
        try:
            with user_conn.cursor() as cursor:
                # Get users who have successfully invited others for face verification
                # face_type = 2 means invited verification, amount_status = 2 means confirmed
                query = """
                SELECT DISTINCT from_uid, to_uid, created_time
                FROM user_invite_face_record 
                WHERE face_type = 2 
                AND amount_status = 2 
                AND from_uid IS NOT NULL
                """
                cursor.execute(query)
                eligible_users = cursor.fetchall()
                logging.info(f"Found {len(eligible_users)} users who successfully invited others for face verification")
        finally:
            user_conn.close()
        
        if not eligible_users:
            logging.info("No eligible users found for face invitation bonus. Exiting.")
            return
        
        # Step 2: Store invitation data in Redis
        redis_conn = get_redis_connection()
        if redis_conn:
            try:
                for user in eligible_users:
                    inviter_uid = user['from_uid']
                    invited_uid = user['to_uid']
                    # Convert timestamp to 13 digits (milliseconds precision)
                    if isinstance(user['created_time'], datetime):
                        # Get seconds timestamp and multiply by 1000 to get milliseconds
                        timestamp = int(user['created_time'].timestamp() * 1000)
                    else:
                        # Use current time in milliseconds
                        timestamp = int(datetime.now().timestamp() * 1000)
                    
                    # Store in Redis: key is friend:expedition:end:time:%s, value is invited_uid, score is timestamp
                    redis_key = f"friend:expedition:end:time:{inviter_uid}"
                    redis_conn.zadd(redis_key, {invited_uid: timestamp})
                    logging.info(f"Stored invitation data in Redis: {inviter_uid} invited {invited_uid} at timestamp {timestamp}")
            except Exception as e:
                logging.error(f"Error storing invitation data in Redis: {str(e)}")
        
        # Step 3: Process eligible users and update media_task database
        task_conn = pymysql.connect(**task_db_config)
        
        from_uids = []
        for user in eligible_users:
            from_uids.append(user['from_uid'])
        from_uids = list(set(from_uids))
        
        try:
            with task_conn.cursor() as cursor:
                for user_id in from_uids:
                    # Begin transaction
                    try:
                        # Award energy points using the common function
                        success, _ = award_energy_points(
                            cursor, 
                            user_id, 
                            task_code, 
                            base_energy_amount, 
                            '1',
                            'face invitation'
                        )
                        
                        if success:
                            task_conn.commit()
                        
                    except Exception as e:
                        task_conn.rollback()
                        logging.error(f"Error processing user {user_id}: {str(e)}")
        finally:
            task_conn.close()
            
        logging.info("Face invitation energy initialization completed")
        
    except Exception as e:
        logging.error(f"Error in initialize_face_invite_energy: {str(e)}")


def award_energy_points(cursor, user_id, task_code, energy_points, source_type, task_description):
    """
    Common function to award energy points to a user:
    - Checks if the task has already been awarded
    - Creates a task record in user_task_record
    - Creates an energy record in ep_records
    - Updates the user_base_energy table if it exists
    
    Args:
        cursor: Database cursor for media_task database
        user_id: User ID to award points to
        task_code: Task code identifier
        energy_points: Number of energy points to award
        source_type: Source type for the energy record
        task_description: Description for logging
        
    Returns:
        bool: True if points were awarded, False if user already had the task
        int: Task record ID if created, None otherwise
    """
    # Calculate the shard number based on user_id % 1024
    shard_num = user_id % 1024
    
    # Define sharded table names
    user_task_table = f"user_task_record_{shard_num}"
    ep_records_table = f"user_ep_records_{shard_num}"
    #user_task_table = "user_task_record"
    #ep_records_table = "user_ep_records"

    try:
        # Start transaction
        cursor.execute("START TRANSACTION")
        
        # Check if task record already exists for this user
        # Using SELECT FOR UPDATE to lock the row and prevent race conditions
        check_query = f"""
        SELECT id FROM {user_task_table} 
        WHERE user_id = %s AND task_code = %s AND delete_status = 0
        FOR UPDATE
        """
        cursor.execute(check_query, (user_id, task_code))
        existing_record = cursor.fetchone()
        
        if existing_record:
            logging.info(f"User {user_id} already has {task_description} bonus. Skipping.")
            cursor.execute("COMMIT")
            return False, None
        
        # Insert task record
        now = datetime.now()
        task_insert_query = f"""
        INSERT INTO {user_task_table} 
        (user_id, task_code,task_type, complete_status,has_complete_count) 
        VALUES (%s, %s,1, %s,1)
        """
        cursor.execute(task_insert_query, (user_id, task_code, 1))
        task_record_id = cursor.lastrowid
        
        # Get the beginning of the current hour (HH:00:00)
        hourly_time = now.replace(minute=0, second=0, microsecond=0)
        
        # Insert energy record
        energy_insert_query = f"""
        INSERT INTO {ep_records_table} 
        (user_id, ep_type, ep_amount, ep_source_type, task_record_id, hourly) 
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        cursor.execute(
            energy_insert_query, 
            (user_id, 1, energy_points, source_type, task_record_id, hourly_time)
        )
        
        # Update user_base_energy table if it exists
        
        # Calculate the shard number for user_base_energy table
        base_energy_table = "user_base_ep"
        
        base_energy_query = f"""
        INSERT INTO {base_energy_table} (user_id, total_base_ep) 
        VALUES (%s, %s) 
        ON DUPLICATE KEY UPDATE total_base_ep= total_base_ep + %s
        """
        cursor.execute(base_energy_query, (user_id, energy_points, energy_points))
        
        # Commit transaction
        cursor.execute("COMMIT")
        
        logging.info(f"Successfully processed user {user_id}: Added {energy_points} base energy points for {task_description}")
        return True, task_record_id
    
    except Exception as e:
        # Rollback transaction in case of error
        try:
            cursor.execute("ROLLBACK")
        except:
            pass  # Ignore errors during rollback
        logging.error(f"Error processing user {user_id} for {task_description}: {str(e)}")
        raise


def main():
    """
    Main function to run all initialization tasks
    """
    logging.info("Starting energy initialization tasks")
    
    # Initialize energy for verified users
    #初始化用户能量（包含手机，邮箱）
    initialize_user_energy()
    
    # Initialize energy for UGC posts
    #初始化UGC能量
    initialize_ugc_energy()
    
    # Initialize energy for interest selection
    #初始化用户兴趣选择能量
    initialize_interest_energy()
    
    # Initialize energy for face verification invitations
    #初始化用户人脸邀请能量
    initialize_face_invite_energy()
    
    logging.info("All energy initialization tasks completed")

if __name__ == "__main__":
    main()