import pymysql
import logging
from datetime import datetime
from redis.cluster import RedisCluster

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("task_update.log"),
        logging.StreamHandler()
    ]
)

# 数据库配置
DB_CONFIG = {
    'host': 'xme-envtest-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_task',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_redis_connection():
    """获取Redis集群连接"""
    try:
        # Redis连接参数
        host = "xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com"
        port = 6379
        
        # 创建RedisCluster连接
        redis_conn = RedisCluster(
            host=host,
            port=port,
            decode_responses=True,
            ssl=False
        )
        
        redis_conn.ping()
        logging.info("成功连接到Redis集群")
        return redis_conn
    except Exception as e:
        logging.error(f"连接Redis时出错: {str(e)}")
        return None

def delete_user_base_ep_cache(redis_conn, user_ids):
    """
    批量删除用户基础能量点缓存
    """
    try:
        # 构建所有需要删除的key
        keys = [f"user:base:ep:{uid}" for uid in user_ids]
        if keys:
            # 批量删除
            for key in keys:
                redis_conn.delete(key)
            logging.info(f"成功删除 {len(keys)} 个用户的能量点缓存")
    except Exception as e:
        logging.error(f"删除Redis缓存时出错: {str(e)}")

def process_user_tasks(cursor, user_id, now):
    """
    处理单个用户的任务和能量点
    """
    total_deduction = 0
    shard_num = user_id % 1024
    
    task_table = f"user_task_record_{shard_num}"
    ep_table = f"user_ep_records_{shard_num}"
    
    # 1. 查询需要更新的task记录
    task_query = f"""
    SELECT id 
    FROM {task_table}
    WHERE user_id = %s
    AND task_type = 1 
    AND task_code NOT IN ('10002','10003','10011')
    AND delete_status = 0
    """
    cursor.execute(task_query, (user_id,))
    task_records = cursor.fetchall()
    
    if not task_records:
        return 0
    
    # 2. 获取这些task对应的ep记录
    task_ids = [str(record['id']) for record in task_records]
    ep_query = f"""
    SELECT SUM(ep_amount) as total_ep
    FROM {ep_table}
    WHERE ep_type = 1 
    AND task_record_id IN ({','.join(task_ids)})
    AND delete_status = 0
    """
    cursor.execute(ep_query)
    ep_result = cursor.fetchone()
    total_deduction = ep_result['total_ep'] if ep_result['total_ep'] else 0
    
    # 如果扣除金额超过950，限制为950
    if total_deduction > 950:
        total_deduction = 950
        
    # 3. 更新task记录
    task_update = f"""
    UPDATE {task_table}
    SET delete_status = 1,
        delete_time = %s
    WHERE id IN ({','.join(task_ids)})
    """
    cursor.execute(task_update, (now,))
    
    # 4. 更新ep记录
    ep_update = f"""
    UPDATE {ep_table}
    SET delete_status = 1,
        delete_time = %s
    WHERE ep_type = 1 
    AND task_record_id IN ({','.join(task_ids)})
    """
    cursor.execute(ep_update, (now,))
    
    return total_deduction

def check_user_tasks(cursor, user_id):
    """
    检查用户任务数据，不做更新
    """
    shard_num = user_id % 1024
    
    task_table = f"user_task_record_{shard_num}"
    ep_table = f"user_ep_records_{shard_num}"
    
    # 1. 查询需要更新的task记录
    task_query = f"""
    SELECT id, task_code 
    FROM {task_table}
    WHERE user_id = %s
    AND task_type = 1 
    AND task_code NOT IN ('10002','10003','10011')
    AND delete_status = 0
    """
    cursor.execute(task_query, (user_id,))
    task_records = cursor.fetchall()
    
    if not task_records:
        return None
    
    # 2. 获取这些task对应的ep记录
    task_ids = [str(record['id']) for record in task_records]
    ep_query = f"""
    SELECT SUM(ep_amount) as total_ep
    FROM {ep_table}
    WHERE ep_type = 1 
    AND task_record_id IN ({','.join(task_ids)})
    AND delete_status = 0
    """
    cursor.execute(ep_query)
    ep_result = cursor.fetchone()
    total_deduction = ep_result['total_ep'] if ep_result['total_ep'] else 0
    
    # 如果扣除金额超过950，限制为950
    if total_deduction > 950:
        total_deduction = 950
        
    return {
        'task_count': len(task_records),
        'task_codes': [record['task_code'] for record in task_records],
        'total_deduction': total_deduction
    }

def check_user_energy(batch_size=1000, last_id=0):
    """
    检查模式：只查看会影响哪些数据，不做实际更新
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        total_processed = 0
        total_affected_users = 0
        total_deduction_amount = 0
        
        while True:
            user_query = """
            SELECT id, user_id, total_base_ep
            FROM user_base_ep
            WHERE id > %s
            AND total_base_ep > 0
            AND delete_status = 0
            ORDER BY id
            LIMIT %s
            """
            cursor.execute(user_query, (last_id, batch_size))
            users = cursor.fetchall()
            
            if not users:
                break
                
            logging.info(f"正在检查第 {total_processed + 1} - {total_processed + len(users)} 个用户")
            
            # 记录本批次的检查结果
            with open('check_details.log', 'a') as f:
                for user in users:
                    user_id = user['user_id']
                    current_ep = user['total_base_ep']
                    last_id = user['id']
                    
                    try:
                        check_result = check_user_tasks(cursor, user_id)
                        
                        if check_result:
                            total_affected_users += 1
                            total_deduction_amount += check_result['total_deduction']
                            
                            # 记录详细信息
                            f.write(f"用户ID: {user_id}\n")
                            f.write(f"  当前能量点: {current_ep}\n")
                            f.write(f"  将扣除能量点: {check_result['total_deduction']}\n")
                            f.write(f"  影响任务数: {check_result['task_count']}\n")
                            f.write(f"  任务代码: {', '.join(check_result['task_codes'])}\n")
                            f.write("----------------------------------------\n")
                            
                    except Exception as e:
                        logging.error(f"检查用户 {user_id} 时出错: {str(e)}")
                        continue
            
            total_processed += len(users)
            
            if len(users) < batch_size:
                break
        
        # 输出汇总信息
        logging.info("=== 检查汇总 ===")
        logging.info(f"总检查用户数: {total_processed}")
        logging.info(f"受影响用户数: {total_affected_users}")
        logging.info(f"总扣除能量点: {total_deduction_amount}")
        
    except Exception as e:
        logging.error(f"数据库操作错误: {str(e)}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def update_user_energy(batch_size=1000, last_id=0):
    """
    批量更新用户能量点
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 获取Redis连接
        redis_conn = get_redis_connection()
        if not redis_conn:
            logging.error("无法连接Redis，将只更新MySQL数据")
        
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        total_processed = 0
        
        while True:
            # 分批获取用户
            user_query = """
            SELECT id, user_id, total_base_ep
            FROM user_base_ep
            WHERE id > %s
            AND total_base_ep > 0
            AND delete_status = 0
            ORDER BY id
            LIMIT %s
            """
            cursor.execute(user_query, (last_id, batch_size))
            users = cursor.fetchall()
            
            if not users:
                break
                
            logging.info(f"正在处理第 {total_processed + 1} - {total_processed + len(users)} 个用户")
            
            user_updates = []
            updated_user_ids = []  # 记录需要删除缓存的用户ID
            
            for user in users:
                user_id = user['user_id']
                current_ep = user['total_base_ep']
                last_id = user['id']
                
                try:
                    # 处理用户任务并获取需要扣除的能量点
                    deduction = process_user_tasks(cursor, user_id, now)
                    
                    if deduction > 0:
                        # 确保不会扣成负数
                        final_deduction = min(deduction, current_ep)
                        user_updates.append({
                            'user_id': user_id,
                            'deduction': final_deduction
                        })
                        updated_user_ids.append(user_id)
                        
                except Exception as e:
                    logging.error(f"处理用户 {user_id} 时出错: {str(e)}")
                    continue
            
            # 批量更新user_base_ep表
            if user_updates:
                for update in user_updates:
                    base_ep_update = """
                    UPDATE user_base_ep
                    SET total_base_ep = total_base_ep - %s,
                        update_time = %s
                    WHERE user_id = %s
                    """
                    cursor.execute(base_ep_update, (update['deduction'], now, update['user_id']))
                
                conn.commit()
                
                # 删除Redis缓存
                if redis_conn and updated_user_ids:
                    delete_user_base_ep_cache(redis_conn, updated_user_ids)
                
                # 记录这批次的更新信息
                batch_users = len(user_updates)
                batch_deduction = sum(update['deduction'] for update in user_updates)
                logging.info(f"本批次完成: 处理了 {batch_users} 个用户, 扣除能量点: {batch_deduction}")
                
                # 记录详细信息
                with open('ep_deduction_details.log', 'a') as f:
                    for update in user_updates:
                        f.write(f"用户ID: {update['user_id']}, 扣除能量点: {update['deduction']}\n")
            
            total_processed += len(users)
            
            if len(users) < batch_size:
                break
                
        logging.info(f"所有批次处理完成，共处理 {total_processed} 个用户")
                    
    except Exception as e:
        logging.error(f"数据库操作错误: {str(e)}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        if 'redis_conn' in locals() and redis_conn:
            redis_conn.close()

def main():
    import argparse
    parser = argparse.ArgumentParser(description='用户能量点更新工具')
    parser.add_argument('--check', action='store_true', help='只检查数据，不做更新')
    parser.add_argument('--batch-size', type=int, default=1000, help='每批处理的用户数')
    args = parser.parse_args()

    if args.check:
        logging.info("开始检查任务...")
        check_user_energy(batch_size=args.batch_size)
        logging.info("检查任务完成")
    else:
        logging.info("开始更新任务...")
        update_user_energy(batch_size=args.batch_size)
        logging.info("更新任务完成")

if __name__ == "__main__":
    main()