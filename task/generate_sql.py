#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成push_message分表(0-1023)的字段修改和数据更新SQL脚本
"""

def generate_push_message_sql():
    """生成push_message表0-1023的完整SQL脚本"""
    
    sql_statements = []
    
    # 添加头部注释
    sql_statements.append("-- 修改字段定义和更新数据 - push_message表 (0-1023)")
    sql_statements.append("-- 生成时间: " + str(__import__('datetime').datetime.now()))
    sql_statements.append("")
    
    # 生成 created_time 字段修改语句
    sql_statements.append("-- 修改 created_time 字段默认值")
    for i in range(1024):
        sql = f"ALTER TABLE media_message.push_message{i} MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';"
        sql_statements.append(sql)
    
    sql_statements.append("")
    
    # 生成 updated_time 字段修改语句
    sql_statements.append("-- 修改 updated_time 字段默认值")
    for i in range(1024):
        sql = f"ALTER TABLE media_message.push_message{i} MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';"
        sql_statements.append(sql)
    
    sql_statements.append("")
    
    # 生成数据更新语句
    sql_statements.append("-- 更新空值数据")
    for i in range(1024):
        sql = f"UPDATE media_message.push_message{i} SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;"
        sql_statements.append(sql)
    
    sql_statements.append("")
    sql_statements.append("-- 注意：此脚本包含0-1023共1024个表的修改，实际执行时请确保所有表都存在")
    sql_statements.append("-- 建议分批执行，避免数据库压力过大")
    sql_statements.append("-- 可以按照以下方式分批执行：")
    sql_statements.append("-- 第1批：0-255")
    sql_statements.append("-- 第2批：256-511")
    sql_statements.append("-- 第3批：512-767")
    sql_statements.append("-- 第4批：768-1023")
    
    return '\n'.join(sql_statements)

def generate_batch_sql(start_index, end_index):
    """生成指定范围的分批SQL脚本"""
    
    sql_statements = []
    
    # 添加头部注释
    sql_statements.append(f"-- 修改字段定义和更新数据 - push_message表 ({start_index}-{end_index})")
    sql_statements.append("-- 生成时间: " + str(__import__('datetime').datetime.now()))
    sql_statements.append("")
    
    # 生成 created_time 字段修改语句
    sql_statements.append(f"-- 修改 created_time 字段默认值 ({start_index}-{end_index})")
    for i in range(start_index, end_index + 1):
        sql = f"ALTER TABLE media_message.push_message{i} MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';"
        sql_statements.append(sql)
    
    sql_statements.append("")
    
    # 生成 updated_time 字段修改语句
    sql_statements.append(f"-- 修改 updated_time 字段默认值 ({start_index}-{end_index})")
    for i in range(start_index, end_index + 1):
        sql = f"ALTER TABLE media_message.push_message{i} MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';"
        sql_statements.append(sql)
    
    sql_statements.append("")
    
    # 生成数据更新语句
    sql_statements.append(f"-- 更新空值数据 ({start_index}-{end_index})")
    for i in range(start_index, end_index + 1):
        sql = f"UPDATE media_message.push_message{i} SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;"
        sql_statements.append(sql)
    
    return '\n'.join(sql_statements)

if __name__ == "__main__":
    # 生成完整的SQL脚本
    print("正在生成完整的SQL脚本...")
    full_sql = generate_push_message_sql()
    
    # 保存到文件
    with open('push_message_full_sql.sql', 'w', encoding='utf-8') as f:
        f.write(full_sql)
    
    print("完整SQL脚本已保存到: push_message_full_sql.sql")
    
    # 生成分批SQL脚本
    batches = [
        (0, 255),
        (256, 511),
        (512, 767),
        (768, 1023)
    ]
    
    for i, (start, end) in enumerate(batches, 1):
        batch_sql = generate_batch_sql(start, end)
        filename = f'push_message_batch_{i}_{start}_{end}.sql'
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(batch_sql)
        
        print(f"第{i}批SQL脚本已保存到: {filename}")
    
    print("\n所有SQL脚本生成完成！")
    print("建议按批次执行，每批次间隔一定时间以减少数据库压力。") 