# 用户登录日志分析工具

基于 `user_login_log` 表结构的用户登录数据分析工具，支持来源统计、平台对比和用户抽样分析。

## 功能特性

- 📊 **来源类型统计**：按设备来源（Android、iOS、Web）统计登录占比
- 📱 **登录平台分析**：分析不同平台（自有平台、Google Play、Bee）的用户分布
- 🔍 **APK vs Google Play对比**：专门对比分析APK和Google Play用户特征
- 🎯 **用户抽样分析**：随机抽样分析指定平台的用户行为
- 📋 **报告导出**：生成完整的JSON格式分析报告

## 环境要求

- Python 3.7+
- MySQL 数据库
- 相关Python包（见requirements.txt）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 数据库表结构

工具基于以下表结构进行分析：

```sql
CREATE TABLE `user_login_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` bigint NOT NULL,
  `login_ip` varchar(64) DEFAULT NULL COMMENT '登录ip',
  `login_time` varchar(10) NOT NULL COMMENT '登录日期(yyyy-mm-dd)',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `login_type` varchar(100) DEFAULT NULL COMMENT '登录平台(platform, bee, google)',
  `source_type` bigint DEFAULT NULL COMMENT '用户设备来源(ANDROID，IOS， WEB)',
  `country_code` varchar(10) DEFAULT NULL COMMENT '国家码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_idx_uid_day` (`uid`,`login_time`),
  KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表'
```

## 快速开始

### 1. 配置数据库连接

修改 `example_usage.py` 中的数据库配置：

```python
DB_CONFIG = {
    'host': 'your_mysql_host',        # 数据库主机地址
    'database': 'your_database_name', # 数据库名称
    'user': 'your_username',          # 数据库用户名
    'password': 'your_password',      # 数据库密码
    'port': 3306                      # 数据库端口
}
```

### 2. 运行分析

```bash
python example_usage.py
```

## 详细功能说明

### 1. 来源类型统计 (`get_source_type_statistics`)

分析用户设备来源分布：
- 统计Android、iOS、Web用户的登录次数和占比
- 计算独立用户数量和用户占比
- 支持自定义时间范围（默认30天）

### 2. 登录平台统计 (`get_platform_statistics`)

分析登录平台分布：
- 统计自有平台、Google Play、Bee平台的用户分布
- 显示各平台登录次数和用户占比

### 3. APK vs Google Play对比 (`analyze_apk_vs_googleplay`)

专门对比APK和Google Play用户：
- 设备来源对比（Android/iOS/Web占比）
- 国家分布对比（Top 5国家）
- 登录频率对比（平均登录次数）

### 4. 用户抽样分析 (`sample_users_by_platform`)

随机抽样分析用户特征：
- 支持指定平台和抽样数量
- 显示抽样用户的平台、设备、国家分布
- 分析登录频率等行为特征

### 5. 报告导出 (`export_analysis_report`)

导出完整的JSON格式分析报告，包含：
- 分析时间戳
- 所有统计数据
- 对比分析结果

## 输出示例

### 来源类型统计示例
```
📊 来源类型统计 (2024-01-01 到 2024-01-30)
============================================================
来源类型           登录次数        占比      独立用户        用户占比   
------------------------------------------------------------
ANDROID          15000        75.00%   5000         71.43%
IOS              3000         15.00%   1500         21.43%
WEB              2000         10.00%   500          7.14%
------------------------------------------------------------
总计             20000        100.00%  7000         100.00%
```

### APK vs Google Play对比示例
```
📈 详细对比分析:
--------------------------------------------------
📱 设备来源对比:
设备类型           APK用户      Google Play
----------------------------------------
ANDROID             800(80.0%)   950(95.0%)
IOS                 150(15.0%)    40( 4.0%)
WEB                  50( 5.0%)    10( 1.0%)

🌍 国家分布对比 (Top 5):
国家        APK用户      Google Play
-----------------------------------
US            200(20.0%)   300(30.0%)
CN            300(30.0%)   100(10.0%)
IN            150(15.0%)   200(20.0%)
```

## 自定义使用

### 基本用法

```python
from user_login_analysis import UserLoginAnalyzer

# 创建分析器
analyzer = UserLoginAnalyzer(
    host='localhost',
    database='your_db',
    user='user',
    password='password'
)

# 连接数据库
analyzer.connect_db()

# 获取统计数据
source_stats = analyzer.get_source_type_statistics(days=7)  # 最近7天
platform_stats = analyzer.get_platform_statistics(days=7) # 最近7天

# 抽样分析
sample_data = analyzer.sample_users_by_platform(
    sample_size=500,
    target_platforms=['platform', 'google'],
    days=7
)

# 关闭连接
analyzer.close_connection()
```

### 配置选项

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `days` | 分析时间范围（天） | 7 |
| `sample_size` | 抽样用户数量 | 1000 |
| `target_platforms` | 目标平台列表 | ['platform', 'google'] |

### 平台类型映射

| 数据库值 | 显示名称 |
|----------|----------|
| platform | 自有平台 |
| google | Google Play |
| bee | Bee平台 |

### 设备类型映射

| source_type | 显示名称 |
|-------------|----------|
| 0 | ANDROID (APK) |
| 1 | IOS |
| 2 | WEB |
| 3 | CONTENT |
| 4 | ANDROID-GP (Google Play) |
| 5 | UNKNOWN |

## 注意事项

1. **数据库权限**：确保数据库用户有读取 `user_login_log` 表的权限
2. **时间格式**：`login_time` 字段应为 'YYYY-MM-DD' 格式
3. **抽样随机性**：使用 `ORDER BY RAND()` 进行随机抽样，大数据量时可能影响性能
4. **字符编码**：确保数据库和Python环境都使用UTF-8编码

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查主机地址、端口号
   - 确认用户名密码正确
   - 检查网络连接

2. **查询结果为空**
   - 检查时间范围是否有数据
   - 确认表名和字段名正确
   - 检查数据库中是否有相应数据

3. **中文显示问题**
   - 确保数据库字符集为utf8mb4
   - 检查Python环境编码设置

## 扩展功能

您可以根据需要扩展以下功能：

1. **可视化图表**：使用matplotlib生成统计图表
2. **更多统计维度**：添加时间趋势分析、地理分布等
3. **报告格式**：支持Excel、PDF等格式导出
4. **实时监控**：结合定时任务实现实时数据监控

## 许可证

MIT License 