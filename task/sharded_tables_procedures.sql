-- 分表查询存储过程 (user_like和user_read表，1024张分表)
-- 适用于MySQL数据库

DELIMITER $$

-- =============================================================================
-- 1. 根据用户ID查询用户点赞数据的存储过程
-- =============================================================================
DROP PROCEDURE IF EXISTS GetUserLikes$$

CREATE PROCEDURE GetUserLikes(IN p_user_id BIGINT)
BEGIN
    DECLARE table_suffix INT;
    DECLARE sql_stmt TEXT;
    
    -- 计算分表后缀 (user_id % 1024)
    SET table_suffix = p_user_id % 1024;
    
    -- 构建动态SQL
    SET sql_stmt = CONCAT('SELECT * FROM user_like_', table_suffix, ' WHERE user_id = ', p_user_id);
    
    -- 执行动态SQL
    SET @sql = sql_stmt;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$

-- =============================================================================
-- 2. 根据用户ID查询用户阅读数据的存储过程
-- =============================================================================
DROP PROCEDURE IF EXISTS GetUserReads$$

CREATE PROCEDURE GetUserReads(IN p_user_id BIGINT)
BEGIN
    DECLARE table_suffix INT;
    DECLARE sql_stmt TEXT;
    
    -- 计算分表后缀
    SET table_suffix = p_user_id % 1024;
    
    -- 构建动态SQL
    SET sql_stmt = CONCAT('SELECT * FROM user_read_', table_suffix, ' WHERE user_id = ', p_user_id);
    
    -- 执行动态SQL
    SET @sql = sql_stmt;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$

-- =============================================================================
-- 3. 获取用户的点赞和阅读统计信息
-- =============================================================================
DROP PROCEDURE IF EXISTS GetUserStats$$

CREATE PROCEDURE GetUserStats(IN p_user_id BIGINT)
BEGIN
    DECLARE table_suffix INT;
    DECLARE like_count INT DEFAULT 0;
    DECLARE read_count INT DEFAULT 0;
    DECLARE sql_stmt TEXT;
    
    -- 计算分表后缀
    SET table_suffix = p_user_id % 1024;
    
    -- 查询点赞数量
    SET sql_stmt = CONCAT('SELECT COUNT(*) INTO @like_count FROM user_like_', table_suffix, ' WHERE user_id = ', p_user_id);
    SET @sql = sql_stmt;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET like_count = @like_count;
    
    -- 查询阅读数量
    SET sql_stmt = CONCAT('SELECT COUNT(*) INTO @read_count FROM user_read_', table_suffix, ' WHERE user_id = ', p_user_id);
    SET @sql = sql_stmt;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET read_count = @read_count;
    
    -- 返回统计结果
    SELECT 
        p_user_id as user_id,
        table_suffix,
        like_count,
        read_count,
        CONCAT('user_like_', table_suffix) as like_table_name,
        CONCAT('user_read_', table_suffix) as read_table_name;
END$$

-- =============================================================================
-- 4. 统计所有分表的记录数
-- =============================================================================
DROP PROCEDURE IF EXISTS GetAllTablesStats$$

CREATE PROCEDURE GetAllTablesStats()
BEGIN
    DECLARE i INT DEFAULT 0;
    DECLARE sql_stmt TEXT;
    DECLARE done INT DEFAULT FALSE;
    
    -- 创建临时结果表
    DROP TEMPORARY TABLE IF EXISTS temp_stats;
    CREATE TEMPORARY TABLE temp_stats (
        table_name VARCHAR(50),
        table_type VARCHAR(20),
        record_count BIGINT
    );
    
    -- 循环查询所有user_like分表
    WHILE i < 1024 DO
        SET sql_stmt = CONCAT('INSERT INTO temp_stats SELECT ''user_like_', i, ''', ''like'', COUNT(*) FROM user_like_', i);
        SET @sql = sql_stmt;
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        SET i = i + 1;
    END WHILE;
    
    -- 重置计数器，查询所有user_read分表
    SET i = 0;
    WHILE i < 1024 DO
        SET sql_stmt = CONCAT('INSERT INTO temp_stats SELECT ''user_read_', i, ''', ''read'', COUNT(*) FROM user_read_', i);
        SET @sql = sql_stmt;
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        SET i = i + 1;
    END WHILE;
    
    -- 返回统计结果
    SELECT 
        table_type,
        COUNT(*) as table_count,
        SUM(record_count) as total_records,
        AVG(record_count) as avg_records_per_table,
        MIN(record_count) as min_records,
        MAX(record_count) as max_records
    FROM temp_stats 
    GROUP BY table_type
    
    UNION ALL
    
    SELECT 
        'total' as table_type,
        COUNT(*) as table_count,
        SUM(record_count) as total_records,
        AVG(record_count) as avg_records_per_table,
        MIN(record_count) as min_records,
        MAX(record_count) as max_records
    FROM temp_stats;
    
    -- 详细分表统计
    SELECT * FROM temp_stats ORDER BY table_name;
    
    DROP TEMPORARY TABLE temp_stats;
END$$

-- =============================================================================
-- 5. 根据时间范围查询用户数据
-- =============================================================================
DROP PROCEDURE IF EXISTS GetUserDataByTimeRange$$

CREATE PROCEDURE GetUserDataByTimeRange(
    IN p_user_id BIGINT, 
    IN p_start_time DATETIME, 
    IN p_end_time DATETIME
)
BEGIN
    DECLARE table_suffix INT;
    DECLARE sql_stmt TEXT;
    
    -- 计算分表后缀
    SET table_suffix = p_user_id % 1024;
    
    -- 创建临时结果表
    DROP TEMPORARY TABLE IF EXISTS temp_user_data;
    CREATE TEMPORARY TABLE temp_user_data (
        data_type VARCHAR(10),
        id BIGINT,
        user_id BIGINT,
        created_time DATETIME,
        table_name VARCHAR(50)
    );
    
    -- 查询点赞数据
    SET sql_stmt = CONCAT(
        'INSERT INTO temp_user_data (data_type, id, user_id, created_time, table_name) ',
        'SELECT ''like'', id, user_id, created_time, ''user_like_', table_suffix, ''' ',
        'FROM user_like_', table_suffix, ' ',
        'WHERE user_id = ', p_user_id, ' ',
        'AND created_time BETWEEN ''', p_start_time, ''' AND ''', p_end_time, ''''
    );
    SET @sql = sql_stmt;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 查询阅读数据
    SET sql_stmt = CONCAT(
        'INSERT INTO temp_user_data (data_type, id, user_id, created_time, table_name) ',
        'SELECT ''read'', id, user_id, created_time, ''user_read_', table_suffix, ''' ',
        'FROM user_read_', table_suffix, ' ',
        'WHERE user_id = ', p_user_id, ' ',
        'AND created_time BETWEEN ''', p_start_time, ''' AND ''', p_end_time, ''''
    );
    SET @sql = sql_stmt;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 返回结果
    SELECT * FROM temp_user_data ORDER BY created_time DESC;
    
    -- 返回统计信息
    SELECT 
        data_type,
        COUNT(*) as count,
        MIN(created_time) as earliest_time,
        MAX(created_time) as latest_time
    FROM temp_user_data 
    GROUP BY data_type;
    
    DROP TEMPORARY TABLE temp_user_data;
END$$

-- =============================================================================
-- 6. 批量查询多个用户的统计信息
-- =============================================================================
DROP PROCEDURE IF EXISTS GetMultiUserStats$$

CREATE PROCEDURE GetMultiUserStats(IN p_user_ids TEXT)
BEGIN
    DECLARE user_id BIGINT;
    DECLARE table_suffix INT;
    DECLARE sql_stmt TEXT;
    DECLARE pos INT;
    DECLARE next_pos INT;
    DECLARE user_id_str VARCHAR(20);
    
    -- 创建临时结果表
    DROP TEMPORARY TABLE IF EXISTS temp_multi_stats;
    CREATE TEMPORARY TABLE temp_multi_stats (
        user_id BIGINT,
        table_suffix INT,
        like_count INT,
        read_count INT
    );
    
    -- 解析用户ID列表 (逗号分隔)
    SET pos = 1;
    WHILE pos > 0 DO
        SET next_pos = LOCATE(',', p_user_ids, pos);
        
        IF next_pos = 0 THEN
            SET user_id_str = SUBSTRING(p_user_ids, pos);
            SET pos = 0;
        ELSE
            SET user_id_str = SUBSTRING(p_user_ids, pos, next_pos - pos);
            SET pos = next_pos + 1;
        END IF;
        
        SET user_id = CAST(TRIM(user_id_str) AS UNSIGNED);
        SET table_suffix = user_id % 1024;
        
        -- 查询该用户的点赞和阅读数量
        SET sql_stmt = CONCAT(
            'INSERT INTO temp_multi_stats (user_id, table_suffix, like_count, read_count) ',
            'SELECT ', user_id, ', ', table_suffix, ', ',
            '(SELECT COUNT(*) FROM user_like_', table_suffix, ' WHERE user_id = ', user_id, '), ',
            '(SELECT COUNT(*) FROM user_read_', table_suffix, ' WHERE user_id = ', user_id, ')'
        );
        SET @sql = sql_stmt;
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END WHILE;
    
    -- 返回结果
    SELECT * FROM temp_multi_stats ORDER BY user_id;
    
    DROP TEMPORARY TABLE temp_multi_stats;
END$$

-- =============================================================================
-- 7. 检查分表是否存在的存储过程
-- =============================================================================
DROP PROCEDURE IF EXISTS CheckTablesExist$$

CREATE PROCEDURE CheckTablesExist()
BEGIN
    DECLARE i INT DEFAULT 0;
    DECLARE like_table_name VARCHAR(50);
    DECLARE read_table_name VARCHAR(50);
    DECLARE like_exists INT DEFAULT 0;
    DECLARE read_exists INT DEFAULT 0;
    
    -- 创建临时结果表
    DROP TEMPORARY TABLE IF EXISTS temp_table_check;
    CREATE TEMPORARY TABLE temp_table_check (
        table_suffix INT,
        like_table_exists BOOLEAN,
        read_table_exists BOOLEAN
    );
    
    WHILE i < 1024 DO
        SET like_table_name = CONCAT('user_like_', i);
        SET read_table_name = CONCAT('user_read_', i);
        
        -- 检查like表是否存在
        SELECT COUNT(*) INTO like_exists 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = like_table_name;
        
        -- 检查read表是否存在
        SELECT COUNT(*) INTO read_exists 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = read_table_name;
        
        INSERT INTO temp_table_check VALUES (i, like_exists > 0, read_exists > 0);
        
        SET i = i + 1;
    END WHILE;
    
    -- 返回检查结果
    SELECT 
        COUNT(*) as total_suffixes,
        SUM(like_table_exists) as like_tables_exist,
        SUM(read_table_exists) as read_tables_exist,
        SUM(like_table_exists AND read_table_exists) as both_tables_exist
    FROM temp_table_check;
    
    -- 返回缺失的表
    SELECT 
        table_suffix,
        CASE WHEN NOT like_table_exists THEN CONCAT('user_like_', table_suffix) ELSE NULL END as missing_like_table,
        CASE WHEN NOT read_table_exists THEN CONCAT('user_read_', table_suffix) ELSE NULL END as missing_read_table
    FROM temp_table_check 
    WHERE NOT like_table_exists OR NOT read_table_exists;
    
    DROP TEMPORARY TABLE temp_table_check;
END$$

DELIMITER ;

-- =============================================================================
-- 使用示例
-- =============================================================================

-- 示例1：查询用户12345的点赞数据
-- CALL GetUserLikes(12345);

-- 示例2：查询用户12345的阅读数据
-- CALL GetUserReads(12345);

-- 示例3：获取用户12345的统计信息
-- CALL GetUserStats(12345);

-- 示例4：获取所有分表的统计信息
-- CALL GetAllTablesStats();

-- 示例5：查询用户12345在指定时间范围内的数据
-- CALL GetUserDataByTimeRange(12345, '2024-01-01 00:00:00', '2024-01-31 23:59:59');

-- 示例6：批量查询多个用户的统计信息
-- CALL GetMultiUserStats('12345,67890,11111');

-- 示例7：检查所有分表是否存在
-- CALL CheckTablesExist();

-- =============================================================================
-- 注意事项：
-- 1. 这些存储过程假设分表规则是：table_name_{user_id % 1024}
-- 2. 需要确保所有分表都已创建
-- 3. 对于大量数据的统计操作，建议在低峰时执行
-- 4. 可以根据实际需求修改和扩展这些存储过程
-- ============================================================================= 