import pymysql
import logging
from concurrent.futures import ThreadPoolExecutor
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("update_complete_count.log"),
        logging.StreamHandler()
    ]
)

# 任务数据库配置
TASK_DB = {
    'host': 'xme-envtest-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_task'
}

def get_db_config(**kwargs):
    """
    Helper function to prepare database configuration.
    Returns the database configuration dictionary with cursor settings.
    
    Args:
        **kwargs: Database connection parameters (host, user, password, database)
        
    Returns:
        dict: Database configuration with cursor settings
    """
    # Create a copy of the input config
    config = dict(kwargs)
    
    # Add cursor class configuration for dictionary results
    config['cursorclass'] = pymysql.cursors.DictCursor
    
    # Add other common configuration parameters if needed
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    
    return config

def update_shard(shard_num):
    """
    Update has_complete_count for a specific shard table
    
    Args:
        shard_num: The shard number to process
    """
    # Connect to media_task database
    task_db_config = get_db_config(**TASK_DB)
    task_conn = None
    
    try:
        # Define sharded table name
        user_task_table = f"user_task_record_{shard_num}"
        
        # Connect to database
        task_conn = pymysql.connect(**task_db_config)
        
        with task_conn.cursor() as cursor:
            # Update records where complete_status=1 but has_complete_count is not 1
            update_query = f"""
            UPDATE {user_task_table}
            SET has_complete_count = 1
            WHERE complete_status = 1 AND task_type = 1 AND (has_complete_count IS NULL OR has_complete_count != 1)
            """
            
            # Execute the update
            cursor.execute(update_query)
            rows_affected = cursor.rowcount
            
            # Commit the transaction
            task_conn.commit()
            
            logging.info(f"Shard {shard_num}: Updated {rows_affected} records")
            return rows_affected
            
    except Exception as e:
        if task_conn:
            task_conn.rollback()
        logging.error(f"Error updating shard {shard_num}: {str(e)}")
        return 0
    finally:
        if task_conn:
            task_conn.close()

def main():
    """
    Main function to update has_complete_count across all shards
    """
    start_time = time.time()
    logging.info("Starting update of has_complete_count across all shards")
    
    total_updated = 0
    
    # Use ThreadPoolExecutor to process shards in parallel
    # Adjust max_workers based on your system capabilities
    with ThreadPoolExecutor(max_workers=10) as executor:
        # Submit all shard updates to the executor
        future_to_shard = {executor.submit(update_shard, shard_num): shard_num for shard_num in range(1024)}
        
        # Process results as they complete
        for future in future_to_shard:
            shard_num = future_to_shard[future]
            try:
                rows_updated = future.result()
                total_updated += rows_updated
            except Exception as e:
                logging.error(f"Exception processing shard {shard_num}: {str(e)}")
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    logging.info(f"Update completed. Total records updated: {total_updated}")
    logging.info(f"Total execution time: {execution_time:.2f} seconds")

if __name__ == "__main__":
    main()
