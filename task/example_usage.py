#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户登录分析工具使用示例
"""

from user_login_analysis import UserLoginAnalyzer

def main():
    print("🚀 用户登录日志分析工具 - 示例演示")
    print("=" * 50)
    
    # 创建分析器实例（使用实际的数据库配置）
    analyzer = UserLoginAnalyzer(
        host="xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com",
        database="media_user",
        user="pro-user-user",
        password="VcEVqaE5HX",
        port=3306
    )
    
    try:
        # 1. 来源类型统计分析
        print("\n📊 1. 来源类型统计分析")
        print("分析最近3天的用户来源分布...")
        source_stats = analyzer.get_source_type_statistics(days=3)  # 最近3天
        
        # 2. 登录平台统计分析  
        print("\n📱 2. 登录平台统计分析")
        print("分析最近3天的平台使用情况...")
        platform_stats = analyzer.get_platform_statistics(days=3)  # 最近3天
        
        # 3. APK vs Google Play 对比分析 (基于source_type)
        print("\n🔍 3. Android APK vs Google Play 对比分析")
        print("基于source_type字段进行精确对比...")
        android_comparison = analyzer.analyze_android_apk_vs_gp(days=3, sample_size=1000)  # 最近3天
        
        # 4. 特定平台用户抽样分析
        print("\n🎯 4. 自有平台用户抽样分析")
        print("抽样分析1000个自有平台用户...")
        platform_sample = analyzer.sample_users_by_platform(
            sample_size=1000, 
            target_platforms=['platform-login', 'platform-register'], 
            days=3  # 最近3天
        )
        
        # 5. 导出完整分析报告
        print("\n📋 5. 导出完整分析报告")
        analyzer.export_analysis_report("user_analysis_report_3days.json")
        
        print("\n✅ 所有分析任务完成！")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        analyzer.close_connection()
        print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main() 