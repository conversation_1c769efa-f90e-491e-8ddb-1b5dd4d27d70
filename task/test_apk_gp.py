#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试APK vs Google Play分析功能
"""

import pymysql
from datetime import datetime, timedelta
from collections import Counter

def test_apk_vs_gp():
    print("🔍 测试 APK vs Google Play 分析...")
    print("=" * 60)
    
    try:
        connection = pymysql.connect(
            host="xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com",
            database="media_user",
            user="pro-user-user",
            password="VcEVqaE5HX",
            port=3306,
            charset='utf8mb4'
        )
        print("✅ 数据库连接成功!")
        
        cursor = connection.cursor()
        
        # 计算日期范围
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        print(f"📅 分析时间范围: {start_date} 到 {end_date}")
        
        # 测试APK用户查询
        print("\n🔍 测试APK用户查询 (source_type = 0):")
        query_apk = """
        SELECT 
            uid,
            login_type,
            source_type,
            country_code,
            login_ip,
            login_time,
            1 as login_frequency
        FROM user_login_log 
        WHERE login_time >= %s AND login_time <= %s
            AND source_type = 0
        LIMIT 10
        """
        
        cursor.execute(query_apk, (start_date, end_date))
        apk_results = cursor.fetchall()
        
        print(f"✅ APK用户查询返回 {len(apk_results)} 条记录")
        if apk_results:
            print("📋 APK用户样本:")
            for i, row in enumerate(apk_results[:5], 1):
                uid, login_type, source_type, country_code, login_ip, login_time, freq = row
                print(f"  {i}. UID: {uid}, 平台: {login_type}, 国家: {country_code}")
        
        # 测试Google Play用户查询
        print("\n🔍 测试Google Play用户查询 (source_type = 4):")
        query_gp = """
        SELECT 
            uid,
            login_type,
            source_type,
            country_code,
            login_ip,
            login_time,
            1 as login_frequency
        FROM user_login_log 
        WHERE login_time >= %s AND login_time <= %s
            AND source_type = 4
        LIMIT 10
        """
        
        cursor.execute(query_gp, (start_date, end_date))
        gp_results = cursor.fetchall()
        
        print(f"✅ Google Play用户查询返回 {len(gp_results)} 条记录")
        if gp_results:
            print("📋 Google Play用户样本:")
            for i, row in enumerate(gp_results[:5], 1):
                uid, login_type, source_type, country_code, login_ip, login_time, freq = row
                print(f"  {i}. UID: {uid}, 平台: {login_type}, 国家: {country_code}")
        
        # 进行对比分析
        if apk_results and gp_results:
            print("\n📊 对比分析:")
            
            # 分析APK用户
            apk_platforms = Counter([row[1] for row in apk_results])
            apk_countries = Counter([row[3] for row in apk_results if row[3]])
            
            print(f"\n📱 APK用户平台分布:")
            for platform, count in apk_platforms.items():
                print(f"  {platform}: {count} 个")
            
            print(f"\n🌍 APK用户国家分布:")
            for country, count in apk_countries.most_common(5):
                print(f"  {country}: {count} 个")
            
            # 分析Google Play用户
            gp_platforms = Counter([row[1] for row in gp_results])
            gp_countries = Counter([row[3] for row in gp_results if row[3]])
            
            print(f"\n📱 Google Play用户平台分布:")
            for platform, count in gp_platforms.items():
                print(f"  {platform}: {count} 个")
            
            print(f"\n🌍 Google Play用户国家分布:")
            for country, count in gp_countries.most_common(5):
                print(f"  {country}: {count} 个")
        
        # 测试原始查询逻辑
        print("\n🔍 测试原始GROUP BY查询:")
        query_original = """
        SELECT 
            uid,
            login_type,
            source_type,
            country_code,
            login_ip,
            MAX(login_time) as last_login,
            COUNT(*) as login_frequency
        FROM user_login_log 
        WHERE login_time >= %s AND login_time <= %s
            AND source_type = 0
        GROUP BY uid, login_type, source_type, country_code, login_ip
        ORDER BY RAND()
        LIMIT 5
        """
        
        cursor.execute(query_original, (start_date, end_date))
        original_results = cursor.fetchall()
        
        print(f"✅ GROUP BY查询返回 {len(original_results)} 条记录")
        if original_results:
            print("📋 GROUP BY结果样本:")
            for i, row in enumerate(original_results, 1):
                uid, login_type, source_type, country_code, login_ip, last_login, freq = row
                print(f"  {i}. UID: {uid}, 频率: {freq}, 最后登录: {last_login}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_apk_vs_gp() 