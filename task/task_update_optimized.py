import pymysql
import logging
from datetime import datetime
from redis.cluster import RedisCluster
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("task_update.log"),
        logging.StreamHandler()
    ]
)

# 数据库配置
DB_CONFIG = {
    'host': 'xme-envtest-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_task',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': True  # 自动提交,减少事务开销
}

# 连接池配置
POOL_CONFIG = {
    'maxconnections': 10,  # 最大连接数
    'blocking': True,
    'maxusage': None,
    'setsession': [],
    'ping': 1
}

def get_redis_connection():
    """获取Redis集群连接"""
    try:
        host = "xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com"
        port = 6379
        
        redis_conn = RedisCluster(
            host=host,
            port=port,
            decode_responses=True,
            ssl=False,
            max_connections=20,  # 增加连接池大小
            retry_on_timeout=True
        )
        
        redis_conn.ping()
        logging.info("成功连接到Redis集群")
        return redis_conn
    except Exception as e:
        logging.error(f"连接Redis时出错: {str(e)}")
        return None

def delete_user_base_ep_cache_batch(redis_conn, user_ids):
    """
    批量删除用户基础能量点缓存 - 优化版本
    """
    try:
        if not user_ids:
            return
            
        # 分批删除，避免一次性删除太多
        batch_size = 100
        for i in range(0, len(user_ids), batch_size):
            batch_ids = user_ids[i:i + batch_size]
            keys = [f"user:base:ep:{uid}" for uid in batch_ids]
            
            # 使用pipeline提升性能
            pipeline = redis_conn.pipeline()
            for key in keys:
                pipeline.delete(key)
            pipeline.execute()
            
        logging.info(f"成功删除 {len(user_ids)} 个用户的能量点缓存")
    except Exception as e:
        logging.error(f"删除Redis缓存时出错: {str(e)}")

def process_user_tasks_optimized(cursor, user_id, now):
    """
    处理单个用户的任务和能量点 - 优化版本
    """
    shard_num = user_id % 1024
    
    task_table = f"user_task_record_{shard_num}"
    ep_table = f"user_ep_records_{shard_num}"
    
    # 优化：一次查询获取所有需要的数据
    combined_query = f"""
    SELECT 
        t.id as task_id,
        t.task_code,
        COALESCE(SUM(e.ep_amount), 0) as total_ep
    FROM {task_table} t
    LEFT JOIN {ep_table} e ON t.id = e.task_record_id AND e.ep_type = 1 AND e.delete_status = 0
    WHERE t.user_id = %s
    AND t.task_type = 1 
    AND t.task_code NOT IN ('10002','10003','10011')
    AND t.delete_status = 0
    GROUP BY t.id, t.task_code
    """
    
    cursor.execute(combined_query, (user_id,))
    results = cursor.fetchall()
    
    if not results:
        return 0, []
    
    task_ids = [str(row['task_id']) for row in results]
    total_deduction = sum(row['total_ep'] for row in results)
    
    # 限制最大扣除量
    if total_deduction > 950:
        total_deduction = 950
    
    if task_ids:
        # 批量更新task记录
        task_update = f"""
        UPDATE {task_table}
        SET delete_status = 1, delete_time = %s
        WHERE id IN ({','.join(task_ids)})
        """
        cursor.execute(task_update, (now,))
        
        # 批量更新ep记录
        ep_update = f"""
        UPDATE {ep_table}
        SET delete_status = 1, delete_time = %s
        WHERE ep_type = 1 AND task_record_id IN ({','.join(task_ids)})
        """
        cursor.execute(ep_update, (now,))
    
    return total_deduction, task_ids

def process_user_batch(user_batch, now, redis_conn):
    """
    处理一批用户 - 多线程优化
    """
    results = []
    updated_user_ids = []
    
    try:
        # 为每个线程创建独立的数据库连接
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        for user in user_batch:
            user_id = user['user_id']
            current_ep = user['total_base_ep']
            
            try:
                deduction, task_ids = process_user_tasks_optimized(cursor, user_id, now)
                
                if deduction > 0:
                    final_deduction = min(deduction, current_ep)
                    results.append({
                        'user_id': user_id,
                        'deduction': final_deduction,
                        'task_count': len(task_ids)
                    })
                    updated_user_ids.append(user_id)
                    
            except Exception as e:
                logging.error(f"处理用户 {user_id} 时出错: {str(e)}")
                continue
        
        # 批量更新user_base_ep表
        if results:
            for result in results:
                base_ep_update = """
                UPDATE user_base_ep
                SET total_base_ep = total_base_ep - %s, update_time = %s
                WHERE user_id = %s
                """
                cursor.execute(base_ep_update, (result['deduction'], now, result['user_id']))
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logging.error(f"处理用户批次时出错: {str(e)}")
        return [], []
    
    return results, updated_user_ids

def update_user_energy_optimized(batch_size=2000, thread_count=5):
    """
    优化版本的用户能量点更新
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        redis_conn = get_redis_connection()
        if not redis_conn:
            logging.error("无法连接Redis，将只更新MySQL数据")
        
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        total_processed = 0
        last_id = 0
        
        # 预先统计总用户数
        count_query = """
        SELECT COUNT(*) as total_count
        FROM user_base_ep
        WHERE total_base_ep > 0 AND delete_status = 0
        """
        cursor.execute(count_query)
        total_users = cursor.fetchone()['total_count']
        logging.info(f"总共需要处理 {total_users} 个用户")
        
        start_time = time.time()
        
        while True:
            # 获取用户数据
            user_query = """
            SELECT id, user_id, total_base_ep
            FROM user_base_ep
            WHERE id > %s
            AND total_base_ep > 0
            AND delete_status = 0
            ORDER BY id
            LIMIT %s
            """
            cursor.execute(user_query, (last_id, batch_size))
            users = cursor.fetchall()
            
            if not users:
                break
            
            last_id = users[-1]['id']
            
            # 将用户分成小批次，用于多线程处理
            thread_batch_size = len(users) // thread_count + 1
            user_batches = [users[i:i + thread_batch_size] 
                          for i in range(0, len(users), thread_batch_size)]
            
            all_results = []
            all_updated_user_ids = []
            
            # 使用线程池并行处理
            with ThreadPoolExecutor(max_workers=thread_count) as executor:
                future_to_batch = {
                    executor.submit(process_user_batch, batch, now, redis_conn): batch 
                    for batch in user_batches if batch
                }
                
                for future in as_completed(future_to_batch):
                    try:
                        results, updated_user_ids = future.result()
                        all_results.extend(results)
                        all_updated_user_ids.extend(updated_user_ids)
                    except Exception as e:
                        logging.error(f"线程执行出错: {str(e)}")
            
            # 批量删除Redis缓存
            if redis_conn and all_updated_user_ids:
                delete_user_base_ep_cache_batch(redis_conn, all_updated_user_ids)
            
            total_processed += len(users)
            
            # 计算处理速度
            elapsed_time = time.time() - start_time
            users_per_second = total_processed / elapsed_time if elapsed_time > 0 else 0
            eta = (total_users - total_processed) / users_per_second if users_per_second > 0 else 0
            
            batch_users = len(all_results)
            batch_deduction = sum(r['deduction'] for r in all_results)
            
            logging.info(f"进度: {total_processed}/{total_users} ({total_processed/total_users*100:.1f}%) "
                        f"- 本批次: {batch_users} 用户, 扣除: {batch_deduction} EP "
                        f"- 速度: {users_per_second:.1f} 用户/秒, 预计剩余: {eta/60:.1f} 分钟")
            
            # 记录详细信息
            if all_results:
                with open('ep_deduction_details.log', 'a') as f:
                    for result in all_results:
                        f.write(f"用户ID: {result['user_id']}, 扣除能量点: {result['deduction']}, "
                               f"任务数: {result['task_count']}\n")
            
            if len(users) < batch_size:
                break
        
        total_time = time.time() - start_time
        logging.info(f"所有批次处理完成，共处理 {total_processed} 个用户，耗时 {total_time:.1f} 秒")
        
    except Exception as e:
        logging.error(f"数据库操作错误: {str(e)}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        if 'redis_conn' in locals() and redis_conn:
            redis_conn.close()

def check_user_energy_optimized(batch_size=5000):
    """
    优化版本的检查模式
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        total_processed = 0
        total_affected_users = 0
        total_deduction_amount = 0
        last_id = 0
        
        start_time = time.time()
        
        while True:
            user_query = """
            SELECT id, user_id, total_base_ep
            FROM user_base_ep
            WHERE id > %s
            AND total_base_ep > 0
            AND delete_status = 0
            ORDER BY id
            LIMIT %s
            """
            cursor.execute(user_query, (last_id, batch_size))
            users = cursor.fetchall()
            
            if not users:
                break
            
            last_id = users[-1]['id']
            
            # 批量检查用户任务
            user_ids = [user['user_id'] for user in users]
            affected_users = []
            
            # 优化：批量查询所有用户的任务数据
            for user in users:
                user_id = user['user_id']
                current_ep = user['total_base_ep']
                shard_num = user_id % 1024
                
                task_table = f"user_task_record_{shard_num}"
                ep_table = f"user_ep_records_{shard_num}"
                
                combined_query = f"""
                SELECT 
                    COUNT(DISTINCT t.id) as task_count,
                    GROUP_CONCAT(DISTINCT t.task_code) as task_codes,
                    COALESCE(SUM(e.ep_amount), 0) as total_ep
                FROM {task_table} t
                LEFT JOIN {ep_table} e ON t.id = e.task_record_id AND e.ep_type = 1 AND e.delete_status = 0
                WHERE t.user_id = %s
                AND t.task_type = 1 
                AND t.task_code NOT IN ('10002','10003','10011')
                AND t.delete_status = 0
                """
                
                cursor.execute(combined_query, (user_id,))
                result = cursor.fetchone()
                
                if result and result['task_count'] > 0:
                    total_deduction = min(result['total_ep'], 950)
                    total_affected_users += 1
                    total_deduction_amount += total_deduction
                    
                    affected_users.append({
                        'user_id': user_id,
                        'current_ep': current_ep,
                        'deduction': total_deduction,
                        'task_count': result['task_count'],
                        'task_codes': result['task_codes'] or ''
                    })
            
            # 批量写入检查结果
            if affected_users:
                with open('check_details.log', 'a') as f:
                    for user_data in affected_users:
                        f.write(f"用户ID: {user_data['user_id']}\n")
                        f.write(f"  当前能量点: {user_data['current_ep']}\n")
                        f.write(f"  将扣除能量点: {user_data['deduction']}\n")
                        f.write(f"  影响任务数: {user_data['task_count']}\n")
                        f.write(f"  任务代码: {user_data['task_codes']}\n")
                        f.write("----------------------------------------\n")
            
            total_processed += len(users)
            
            elapsed_time = time.time() - start_time
            users_per_second = total_processed / elapsed_time if elapsed_time > 0 else 0
            
            logging.info(f"检查进度: {total_processed} 用户 - "
                        f"受影响: {total_affected_users} 用户 - "
                        f"速度: {users_per_second:.1f} 用户/秒")
            
            if len(users) < batch_size:
                break
        
        total_time = time.time() - start_time
        logging.info("=== 检查汇总 ===")
        logging.info(f"总检查用户数: {total_processed}")
        logging.info(f"受影响用户数: {total_affected_users}")
        logging.info(f"总扣除能量点: {total_deduction_amount}")
        logging.info(f"总耗时: {total_time:.1f} 秒")
        
    except Exception as e:
        logging.error(f"数据库操作错误: {str(e)}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    import argparse
    parser = argparse.ArgumentParser(description='用户能量点更新工具 - 优化版本')
    parser.add_argument('--check', action='store_true', help='只检查数据，不做更新')
    parser.add_argument('--batch-size', type=int, default=2000, help='每批处理的用户数')
    parser.add_argument('--threads', type=int, default=5, help='线程数（仅更新模式）')
    args = parser.parse_args()

    if args.check:
        logging.info("开始检查任务...")
        check_user_energy_optimized(batch_size=args.batch_size)
        logging.info("检查任务完成")
    else:
        logging.info("开始更新任务...")
        update_user_energy_optimized(batch_size=args.batch_size, thread_count=args.threads)
        logging.info("更新任务完成")

if __name__ == "__main__":
    main() 