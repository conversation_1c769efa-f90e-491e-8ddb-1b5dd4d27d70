#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 测试数据库连接和基本分析功能
"""

from user_login_analysis import UserLoginAnalyzer

def main():
    print("🚀 快速测试用户登录分析工具")
    print("=" * 50)
    
    # 使用已配置的数据库连接（无需额外配置）
    analyzer = UserLoginAnalyzer(
        host="xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com",
        database="media_user",
        user="pro-user-user",
        password="VcEVqaE5HX",
        port=3306
    )
    
    # 连接数据库
    if not analyzer.connect_db():
        print("❌ 数据库连接失败")
        return
    
    try:
        print("\n🔍 开始分析最近7天的数据...")
        
        # 1. 来源类型统计
        print("\n📊 来源类型统计：")
        source_stats = analyzer.get_source_type_statistics(days=7)
        
        # 2. 平台统计
        print("\n📱 登录平台统计：")
        platform_stats = analyzer.get_platform_statistics(days=7)
        
        # 3. Android APK vs Google Play对比（重点分析）
        print("\n🎯 Android APK vs Google Play 对比分析：")
        android_comparison = analyzer.analyze_android_apk_vs_gp(sample_size=1000, days=7)
        
        print("\n✅ 测试完成！分析结果如上所示。")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 关闭数据库连接
        analyzer.close_connection()

if __name__ == "__main__":
    main() 