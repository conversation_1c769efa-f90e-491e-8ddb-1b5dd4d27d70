-- 修改字段定义和更新数据 - push_message表 (512-767)
-- 生成时间: 2025-06-24 11:21:20.958528

-- 修改 created_time 字段默认值 (512-767)
ALTER TABLE media_message.push_message512 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message513 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message514 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message515 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message516 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message517 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message518 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message519 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message520 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message521 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message522 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message523 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message524 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message525 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message526 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message527 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message528 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message529 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message530 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message531 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message532 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message533 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message534 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message535 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message536 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message537 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message538 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message539 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message540 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message541 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message542 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message543 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message544 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message545 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message546 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message547 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message548 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message549 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message550 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message551 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message552 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message553 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message554 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message555 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message556 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message557 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message558 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message559 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message560 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message561 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message562 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message563 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message564 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message565 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message566 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message567 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message568 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message569 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message570 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message571 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message572 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message573 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message574 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message575 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message576 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message577 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message578 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message579 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message580 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message581 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message582 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message583 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message584 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message585 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message586 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message587 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message588 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message589 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message590 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message591 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message592 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message593 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message594 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message595 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message596 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message597 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message598 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message599 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message600 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message601 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message602 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message603 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message604 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message605 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message606 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message607 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message608 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message609 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message610 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message611 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message612 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message613 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message614 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message615 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message616 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message617 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message618 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message619 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message620 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message621 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message622 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message623 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message624 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message625 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message626 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message627 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message628 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message629 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message630 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message631 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message632 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message633 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message634 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message635 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message636 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message637 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message638 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message639 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message640 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message641 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message642 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message643 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message644 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message645 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message646 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message647 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message648 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message649 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message650 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message651 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message652 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message653 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message654 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message655 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message656 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message657 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message658 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message659 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message660 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message661 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message662 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message663 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message664 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message665 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message666 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message667 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message668 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message669 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message670 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message671 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message672 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message673 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message674 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message675 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message676 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message677 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message678 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message679 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message680 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message681 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message682 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message683 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message684 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message685 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message686 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message687 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message688 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message689 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message690 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message691 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message692 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message693 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message694 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message695 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message696 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message697 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message698 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message699 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message700 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message701 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message702 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message703 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message704 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message705 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message706 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message707 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message708 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message709 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message710 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message711 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message712 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message713 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message714 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message715 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message716 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message717 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message718 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message719 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message720 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message721 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message722 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message723 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message724 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message725 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message726 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message727 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message728 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message729 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message730 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message731 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message732 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message733 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message734 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message735 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message736 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message737 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message738 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message739 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message740 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message741 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message742 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message743 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message744 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message745 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message746 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message747 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message748 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message749 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message750 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message751 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message752 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message753 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message754 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message755 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message756 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message757 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message758 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message759 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message760 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message761 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message762 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message763 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message764 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message765 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message766 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message767 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 修改 updated_time 字段默认值 (512-767)
ALTER TABLE media_message.push_message512 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message513 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message514 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message515 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message516 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message517 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message518 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message519 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message520 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message521 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message522 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message523 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message524 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message525 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message526 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message527 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message528 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message529 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message530 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message531 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message532 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message533 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message534 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message535 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message536 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message537 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message538 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message539 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message540 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message541 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message542 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message543 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message544 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message545 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message546 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message547 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message548 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message549 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message550 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message551 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message552 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message553 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message554 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message555 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message556 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message557 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message558 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message559 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message560 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message561 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message562 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message563 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message564 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message565 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message566 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message567 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message568 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message569 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message570 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message571 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message572 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message573 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message574 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message575 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message576 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message577 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message578 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message579 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message580 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message581 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message582 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message583 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message584 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message585 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message586 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message587 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message588 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message589 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message590 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message591 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message592 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message593 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message594 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message595 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message596 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message597 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message598 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message599 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message600 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message601 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message602 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message603 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message604 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message605 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message606 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message607 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message608 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message609 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message610 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message611 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message612 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message613 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message614 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message615 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message616 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message617 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message618 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message619 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message620 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message621 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message622 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message623 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message624 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message625 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message626 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message627 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message628 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message629 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message630 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message631 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message632 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message633 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message634 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message635 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message636 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message637 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message638 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message639 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message640 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message641 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message642 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message643 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message644 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message645 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message646 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message647 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message648 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message649 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message650 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message651 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message652 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message653 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message654 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message655 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message656 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message657 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message658 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message659 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message660 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message661 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message662 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message663 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message664 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message665 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message666 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message667 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message668 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message669 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message670 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message671 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message672 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message673 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message674 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message675 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message676 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message677 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message678 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message679 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message680 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message681 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message682 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message683 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message684 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message685 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message686 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message687 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message688 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message689 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message690 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message691 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message692 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message693 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message694 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message695 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message696 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message697 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message698 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message699 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message700 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message701 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message702 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message703 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message704 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message705 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message706 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message707 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message708 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message709 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message710 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message711 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message712 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message713 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message714 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message715 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message716 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message717 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message718 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message719 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message720 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message721 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message722 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message723 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message724 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message725 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message726 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message727 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message728 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message729 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message730 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message731 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message732 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message733 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message734 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message735 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message736 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message737 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message738 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message739 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message740 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message741 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message742 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message743 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message744 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message745 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message746 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message747 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message748 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message749 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message750 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message751 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message752 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message753 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message754 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message755 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message756 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message757 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message758 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message759 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message760 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message761 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message762 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message763 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message764 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message765 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message766 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message767 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 更新空值数据 (512-767)
UPDATE media_message.push_message512 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message513 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message514 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message515 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message516 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message517 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message518 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message519 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message520 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message521 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message522 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message523 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message524 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message525 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message526 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message527 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message528 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message529 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message530 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message531 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message532 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message533 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message534 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message535 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message536 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message537 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message538 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message539 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message540 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message541 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message542 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message543 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message544 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message545 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message546 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message547 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message548 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message549 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message550 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message551 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message552 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message553 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message554 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message555 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message556 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message557 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message558 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message559 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message560 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message561 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message562 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message563 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message564 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message565 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message566 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message567 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message568 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message569 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message570 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message571 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message572 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message573 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message574 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message575 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message576 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message577 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message578 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message579 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message580 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message581 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message582 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message583 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message584 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message585 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message586 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message587 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message588 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message589 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message590 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message591 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message592 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message593 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message594 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message595 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message596 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message597 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message598 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message599 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message600 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message601 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message602 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message603 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message604 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message605 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message606 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message607 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message608 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message609 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message610 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message611 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message612 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message613 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message614 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message615 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message616 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message617 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message618 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message619 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message620 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message621 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message622 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message623 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message624 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message625 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message626 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message627 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message628 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message629 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message630 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message631 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message632 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message633 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message634 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message635 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message636 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message637 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message638 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message639 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message640 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message641 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message642 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message643 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message644 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message645 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message646 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message647 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message648 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message649 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message650 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message651 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message652 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message653 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message654 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message655 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message656 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message657 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message658 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message659 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message660 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message661 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message662 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message663 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message664 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message665 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message666 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message667 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message668 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message669 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message670 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message671 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message672 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message673 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message674 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message675 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message676 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message677 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message678 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message679 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message680 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message681 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message682 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message683 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message684 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message685 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message686 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message687 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message688 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message689 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message690 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message691 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message692 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message693 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message694 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message695 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message696 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message697 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message698 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message699 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message700 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message701 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message702 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message703 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message704 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message705 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message706 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message707 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message708 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message709 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message710 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message711 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message712 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message713 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message714 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message715 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message716 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message717 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message718 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message719 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message720 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message721 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message722 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message723 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message724 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message725 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message726 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message727 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message728 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message729 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message730 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message731 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message732 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message733 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message734 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message735 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message736 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message737 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message738 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message739 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message740 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message741 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message742 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message743 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message744 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message745 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message746 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message747 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message748 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message749 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message750 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message751 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message752 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message753 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message754 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message755 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message756 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message757 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message758 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message759 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message760 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message761 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message762 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message763 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message764 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message765 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message766 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message767 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;