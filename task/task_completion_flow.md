# 任务完成与奖励领取流程

## 流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant TaskService as 任务服务
    participant DB as 数据库

    User->>API: 提交任务完成请求
    API->>TaskService: 验证任务完成条件
    
    alt 任务条件未满足
        TaskService-->>API: 返回任务未完成错误
        API-->>User: 显示任务未完成提示
    else 任务条件已满足
        TaskService->>DB: 更新user_task_completions表(任务状态)
        TaskService->>EnergyService: 发放任务奖励能量
        
        EnergyService->>DB: 开始事务
        EnergyService->>DB: 写入energy_points_records表
        EnergyService->>DB: 更新user_hourly_energy表
        EnergyService->>DB: 更新user_mining_status表
        EnergyService->>DB: 提交事务
        
        EnergyService-->>TaskService: 返回奖励发放结果
        TaskService-->>API: 返回任务完成成功
        API-->>User: 显示任务完成和奖励信息
    end

    Note over User,DB: 用户领取任务奖励流程
    
    User->>API: 提交奖励领取请求
    API->>TaskService: 验证奖励领取条件
    
    alt 奖励已领取或任务未完成
        TaskService-->>API: 返回领取失败错误
        API-->>User: 显示领取失败提示
    else 可以领取奖励
        TaskService->>DB: 更新user_task_completions表(奖励领取状态)
        TaskService->>EnergyService: 发放任务奖励能量
        
        EnergyService->>DB: 开始事务
        EnergyService->>DB: 写入energy_points_records表
        EnergyService->>DB: 更新user_hourly_energy表
        EnergyService->>DB: 更新user_mining_status表
        EnergyService->>DB: 提交事务
        
        EnergyService-->>TaskService: 返回奖励发放结果
        TaskService-->>API: 返回奖励领取成功
        API-->>User: 显示奖励领取成功信息
    end
```

## 数据流转图

```mermaid
flowchart TD
    %% 定义节点
    User[用户] --> |完成任务/领取奖励| API[API服务]
    API --> TaskService[任务服务]
    TaskService --> |验证任务状态| UserTasks[(user_task_completions表)]
    TaskService --> |发放奖励| EnergyService[能量服务]
    
    EnergyService --> |创建能量记录| EPR[(energy_points_records表)]
    EnergyService --> |更新小时能量| UHE[(user_hourly_energy表)]
    EnergyService --> |更新挖矿状态| UMS[(user_mining_status表)]
    
    EPR -.-> |影响| FBT[好友能量加成任务]
    UHE -.-> |影响| FBT
    FBT -.-> |计算加成| UHE
    
    EPR -.-> |影响| ST[小时能量汇总任务]
    ST -.-> |汇总更新| UHE
    UHE -.-> |影响| XME[XME结算任务]
    
    %% 样式
    classDef user fill:#f9f,stroke:#333,stroke-width:2px;
    classDef service fill:#bbf,stroke:#33f,stroke-width:2px;
    classDef table fill:#bfb,stroke:#3f3,stroke-width:2px;
    classDef task fill:#fbb,stroke:#f33,stroke-width:2px;
    
    class User user;
    class API,TaskService,EnergyService service;
    class UserTasks,EPR,UHE,UMS table;
    class FBT,ST,XME task;
```

## 任务完成与奖励领取流程说明

### 1. 任务完成流程

1. **用户提交任务完成请求**：
   - 用户通过前端界面提交任务完成请求
   - 请求发送到API服务：`POST /api/v1/tasks/{taskId}/complete`

2. **任务验证**：
   - 任务服务验证任务完成条件
   - 检查任务是否存在、是否已过期、是否已完成等

3. **任务状态更新**：
   - 更新`user_task_completions`表中的任务状态为已完成
   - 记录任务完成时间

4. **奖励发放**：
   - 能量服务在一个事务中执行以下操作：
     - 在`energy_points_records`表中创建新的能量记录，记录来源为任务奖励
     - 更新`user_hourly_energy`表中用户当前小时的能量点
     - 更新`user_mining_status`表中用户的最后活跃时间和挖矿状态

5. **返回结果**：
   - 返回任务完成成功信息，包含奖励点数和类型

### 2. 奖励领取流程

1. **用户提交奖励领取请求**：
   - 用户通过前端界面提交奖励领取请求
   - 请求发送到API服务：`POST /api/v1/tasks/{taskId}/claim`

2. **领取条件验证**：
   - 任务服务验证奖励领取条件
   - 检查任务是否已完成、奖励是否已领取等

3. **奖励状态更新**：
   - 更新`user_task_completions`表中的奖励领取状态
   - 记录奖励领取时间

4. **奖励发放**：
   - 能量服务在一个事务中执行以下操作：
     - 在`energy_points_records`表中创建新的能量记录，记录来源为任务奖励
     - 更新`user_hourly_energy`表中用户当前小时的能量点
     - 更新`user_mining_status`表中用户的最后活跃时间和挖矿状态

5. **返回结果**：
   - 返回奖励领取成功信息，包含奖励点数和类型

### 3. 数据影响

任务完成和奖励领取后，新增的能量点会：

1. **立即反映**：
   - 用户界面立即显示更新后的能量点
   - 能量点记录可在用户的能量历史中查看

2. **参与后续流程**：
   - 作为好友能量加成的基础，为用户的好友提供加成
   - 被小时能量汇总任务汇总，确保数据一致性
   - 参与XME结算，转换为XME奖励

3. **能量衰减**：
   - 在下一个小时的能量衰减任务中被衰减
   - 衰减后的能量继续参与挖矿流程

### 4. 数据一致性保障

1. **事务处理**：
   - 所有表操作在一个事务中完成，确保原子性
   - 任何步骤失败都会导致整个事务回滚

2. **幂等性设计**：
   - 防止重复提交和重复领取
   - 使用任务状态和时间戳标记确保每个任务只能完成和领取一次

3. **异常处理**：
   - 系统捕获并记录所有异常
   - 提供友好的错误提示和恢复机制
