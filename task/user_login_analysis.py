#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户登录日志分析脚本
基于user_login_log表结构进行来源统计和抽样分析
"""

import pymysql
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import random
from datetime import datetime, timedelta
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class UserLoginAnalyzer:
    def __init__(self, host, database, user, password, port=3306):
        """
        初始化数据库连接
        """
        self.host = "xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com"
        self.database = "media_user"
        self.user = "pro-user-user"
        self.password = "VcEVqaE5HX"
        self.port = 3306
        self.connection = None
        
        # 定义来源类型映射
        self.source_type_mapping = {
            0: 'ANDROID (APK)',
            1: 'IOS', 
            2: 'WEB',
            3: 'CONTENT',
            4: 'ANDROID-GP (Google Play)',
            5: 'UNKNOWN'
        }
        
        # 定义登录平台映射
        self.platform_mapping = {
            'platform-login': '自有平台-登录',
            'platform-register': '自有平台-注册',
            'platform': '自有平台',
            'bee': 'Bee平台',
            'google': 'Google Play'
        }
        
        # 自动建立数据库连接
        self.connect_db()
    
    def connect_db(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password,
                port=self.port,
                charset='utf8mb4'
            )
            print("✅ 数据库连接成功!")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("🔒 数据库连接已关闭")
    
    def get_source_type_statistics(self, days=3):
        """
        统计来源类型占比
        
        Args:
            days: 统计最近多少天的数据，默认3天
        """
        try:
            cursor = self.connection.cursor()
            
            # 计算日期范围
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            query = """
            SELECT 
                source_type,
                COUNT(*) as access_count,
                COUNT(DISTINCT uid) as unique_users
            FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
                AND source_type IS NOT NULL
            GROUP BY source_type
            ORDER BY access_count DESC
            """
            
            cursor.execute(query, (start_date, end_date))
            results = cursor.fetchall()
            
            if not results:
                print("⚠️ 指定时间范围内没有找到数据")
                return None
            
            # 处理结果
            data = []
            total_access = sum([row[1] for row in results])
            total_users = sum([row[2] for row in results])
            
            print(f"\n📊 来源类型统计 ({start_date} 到 {end_date})")
            print("=" * 60)
            print(f"{'来源类型':<15} {'访问次数':<12} {'占比':<8} {'独立用户':<12} {'用户占比':<8}")
            print("-" * 60)
            
            for row in results:
                source_type = row[0]
                access_count = row[1]
                unique_users = row[2]
                
                source_name = self.source_type_mapping.get(source_type, f"未知({source_type})")
                access_percentage = (access_count / total_access) * 100
                user_percentage = (unique_users / total_users) * 100
                
                print(f"{source_name:<15} {access_count:<12} {access_percentage:>6.2f}% {unique_users:<12} {user_percentage:>6.2f}%")
                
                data.append({
                    'source_type': source_type,
                    'source_name': source_name,
                    'access_count': access_count,
                    'access_percentage': access_percentage,
                    'unique_users': unique_users,
                    'user_percentage': user_percentage
                })
            
            print("-" * 60)
            print(f"{'总计':<15} {total_access:<12} {'100.00%':<8} {total_users:<12} {'100.00%':<8}")
            
            cursor.close()
            return data
            
        except Exception as e:
            print(f"❌ 查询来源统计失败: {e}")
            return None
    
    def get_platform_statistics(self, days=3):
        """
        统计登录平台占比
        
        Args:
            days: 统计最近多少天的数据，默认3天
        """
        try:
            cursor = self.connection.cursor()
            
            # 计算日期范围
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            query = """
            SELECT 
                login_type,
                COUNT(*) as access_count,
                COUNT(DISTINCT uid) as unique_users
            FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
                AND login_type IS NOT NULL
            GROUP BY login_type
            ORDER BY access_count DESC
            """
            
            cursor.execute(query, (start_date, end_date))
            results = cursor.fetchall()
            
            if not results:
                print("⚠️ 指定时间范围内没有找到平台数据")
                return None
            
            # 处理结果
            data = []
            total_access = sum([row[1] for row in results])
            total_users = sum([row[2] for row in results])
            
            print(f"\n📱 登录平台统计 ({start_date} 到 {end_date})")
            print("=" * 60)
            print(f"{'登录平台':<15} {'访问次数':<12} {'占比':<8} {'独立用户':<12} {'用户占比':<8}")
            print("-" * 60)
            
            for row in results:
                login_type = row[0]
                access_count = row[1]
                unique_users = row[2]
                
                platform_name = self.platform_mapping.get(login_type, login_type)
                access_percentage = (access_count / total_access) * 100
                user_percentage = (unique_users / total_users) * 100
                
                print(f"{platform_name:<15} {access_count:<12} {access_percentage:>6.2f}% {unique_users:<12} {user_percentage:>6.2f}%")
                
                data.append({
                    'login_type': login_type,
                    'platform_name': platform_name,
                    'access_count': access_count,
                    'access_percentage': access_percentage,
                    'unique_users': unique_users,
                    'user_percentage': user_percentage
                })
            
            print("-" * 60)
            print(f"{'总计':<15} {total_access:<12} {'100.00%':<8} {total_users:<12} {'100.00%':<8}")
            
            cursor.close()
            return data
            
        except Exception as e:
            print(f"❌ 查询平台统计失败: {e}")
            return None
    
    def sample_users_by_platform(self, sample_size=1000, target_platforms=['platform', 'google'], days=3):
        """
        抽样分析指定平台的用户
        
        Args:
            sample_size: 抽样数量，默认1000
            target_platforms: 目标平台列表，默认['platform', 'google'] (自有平台和Google Play)
            days: 统计最近多少天的数据，默认3天
        """
        try:
            cursor = self.connection.cursor()
            
            # 计算日期范围
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            platform_str = "', '".join(target_platforms)
            
            query = f"""
            SELECT DISTINCT
                uid,
                login_type,
                source_type,
                country_code,
                login_ip,
                MAX(login_time) as last_access,
                COUNT(*) as access_frequency
            FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
                AND login_type IN ('{platform_str}')
            GROUP BY uid, login_type, source_type, country_code, login_ip
            ORDER BY RAND()
            LIMIT %s
            """
            
            cursor.execute(query, (start_date, end_date, sample_size))
            results = cursor.fetchall()
            
            if not results:
                print("⚠️ 指定条件下没有找到用户数据")
                return None
            
            print(f"\n🎯 抽样用户分析 (样本数: {len(results)})")
            print("=" * 80)
            
            # 统计抽样结果
            platform_count = Counter()
            source_count = Counter()
            country_count = Counter()
            
            sample_data = []
            
            for row in results:
                uid, login_type, source_type, country_code, login_ip, last_access, access_frequency = row
                
                platform_count[login_type] += 1
                source_count[source_type] += 1
                if country_code:
                    country_count[country_code] += 1
                
                sample_data.append({
                    'uid': uid,
                    'login_type': login_type,
                    'platform_name': self.platform_mapping.get(login_type, login_type),
                    'source_type': source_type,
                    'source_name': self.source_type_mapping.get(source_type, f"未知({source_type})"),
                    'country_code': country_code,
                    'login_ip': login_ip,
                    'last_access': last_access,
                    'access_frequency': access_frequency
                })
            
            # 显示平台分布
            print("📱 抽样用户平台分布:")
            for platform, count in platform_count.most_common():
                platform_name = self.platform_mapping.get(platform, platform)
                percentage = (count / len(results)) * 100
                print(f"  {platform_name}: {count} 用户 ({percentage:.1f}%)")
            
            # 显示来源分布
            print("\n📊 抽样用户来源分布:")
            for source, count in source_count.most_common():
                source_name = self.source_type_mapping.get(source, f"未知({source})")
                percentage = (count / len(results)) * 100
                print(f"  {source_name}: {count} 用户 ({percentage:.1f}%)")
            
            # 显示国家分布（top 10）
            print("\n🌍 抽样用户国家分布 (Top 10):")
            for country, count in country_count.most_common(10):
                percentage = (count / len(results)) * 100
                print(f"  {country or '未知'}: {count} 用户 ({percentage:.1f}%)")
            
            cursor.close()
            return sample_data
            
        except Exception as e:
            print(f"❌ 抽样分析失败: {e}")
            return None
    
    def analyze_apk_vs_googleplay(self, sample_size=1000, days=3):
        """
        专门分析APK(自有平台)与Google Play用户的对比
        基于source_type字段进行区分：APK(source_type=0) vs Google Play(source_type=4)
        
        Args:
            sample_size: 每个平台的抽样数量
            days: 统计最近多少天的数据
        """
        print(f"\n🔍 APK vs Google Play 用户对比分析")
        print("=" * 80)
        
        try:
            cursor = self.connection.cursor()
            
            # 计算日期范围
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            print(f"📅 分析时间范围: {start_date} 到 {end_date}")
            
            # 获取APK用户数据 (source_type = 0)
            query_apk = """
            SELECT DISTINCT
                uid,
                login_type,
                source_type,
                country_code,
                login_ip,
                MAX(login_time) as last_access,
                COUNT(*) as access_frequency
            FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
                AND source_type = 0
            GROUP BY uid, login_type, source_type, country_code, login_ip
            ORDER BY RAND()
            LIMIT %s
            """
            
            cursor.execute(query_apk, (start_date, end_date, sample_size))
            apk_results = cursor.fetchall()
            
            # 获取Google Play用户数据 (source_type = 4)
            query_gp = """
            SELECT DISTINCT
                uid,
                login_type,
                source_type,
                country_code,
                login_ip,
                MAX(login_time) as last_access,
                COUNT(*) as access_frequency
            FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
                AND source_type = 4
            GROUP BY uid, login_type, source_type, country_code, login_ip
            ORDER BY RAND()
            LIMIT %s
            """
            
            cursor.execute(query_gp, (start_date, end_date, sample_size))
            gp_results = cursor.fetchall()
            
            print(f"📊 获取到 {len(apk_results)} 个APK用户样本")
            print(f"📊 获取到 {len(gp_results)} 个Google Play用户样本")
            
            if not apk_results and not gp_results:
                print("⚠️ 警告：在指定时间范围内没有找到任何用户数据")
                return {}
            
            # 转换为字典格式以便对比
            apk_users = []
            for row in apk_results:
                uid, login_type, source_type, country_code, login_ip, last_access, access_frequency = row
                apk_users.append({
                    'uid': uid,
                    'login_type': login_type,
                    'platform_name': self.platform_mapping.get(login_type, login_type),
                    'source_type': source_type,
                    'source_name': self.source_type_mapping.get(source_type, f"未知({source_type})"),
                    'country_code': country_code,
                    'login_ip': login_ip,
                    'last_access': last_access,
                    'access_frequency': access_frequency
                })
            
            gp_users = []
            for row in gp_results:
                uid, login_type, source_type, country_code, login_ip, last_access, access_frequency = row
                gp_users.append({
                    'uid': uid,
                    'login_type': login_type,
                    'platform_name': self.platform_mapping.get(login_type, login_type),
                    'source_type': source_type,
                    'source_name': self.source_type_mapping.get(source_type, f"未知({source_type})"),
                    'country_code': country_code,
                    'login_ip': login_ip,
                    'last_access': last_access,
                    'access_frequency': access_frequency
                })
            
            # 进行对比分析
            if apk_users and gp_users:
                self._compare_android_sources(apk_users, gp_users)
            elif apk_users:
                print("✅ 只找到APK用户数据")
            elif gp_users:
                print("✅ 只找到Google Play用户数据")
            
            cursor.close()
            return {'apk_users': apk_users, 'gp_users': gp_users}
            
        except Exception as e:
            print(f"❌ APK vs Google Play分析失败: {e}")
            return {}
    
    def analyze_android_apk_vs_gp(self, days=3, sample_size=1000):
        """
        分析Android APK用户 vs Google Play用户的详细对比
        
        Args:
            days (int): 分析的天数，默认3天
            sample_size (int): 每个平台的样本数量，默认1000
        
        Returns:
            dict: 包含对比分析结果的字典
        """
        try:
            cursor = self.connection.cursor()
            
            # 计算日期范围
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            print(f"📅 分析时间范围: {start_date} 到 {end_date}")
            
            # 查询APK用户 (source_type = 0)
            query_apk = """
            SELECT 
                uid,
                login_type,
                source_type,
                country_code,
                login_ip,
                MAX(login_time) as last_access,
                COUNT(*) as access_frequency
            FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
                AND source_type = 0
            GROUP BY uid, login_type, source_type, country_code, login_ip
            ORDER BY RAND()
            LIMIT %s
            """
            
            cursor.execute(query_apk, (start_date, end_date, sample_size))
            apk_users = cursor.fetchall()
            
            # 查询Google Play用户 (source_type = 4)
            query_gp = """
            SELECT 
                uid,
                login_type,
                source_type,
                country_code,
                login_ip,
                MAX(login_time) as last_access,
                COUNT(*) as access_frequency
            FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
                AND source_type = 4
            GROUP BY uid, login_type, source_type, country_code, login_ip
            ORDER BY RAND()
            LIMIT %s
            """
            
            cursor.execute(query_gp, (start_date, end_date, sample_size))
            gp_users = cursor.fetchall()
            
            print(f"📊 获取到 {len(apk_users)} 个APK用户样本")
            print(f"📊 获取到 {len(gp_users)} 个Google Play用户样本")
            
            if not apk_users and not gp_users:
                print("⚠️ 警告：在指定时间范围内没有找到任何用户数据")
                return {}
            
            analysis_result = {
                'time_range': f"{start_date} 到 {end_date}",
                'apk_users': {
                    'total_count': len(apk_users),
                    'platform_distribution': {},
                    'country_distribution': {},
                    'access_frequency_stats': {},
                    'sample_users': []
                },
                'gp_users': {
                    'total_count': len(gp_users),
                    'platform_distribution': {},
                    'country_distribution': {},
                    'access_frequency_stats': {},
                    'sample_users': []
                },
                'comparison': {}
            }
            
            # 分析APK用户
            if apk_users:
                # 平台分布
                apk_platforms = Counter([user[1] for user in apk_users])
                analysis_result['apk_users']['platform_distribution'] = dict(apk_platforms)
                
                # 国家分布
                apk_countries = Counter([user[3] for user in apk_users if user[3]])
                analysis_result['apk_users']['country_distribution'] = dict(apk_countries)
                
                # 访问频率统计
                apk_frequencies = [user[6] for user in apk_users]
                if apk_frequencies:
                    analysis_result['apk_users']['access_frequency_stats'] = {
                        'average': sum(apk_frequencies) / len(apk_frequencies),
                        'max': max(apk_frequencies),
                        'min': min(apk_frequencies)
                    }
                
                # 样本用户（前10个）
                for user in apk_users[:10]:
                    analysis_result['apk_users']['sample_users'].append({
                        'uid': user[0],
                        'login_type': user[1],
                        'country': user[3],
                        'last_access': str(user[5]),
                        'access_frequency': user[6]
                    })
            
            # 分析Google Play用户
            if gp_users:
                # 平台分布
                gp_platforms = Counter([user[1] for user in gp_users])
                analysis_result['gp_users']['platform_distribution'] = dict(gp_platforms)
                
                # 国家分布
                gp_countries = Counter([user[3] for user in gp_users if user[3]])
                analysis_result['gp_users']['country_distribution'] = dict(gp_countries)
                
                # 访问频率统计
                gp_frequencies = [user[6] for user in gp_users]
                if gp_frequencies:
                    analysis_result['gp_users']['access_frequency_stats'] = {
                        'average': sum(gp_frequencies) / len(gp_frequencies),
                        'max': max(gp_frequencies),
                        'min': min(gp_frequencies)
                    }
                
                # 样本用户（前10个）
                for user in gp_users[:10]:
                    analysis_result['gp_users']['sample_users'].append({
                        'uid': user[0],
                        'login_type': user[1],
                        'country': user[3],
                        'last_access': str(user[5]),
                        'access_frequency': user[6]
                    })
            
            # 对比分析
            if apk_users and gp_users:
                analysis_result['comparison'] = {
                    'user_count_ratio': f"APK:GP = {len(apk_users)}:{len(gp_users)}",
                    'top_countries_apk': list(Counter([user[3] for user in apk_users if user[3]]).most_common(5)),
                    'top_countries_gp': list(Counter([user[3] for user in gp_users if user[3]]).most_common(5)),
                    'avg_access_frequency_apk': sum([user[6] for user in apk_users]) / len(apk_users) if apk_users else 0,
                    'avg_access_frequency_gp': sum([user[6] for user in gp_users]) / len(gp_users) if gp_users else 0
                }
            
            return analysis_result
            
        except Exception as e:
            print(f"❌ 分析Android APK vs Google Play用户时出错: {e}")
            return {}
    
    def _compare_platforms(self, apk_users, gp_users):
        """
        对比两个平台的用户特征
        """
        print(f"\n📈 详细对比分析:")
        print("-" * 50)
        
        # 来源设备对比
        apk_sources = Counter([user['source_type'] for user in apk_users])
        gp_sources = Counter([user['source_type'] for user in gp_users])
        
        print("📱 设备来源对比:")
        print(f"{'设备类型':<15} {'APK用户':<12} {'Google Play':<12}")
        print("-" * 40)
        
        all_sources = set(list(apk_sources.keys()) + list(gp_sources.keys()))
        for source in all_sources:
            source_name = self.source_type_mapping.get(source, f"未知({source})")
            apk_count = apk_sources.get(source, 0)
            gp_count = gp_sources.get(source, 0)
            apk_pct = (apk_count / len(apk_users)) * 100 if apk_users else 0
            gp_pct = (gp_count / len(gp_users)) * 100 if gp_users else 0
            print(f"{source_name:<15} {apk_count:>4}({apk_pct:>5.1f}%) {gp_count:>4}({gp_pct:>5.1f}%)")
        
        # 国家分布对比
        apk_countries = Counter([user['country_code'] for user in apk_users if user['country_code']])
        gp_countries = Counter([user['country_code'] for user in gp_users if user['country_code']])
        
        print(f"\n🌍 国家分布对比 (Top 5):")
        print(f"{'国家':<10} {'APK用户':<12} {'Google Play':<12}")
        print("-" * 35)
        
        all_countries = set(list(apk_countries.keys()) + list(gp_countries.keys()))
        top_countries = sorted(all_countries, 
                             key=lambda x: apk_countries.get(x, 0) + gp_countries.get(x, 0), 
                             reverse=True)[:5]
        
        for country in top_countries:
            apk_count = apk_countries.get(country, 0)
            gp_count = gp_countries.get(country, 0)
            apk_pct = (apk_count / len(apk_users)) * 100 if apk_users else 0
            gp_pct = (gp_count / len(gp_users)) * 100 if gp_users else 0
            print(f"{country:<10} {apk_count:>4}({apk_pct:>5.1f}%) {gp_count:>4}({gp_pct:>5.1f}%)")
        
        # 访问频率对比
        apk_freq = [user['access_frequency'] for user in apk_users]
        gp_freq = [user['access_frequency'] for user in gp_users]
        
        print(f"\n📊 访问频率对比:")
        print(f"APK用户平均访问次数: {sum(apk_freq)/len(apk_freq):.2f}" if apk_freq else "APK用户: 无数据")
        print(f"Google Play用户平均访问次数: {sum(gp_freq)/len(gp_freq):.2f}" if gp_freq else "Google Play用户: 无数据")
    
    def _compare_android_sources(self, apk_users, gp_users):
        """
        对比APK和Google Play Android用户的特征
        """
        print(f"\n📈 APK vs Google Play Android用户详细对比:")
        print("-" * 60)
        
        # 登录平台分布对比
        apk_platforms = Counter([user['login_type'] for user in apk_users])
        gp_platforms = Counter([user['login_type'] for user in gp_users])
        
        print("📱 登录平台分布对比:")
        print(f"{'登录平台':<15} {'APK用户':<15} {'Google Play用户':<15}")
        print("-" * 50)
        
        all_platforms = set(list(apk_platforms.keys()) + list(gp_platforms.keys()))
        for platform in all_platforms:
            platform_name = self.platform_mapping.get(platform, platform)
            apk_count = apk_platforms.get(platform, 0)
            gp_count = gp_platforms.get(platform, 0)
            apk_pct = (apk_count / len(apk_users)) * 100 if apk_users else 0
            gp_pct = (gp_count / len(gp_users)) * 100 if gp_users else 0
            print(f"{platform_name:<15} {apk_count:>4}({apk_pct:>5.1f}%) {gp_count:>7}({gp_pct:>5.1f}%)")
        
        # 国家分布对比
        apk_countries = Counter([user['country_code'] for user in apk_users if user['country_code']])
        gp_countries = Counter([user['country_code'] for user in gp_users if user['country_code']])
        
        print(f"\n🌍 国家分布对比 (Top 10):")
        print(f"{'国家':<10} {'APK用户':<15} {'Google Play用户':<15}")
        print("-" * 45)
        
        all_countries = set(list(apk_countries.keys()) + list(gp_countries.keys()))
        top_countries = sorted(all_countries, 
                             key=lambda x: apk_countries.get(x, 0) + gp_countries.get(x, 0), 
                             reverse=True)[:10]
        
        for country in top_countries:
            apk_count = apk_countries.get(country, 0)
            gp_count = gp_countries.get(country, 0)
            apk_pct = (apk_count / len(apk_users)) * 100 if apk_users else 0
            gp_pct = (gp_count / len(gp_users)) * 100 if gp_users else 0
            print(f"{country:<10} {apk_count:>4}({apk_pct:>5.1f}%) {gp_count:>7}({gp_pct:>5.1f}%)")
        
        # 访问频率对比
        apk_freq = [user['access_frequency'] for user in apk_users]
        gp_freq = [user['access_frequency'] for user in gp_users]
        
        print(f"\n📊 访问活跃度对比:")
        if apk_freq:
            apk_avg = sum(apk_freq) / len(apk_freq)
            print(f"APK用户平均访问次数: {apk_avg:.2f}")
        else:
            print("APK用户: 无数据")
            
        if gp_freq:
            gp_avg = sum(gp_freq) / len(gp_freq)
            print(f"Google Play用户平均访问次数: {gp_avg:.2f}")
        else:
            print("Google Play用户: 无数据")
        
        # 用户总数对比
        print(f"\n📈 样本统计:")
        print(f"APK用户样本数: {len(apk_users)}")
        print(f"Google Play用户样本数: {len(gp_users)}")
        
        if apk_freq and gp_freq:
            print(f"\n🎯 关键发现:")
            if apk_avg > gp_avg:
                print(f"• APK用户的平均访问频率比Google Play用户高 {((apk_avg - gp_avg) / gp_avg * 100):.1f}%")
            else:
                print(f"• Google Play用户的平均访问频率比APK用户高 {((gp_avg - apk_avg) / apk_avg * 100):.1f}%")
    
    def export_analysis_report(self, output_file="user_login_analysis_report.json"):
        """
        导出完整的分析报告
        """
        try:
            print(f"\n📋 生成完整分析报告...")
            
            report = {
                'analysis_time': datetime.now().isoformat(),
                'source_statistics': self.get_source_type_statistics(),
                'platform_statistics': self.get_platform_statistics(),
                'sample_analysis': self.analyze_apk_vs_googleplay()
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 分析报告已保存到: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出报告失败: {e}")
            return False

def main():
    """
    主函数 - 示例用法
    """
    # 数据库连接配置 - 请根据实际情况修改
    DB_CONFIG = {
        'host': 'localhost',
        'database': 'your_database',
        'user': 'your_username',
        'password': 'your_password',
        'port': 3306
    }
    
    print("🚀 用户登录日志分析工具")
    print("=" * 50)
    
    # 创建分析器实例
    analyzer = UserLoginAnalyzer(**DB_CONFIG)
    
    # 连接数据库
    if not analyzer.connect_db():
        print("❌ 无法连接数据库，程序退出")
        return
    
    try:
        # 1. 统计来源占比
        print("\n📊 1. 来源类型统计分析")
        source_stats = analyzer.get_source_type_statistics(days=3)
        
        # 2. 统计平台占比
        print("\n📱 2. 登录平台统计分析")
        platform_stats = analyzer.get_platform_statistics(days=3)
        
        # 3. APK vs Google Play 对比分析 (基于登录平台)
        print("\n🔍 3. APK vs Google Play 对比分析 (基于登录平台)")
        comparison_data = analyzer.analyze_apk_vs_googleplay(sample_size=1000, days=3)
        
        # 4. Android APK vs Google Play 对比分析 (基于source_type)
        print("\n📱 4. Android APK vs Google Play 对比分析 (基于来源类型)")
        android_comparison = analyzer.analyze_android_apk_vs_gp(sample_size=1000, days=3)
        
        # 5. 导出完整报告
        analyzer.export_analysis_report()
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
    
    finally:
        # 关闭数据库连接
        analyzer.close_connection()

if __name__ == "__main__":
    main() 