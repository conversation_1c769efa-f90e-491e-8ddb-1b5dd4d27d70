#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图文混排统计测试脚本
使用示例数据验证统计逻辑
"""

import json
from datetime import datetime
from 图文混排统计 import MixedContentAnalyzer

# 示例数据
sample_post = {
    'id': 2000134490842914400,
    'user_id': 13107915236375,
    'content_type': 2,
    'post_type': 1,
    'content': "what 's the man",
    'segments_data': '[{"type":"text","id":0,"value":"what \'s the man"},{"type":"topic","id":38,"value":"今日穿搭"}]',
    'lang': 'zh_cn',
    'media_data': '[{"type":1,"media":"https://ugc-content.x.me/images/prod/695000117538750464/7965ede26aa2cd77d44472318868c2c1.jpg","url":"","width":1080,"height":2376,"thumb":"","duration":0,"sort":1},{"type":1,"media":"https://ugc-content.x.me/images/prod/695000117542944768/07b90d84b8186feb2f0c939c02685828.jpg","url":"","width":1080,"height":2376,"thumb":"","duration":0,"sort":2}]',
    'original_post_id': 0,
    'parent_post_id': 0,
    'root_user_id': 0,
    'visibility': 0,
    'status': 0,
    'control_status': 0,
    'is_recommended': 0,
    'recommend_at': datetime(2025, 4, 1, 12, 2, 23),
    'recommend_level': 0,
    'is_pinned': 0,
    'is_del': 0,
    'ip_address': b'**************',
    'source': 1,
    'client_info': '',
    'metadata': '06a78434e180f9c139f28bf9529954eb',
    'created_at': datetime(2025, 4, 1, 12, 2, 23, 193000),
    'lase_edit_at': datetime(2025, 4, 1, 12, 2, 23),
    'published_at': None,
    'updated_at': datetime(2025, 4, 1, 12, 2, 23, 193000)
}

def test_content_analysis():
    """测试内容分析逻辑"""
    print("="*60)
    print("图文混排统计测试")
    print("="*60)
    
    # 创建分析器实例（不需要数据库连接）
    analyzer = MixedContentAnalyzer({})
    
    # 分析示例帖子
    analysis = analyzer.analyze_content_composition(sample_post)
    
    print(f"\n📄 原始数据:")
    print(f"content: {sample_post['content']}")
    print(f"content_type: {sample_post['content_type']}")
    print(f"segments_data: {sample_post['segments_data']}")
    print(f"media_data: {sample_post['media_data']}")
    
    print(f"\n🔍 解析结果:")
    
    # 解析media_data
    media_data = analyzer.parse_media_data(sample_post['media_data'])
    print(f"解析后的media_data: {json.dumps(media_data, indent=2, ensure_ascii=False)}")
    
    # 解析segments_data
    segments_data = analyzer.parse_segments_data(sample_post['segments_data'])
    print(f"解析后的segments_data: {json.dumps(segments_data, indent=2, ensure_ascii=False)}")
    
    print(f"\n📊 分析统计:")
    print(f"媒体类型: {analysis['media_types']}")
    print(f"媒体数量: {analysis['media_count']}")
    print(f"文本信息: {analysis['text_info']}")
    print(f"混排类型: {analysis['mixed_type']}")
    print(f"是否混排内容: {analysis['is_mixed_content']}")
    
    print(f"\n✅ 详细分析:")
    print(f"  - 包含 {analysis['media_count']['images']} 张图片")
    print(f"  - 包含 {analysis['media_count']['videos']} 个视频")
    print(f"  - 文字长度: {analysis['text_info']['text_length']} 字符")
    print(f"  - 文本片段数: {analysis['text_info']['text_segments']}")
    print(f"  - 话题片段数: {analysis['text_info']['topic_segments']}")
    print(f"  - 其他片段数: {analysis['text_info']['other_segments']}")
    
    return analysis

def test_multiple_scenarios():
    """测试多种场景"""
    print(f"\n" + "="*60)
    print("多场景测试")
    print("="*60)
    
    analyzer = MixedContentAnalyzer({})
    
    # 测试用例
    test_cases = [
        {
            'name': '纯文本',
            'post': {
                'content': '这是一段纯文本内容',
                'segments_data': '[{"type":"text","id":0,"value":"这是一段纯文本内容"}]',
                'media_data': '[]'
            }
        },
        {
            'name': '纯图片',
            'post': {
                'content': '',
                'segments_data': '[]',
                'media_data': '[{"type":1,"media":"image1.jpg","sort":1}]'
            }
        },
        {
            'name': '图片+文字',
            'post': {
                'content': '看看我的新照片',
                'segments_data': '[{"type":"text","id":0,"value":"看看我的新照片"}]',
                'media_data': '[{"type":1,"media":"image1.jpg","sort":1}]'
            }
        },
        {
            'name': '视频+文字',
            'post': {
                'content': '今天的视频分享',
                'segments_data': '[{"type":"text","id":0,"value":"今天的视频分享"}]',
                'media_data': '[{"type":2,"media":"video1.mp4","sort":1}]'
            }
        },
        {
            'name': '图片+视频+文字',
            'post': {
                'content': '多媒体内容展示',
                'segments_data': '[{"type":"text","id":0,"value":"多媒体内容展示"},{"type":"topic","id":1,"value":"测试"}]',
                'media_data': '[{"type":1,"media":"image1.jpg","sort":1},{"type":2,"media":"video1.mp4","sort":2}]'
            }
        },
        {
            'name': '多图片+文字+话题',
            'post': {
                'content': '今天的穿搭分享',
                'segments_data': '[{"type":"text","id":0,"value":"今天的穿搭分享"},{"type":"topic","id":1,"value":"穿搭"},{"type":"topic","id":2,"value":"时尚"}]',
                'media_data': '[{"type":1,"media":"image1.jpg","sort":1},{"type":1,"media":"image2.jpg","sort":2},{"type":1,"media":"image3.jpg","sort":3}]'
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}: {test_case['name']}")
        print("-" * 40)
        
        # 模拟完整的post数据
        post_data = {
            'content': test_case['post']['content'],
            'segments_data': test_case['post']['segments_data'],
            'media_data': test_case['post']['media_data']
        }
        
        analysis = analyzer.analyze_content_composition(post_data)
        
        print(f"内容: '{post_data['content']}'")
        print(f"媒体: {analysis['media_count']['images']}图片, {analysis['media_count']['videos']}视频")
        print(f"文字: {analysis['text_info']['text_length']}字符")
        print(f"片段: {analysis['text_info']['text_segments']}文本, {analysis['text_info']['topic_segments']}话题")
        print(f"判定: {analysis['mixed_type']}")
        print(f"混排: {'是' if analysis['is_mixed_content'] else '否'}")

def test_edge_cases():
    """测试边界情况"""
    print(f"\n" + "="*60)
    print("边界情况测试")
    print("="*60)
    
    analyzer = MixedContentAnalyzer({})
    
    edge_cases = [
        {
            'name': '空内容',
            'post': {'content': '', 'segments_data': '', 'media_data': ''}
        },
        {
            'name': 'JSON格式错误',
            'post': {'content': '测试', 'segments_data': '{invalid json}', 'media_data': '[invalid}'}
        },
        {
            'name': '只有空格的文字',
            'post': {'content': '   ', 'segments_data': '[]', 'media_data': '[]'}
        },
        {
            'name': 'None值',
            'post': {'content': None, 'segments_data': None, 'media_data': None}
        },
        {
            'name': '非标准媒体类型',
            'post': {
                'content': '测试',
                'segments_data': '[]',
                'media_data': '[{"type":99,"media":"unknown.file","sort":1}]'
            }
        }
    ]
    
    for i, test_case in enumerate(edge_cases, 1):
        print(f"\n🔬 边界测试 {i}: {test_case['name']}")
        print("-" * 30)
        
        try:
            analysis = analyzer.analyze_content_composition(test_case['post'])
            print(f"结果: {analysis['mixed_type']}")
            print(f"混排: {'是' if analysis['is_mixed_content'] else '否'}")
        except Exception as e:
            print(f"错误: {e}")

def main():
    """主测试函数"""
    # 测试示例数据
    analysis = test_content_analysis()
    
    # 测试多种场景
    test_multiple_scenarios()
    
    # 测试边界情况
    test_edge_cases()
    
    print(f"\n" + "="*60)
    print("测试完成")
    print("="*60)
    
    print(f"\n💡 统计脚本使用说明:")
    print(f"1. 修改 DB_CONFIG 中的数据库连接信息")
    print(f"2. 运行: python 图文混排统计.py")
    print(f"3. 查看统计报告输出")
    
    print(f"\n📋 示例数据分析结果:")
    print(f"您提供的示例数据被判定为: {analysis['mixed_type']}")
    print(f"原因: 包含{analysis['media_count']['images']}张图片 + {analysis['text_info']['text_length']}字符文字")

if __name__ == "__main__":
    main() 