import pymysql
import logging
from datetime import datetime
import redis

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("system_user_task_init.log"),
        logging.StreamHandler()
    ]
)

# Database server configurations
# 媒体用户数据库配置
USER_DB = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_user'
}

# 任务数据库配置
TASK_DB = {
    'host': 'xme-envtest-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_task'
}

# UGC内容数据库配置
UGC_DB = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'ugc_hub'
}

# 用户行为数据库配置
BEHAVIOR_DB = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'content_behavior'
}

def get_db_config(**kwargs):
    """
    Helper function to prepare database configuration.
    Returns the database configuration dictionary with cursor settings.
    
    Args:
        **kwargs: Database connection parameters (host, user, password, database)
        
    Returns:
        dict: Database configuration with cursor settings
    """
    # Create a copy of the input config
    config = dict(kwargs)
    
    # Add cursor class configuration for dictionary results
    config['cursorclass'] = pymysql.cursors.DictCursor
    
    # Add other common configuration parameters if needed
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    
    return config

def award_energy_points(cursor, user_id, task_code, energy_points, source_type, task_description):
    """
    Common function to award energy points to a user:
    - Checks if the task has already been awarded
    - Creates a task record in user_task_record
    - Creates an energy record in ep_records
    - Updates the user_base_energy table if it exists
    
    Args:
        cursor: Database cursor for media_task database
        user_id: User ID to award points to
        task_code: Task code identifier
        energy_points: Number of energy points to award
        source_type: Source type for the energy record
        task_description: Description for logging
        
    Returns:
        bool: True if points were awarded, False if user already had the task
        int: Task record ID if created, None otherwise
    """
    # Calculate the shard number based on user_id % 1024
    shard_num = user_id % 1024
    
    # Define sharded table names
    user_task_table = f"user_task_record_{shard_num}"
    ep_records_table = f"user_ep_records_{shard_num}"

    try:
        # Start transaction
        cursor.execute("START TRANSACTION")
        
        # Check if task record already exists for this user
        # Using SELECT FOR UPDATE to lock the row and prevent race conditions
        check_query = f"""
        SELECT id FROM {user_task_table} 
        WHERE user_id = %s AND task_code = %s AND delete_status = 0
        FOR UPDATE
        """
        cursor.execute(check_query, (user_id, task_code))
        existing_record = cursor.fetchone()
        
        if existing_record:
            logging.info(f"User {user_id} already has {task_description} bonus. Skipping.")
            cursor.execute("COMMIT")
            return False, None
        
        # Insert task record
        now = datetime.now()
        task_insert_query = f"""
        INSERT INTO {user_task_table} 
        (user_id, task_code, task_type, complete_status,has_complete_count) 
        VALUES (%s, %s, 1, %s,1)
        """
        cursor.execute(task_insert_query, (user_id, task_code, 1))
        task_record_id = cursor.lastrowid
        
        # Get the beginning of the current hour (HH:00:00)
        hourly_time = now.replace(minute=0, second=0, microsecond=0)
        
        # Insert energy record
        energy_insert_query = f"""
        INSERT INTO {ep_records_table} 
        (user_id, ep_type, ep_amount, ep_source_type, task_record_id, hourly) 
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        cursor.execute(
            energy_insert_query, 
            (user_id, 1, energy_points, source_type, task_record_id, hourly_time)
        )
        
        # Update user_base_energy table
        base_energy_table = "user_base_ep"
        
        base_energy_query = f"""
        INSERT INTO {base_energy_table} (user_id, total_base_ep) 
        VALUES (%s, %s) 
        ON DUPLICATE KEY UPDATE total_base_ep = total_base_ep + %s
        """
        cursor.execute(base_energy_query, (user_id, energy_points, energy_points))
        
        # Commit transaction
        cursor.execute("COMMIT")
        
        logging.info(f"Successfully processed user {user_id}: Added {energy_points} base energy points for {task_description}")
        return True, task_record_id
    
    except Exception as e:
        # Rollback transaction in case of error
        try:
            cursor.execute("ROLLBACK")
        except:
            pass  # Ignore errors during rollback
        logging.error(f"Error processing user {user_id} for {task_description}: {str(e)}")
        raise

def initialize_system_user_tasks():
    """
    Initialize tasks for system users based on system_user_record table.
    - Fetches all users from media_task.system_user_record
    - Assumes all basic tasks are completed
    - Awards energy points for all basic tasks
    - Inserts records into media_task.user_task_record and media_task.ep_records
    - Updates user_base_energy table
    """
    # Connect to media_task database
    task_db_config = get_db_config(**TASK_DB)
    
    # Task configurations
    tasks = [
        {'code': '10001', 'description': 'interest selection', 'energy': 100},
        {'code': '10002', 'description': 'first UGC post', 'energy': 150},
        {'code': '10003', 'description': 'face invitation', 'energy': 200},
        {'code': '10005', 'description': 'phone verification', 'energy': 200},
        {'code': '10006', 'description': 'email verification', 'energy': 200}
    ]
    
    try:
        # Step 1: Fetch system users from media_task database
        task_conn = pymysql.connect(**task_db_config)
        system_users = []
        
        try:
            with task_conn.cursor() as cursor:
                # Get all non-deleted system users
                query = """
                SELECT user_id, nick_name 
                FROM system_user_record 
                WHERE delete_status = 0
                """
                cursor.execute(query)
                system_users = cursor.fetchall()
                logging.info(f"Found {len(system_users)} system users")
        except Exception as e:
            logging.error(f"Error fetching system users: {str(e)}")
            task_conn.close()
            return
        
        if not system_users:
            logging.info("No system users found. Exiting.")
            task_conn.close()
            return
        
        # Step 2: Process system users and update task records
        
        try:
            with task_conn.cursor() as cursor:
                for user in system_users:
                    user_id = user['user_id']
                    nick_name = user['nick_name']
                    
                    logging.info(f"Processing system user {user_id} ({nick_name})")
                    
                    # Process each task for the user
                    for task in tasks:
                        task_code = task['code']
                        energy_points = task['energy']
                        task_description = task['description']
                        
                        try:
                            # Award energy points using the common function
                            success, task_record_id = award_energy_points(
                                cursor, 
                                user_id, 
                                task_code, 
                                energy_points, 
                                '1',  # Source type for system tasks
                                task_description
                            )
                            
                            if success:
                                task_conn.commit()
                                logging.info(f"Awarded {energy_points} energy points to user {user_id} for {task_description}")
                            
                        except Exception as e:
                            task_conn.rollback()
                            logging.error(f"Error processing task {task_code} for user {user_id}: {str(e)}")
                            
        finally:
            task_conn.close()
            
        logging.info("System user task initialization completed")
        
    except Exception as e:
        logging.error(f"Error in initialize_system_user_tasks: {str(e)}")

def main():
    """
    Main function to run system user task initialization
    """
    logging.info("Starting system user task initialization")
    
    # Initialize tasks for system users
    initialize_system_user_tasks()
    
    logging.info("System user task initialization completed")

if __name__ == "__main__":
    main()
