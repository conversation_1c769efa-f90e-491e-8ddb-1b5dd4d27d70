-- 修改字段定义和更新数据 - push_message表 (768-1023)
-- 生成时间: 2025-06-24 11:21:20.958815

-- 修改 created_time 字段默认值 (768-1023)
ALTER TABLE media_message.push_message768 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message769 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message770 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message771 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message772 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message773 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message774 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message775 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message776 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message777 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message778 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message779 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message780 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message781 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message782 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message783 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message784 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message785 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message786 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message787 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message788 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message789 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message790 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message791 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message792 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message793 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message794 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message795 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message796 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message797 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message798 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message799 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message800 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message801 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message802 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message803 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message804 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message805 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message806 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message807 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message808 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message809 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message810 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message811 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message812 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message813 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message814 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message815 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message816 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message817 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message818 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message819 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message820 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message821 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message822 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message823 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message824 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message825 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message826 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message827 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message828 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message829 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message830 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message831 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message832 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message833 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message834 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message835 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message836 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message837 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message838 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message839 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message840 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message841 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message842 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message843 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message844 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message845 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message846 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message847 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message848 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message849 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message850 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message851 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message852 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message853 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message854 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message855 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message856 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message857 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message858 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message859 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message860 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message861 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message862 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message863 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message864 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message865 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message866 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message867 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message868 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message869 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message870 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message871 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message872 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message873 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message874 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message875 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message876 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message877 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message878 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message879 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message880 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message881 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message882 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message883 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message884 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message885 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message886 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message887 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message888 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message889 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message890 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message891 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message892 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message893 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message894 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message895 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message896 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message897 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message898 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message899 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message900 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message901 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message902 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message903 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message904 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message905 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message906 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message907 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message908 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message909 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message910 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message911 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message912 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message913 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message914 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message915 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message916 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message917 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message918 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message919 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message920 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message921 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message922 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message923 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message924 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message925 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message926 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message927 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message928 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message929 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message930 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message931 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message932 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message933 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message934 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message935 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message936 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message937 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message938 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message939 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message940 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message941 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message942 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message943 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message944 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message945 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message946 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message947 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message948 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message949 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message950 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message951 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message952 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message953 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message954 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message955 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message956 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message957 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message958 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message959 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message960 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message961 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message962 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message963 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message964 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message965 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message966 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message967 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message968 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message969 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message970 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message971 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message972 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message973 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message974 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message975 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message976 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message977 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message978 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message979 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message980 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message981 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message982 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message983 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message984 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message985 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message986 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message987 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message988 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message989 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message990 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message991 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message992 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message993 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message994 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message995 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message996 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message997 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message998 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message999 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1000 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1001 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1002 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1003 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1004 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1005 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1006 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1007 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1008 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1009 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1010 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1011 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1012 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1013 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1014 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1015 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1016 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1017 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1018 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1019 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1020 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1021 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1022 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1023 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 修改 updated_time 字段默认值 (768-1023)
ALTER TABLE media_message.push_message768 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message769 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message770 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message771 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message772 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message773 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message774 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message775 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message776 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message777 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message778 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message779 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message780 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message781 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message782 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message783 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message784 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message785 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message786 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message787 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message788 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message789 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message790 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message791 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message792 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message793 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message794 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message795 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message796 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message797 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message798 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message799 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message800 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message801 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message802 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message803 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message804 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message805 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message806 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message807 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message808 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message809 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message810 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message811 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message812 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message813 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message814 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message815 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message816 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message817 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message818 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message819 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message820 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message821 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message822 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message823 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message824 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message825 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message826 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message827 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message828 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message829 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message830 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message831 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message832 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message833 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message834 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message835 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message836 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message837 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message838 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message839 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message840 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message841 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message842 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message843 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message844 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message845 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message846 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message847 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message848 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message849 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message850 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message851 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message852 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message853 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message854 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message855 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message856 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message857 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message858 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message859 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message860 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message861 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message862 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message863 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message864 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message865 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message866 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message867 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message868 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message869 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message870 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message871 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message872 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message873 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message874 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message875 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message876 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message877 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message878 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message879 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message880 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message881 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message882 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message883 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message884 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message885 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message886 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message887 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message888 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message889 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message890 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message891 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message892 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message893 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message894 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message895 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message896 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message897 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message898 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message899 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message900 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message901 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message902 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message903 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message904 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message905 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message906 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message907 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message908 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message909 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message910 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message911 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message912 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message913 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message914 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message915 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message916 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message917 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message918 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message919 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message920 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message921 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message922 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message923 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message924 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message925 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message926 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message927 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message928 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message929 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message930 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message931 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message932 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message933 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message934 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message935 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message936 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message937 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message938 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message939 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message940 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message941 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message942 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message943 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message944 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message945 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message946 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message947 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message948 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message949 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message950 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message951 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message952 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message953 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message954 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message955 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message956 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message957 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message958 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message959 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message960 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message961 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message962 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message963 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message964 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message965 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message966 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message967 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message968 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message969 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message970 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message971 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message972 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message973 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message974 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message975 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message976 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message977 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message978 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message979 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message980 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message981 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message982 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message983 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message984 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message985 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message986 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message987 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message988 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message989 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message990 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message991 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message992 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message993 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message994 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message995 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message996 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message997 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message998 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message999 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1000 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1001 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1002 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1003 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1004 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1005 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1006 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1007 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1008 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1009 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1010 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1011 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1012 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1013 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1014 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1015 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1016 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1017 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1018 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1019 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1020 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1021 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1022 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1023 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 更新空值数据 (768-1023)
UPDATE media_message.push_message768 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message769 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message770 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message771 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message772 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message773 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message774 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message775 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message776 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message777 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message778 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message779 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message780 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message781 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message782 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message783 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message784 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message785 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message786 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message787 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message788 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message789 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message790 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message791 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message792 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message793 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message794 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message795 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message796 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message797 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message798 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message799 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message800 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message801 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message802 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message803 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message804 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message805 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message806 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message807 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message808 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message809 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message810 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message811 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message812 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message813 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message814 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message815 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message816 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message817 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message818 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message819 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message820 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message821 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message822 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message823 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message824 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message825 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message826 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message827 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message828 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message829 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message830 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message831 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message832 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message833 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message834 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message835 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message836 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message837 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message838 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message839 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message840 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message841 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message842 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message843 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message844 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message845 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message846 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message847 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message848 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message849 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message850 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message851 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message852 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message853 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message854 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message855 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message856 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message857 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message858 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message859 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message860 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message861 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message862 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message863 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message864 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message865 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message866 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message867 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message868 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message869 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message870 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message871 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message872 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message873 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message874 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message875 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message876 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message877 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message878 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message879 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message880 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message881 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message882 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message883 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message884 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message885 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message886 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message887 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message888 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message889 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message890 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message891 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message892 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message893 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message894 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message895 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message896 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message897 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message898 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message899 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message900 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message901 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message902 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message903 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message904 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message905 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message906 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message907 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message908 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message909 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message910 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message911 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message912 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message913 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message914 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message915 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message916 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message917 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message918 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message919 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message920 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message921 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message922 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message923 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message924 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message925 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message926 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message927 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message928 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message929 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message930 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message931 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message932 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message933 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message934 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message935 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message936 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message937 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message938 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message939 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message940 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message941 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message942 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message943 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message944 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message945 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message946 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message947 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message948 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message949 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message950 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message951 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message952 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message953 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message954 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message955 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message956 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message957 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message958 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message959 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message960 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message961 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message962 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message963 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message964 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message965 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message966 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message967 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message968 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message969 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message970 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message971 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message972 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message973 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message974 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message975 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message976 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message977 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message978 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message979 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message980 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message981 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message982 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message983 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message984 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message985 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message986 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message987 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message988 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message989 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message990 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message991 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message992 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message993 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message994 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message995 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message996 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message997 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message998 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message999 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1000 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1001 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1002 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1003 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1004 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1005 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1006 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1007 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1008 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1009 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1010 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1011 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1012 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1013 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1014 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1015 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1016 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1017 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1018 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1019 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1020 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1021 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1022 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1023 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;