#!/usr/bin/env python3
import pymysql
import logging
from datetime import datetime, timezone, timedelta
from collections import defaultdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("fix_hourly_ep_data.log"),
        logging.StreamHandler()
    ]
)

# 数据库配置
DB_CONFIG = {
    'host': 'xme-prod-media-task-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': False
}

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        logging.info("成功连接到数据库")
        return conn
    except Exception as e:
        logging.error(f"连接数据库失败: {str(e)}")
        return None

def get_affected_users(conn):
    """获取主表中2025-06-30的所有用户ID"""
    try:
        with conn.cursor() as cursor:
            query = """
            SELECT DISTINCT user_id, expedition_id
            FROM user_hourly_ep
            WHERE DATE(hourly) = '2025-06-30'
                AND delete_status = 0
            ORDER BY user_id
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            users = [(row['user_id'], row['expedition_id']) for row in results]
            logging.info(f"找到 {len(users)} 个受影响的用户")
            
            return users
            
    except Exception as e:
        logging.error(f"获取受影响用户时出错: {str(e)}")
        return []

def get_user_ep_record_tables(conn):
    """获取所有user_ep_record分表"""
    try:
        with conn.cursor() as cursor:
            cursor.execute("SHOW TABLES LIKE 'user_ep_record%'")
            tables = [row[f'Tables_in_{DB_CONFIG["database"]} (user_ep_record%)'] for row in cursor.fetchall()]
            
            logging.info(f"找到 {len(tables)} 个user_ep_record分表")
            return sorted(tables)
            
    except Exception as e:
        logging.error(f"获取user_ep_record分表时出错: {str(e)}")
        return []

def calculate_hourly_ep_from_records(conn, user_id, expedition_id, tables):
    """从user_ep_record分表计算用户2025-06-30的小时EP数据"""
    hourly_data = defaultdict(lambda: {'hour_ep': 0, 'bonus_ep': 0})
    
    for table in tables:
        try:
            with conn.cursor() as cursor:
                # 检查表是否存在该用户的数据
                query = f"""
                SELECT 
                    DATE_FORMAT(created_time, '%Y-%m-%d %H:00:00') as hourly,
                    SUM(CASE WHEN ep_type = 1 THEN ep_amount ELSE 0 END) as hour_ep,
                    SUM(CASE WHEN ep_type = 2 THEN ep_amount ELSE 0 END) as bonus_ep
                FROM {table}
                WHERE user_id = %s 
                    AND expedition_id = %s
                    AND DATE(created_time) = '2025-06-30'
                    AND delete_status = 0
                GROUP BY DATE_FORMAT(created_time, '%Y-%m-%d %H:00:00')
                """
                
                cursor.execute(query, (user_id, expedition_id))
                results = cursor.fetchall()
                
                for row in results:
                    hourly = row['hourly']
                    hourly_data[hourly]['hour_ep'] += row['hour_ep'] or 0
                    hourly_data[hourly]['bonus_ep'] += row['bonus_ep'] or 0
                    
        except Exception as e:
            logging.warning(f"查询表 {table} 用户 {user_id} 数据时出错: {str(e)}")
            continue
    
    return dict(hourly_data)

def generate_sql_statements(user_id, expedition_id, hourly_data):
    """生成SQL语句而不是直接执行"""
    sql_statements = []
    
    if not hourly_data:
        return []
    
    for hourly_str, ep_data in hourly_data.items():
        # 生成INSERT ON DUPLICATE KEY UPDATE语句
        insert_sql = f"""INSERT INTO user_hourly_ep_20250630 
(user_id, expedition_id, hourly, hour_ep, bonus_ep, create_time, update_time, delete_status)
VALUES ({user_id}, {expedition_id}, '{hourly_str}', {ep_data['hour_ep']}, {ep_data['bonus_ep']}, NOW(), NOW(), 0)
ON DUPLICATE KEY UPDATE
hour_ep = {ep_data['hour_ep']},
bonus_ep = {ep_data['bonus_ep']},
update_time = NOW();"""
        
        sql_statements.append(insert_sql)
    
    return sql_statements

def generate_delete_sql(user_id):
    """生成删除主表数据的SQL语句"""
    delete_sql = f"""-- 标记删除主表中用户 {user_id} 的2025-06-30数据
UPDATE user_hourly_ep
SET delete_status = 1, delete_time = NOW()
WHERE user_id = {user_id} AND DATE(hourly) = '2025-06-30';"""
    
    return delete_sql

def save_sql_files(all_sql_statements, delete_sql_statements):
    """保存SQL语句到文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存插入/更新SQL
    insert_filename = f"fix_hourly_ep_insert_{timestamp}.sql"
    with open(insert_filename, 'w', encoding='utf-8') as f:
        f.write("-- 修复用户小时能量点数据 - 插入/更新SQL\n")
        f.write(f"-- 生成时间: {datetime.now().isoformat()}\n")
        f.write("-- 说明: 将user_ep_record重新计算的数据更新到user_hourly_ep_20250630分表\n\n")
        f.write("START TRANSACTION;\n\n")
        
        for sql in all_sql_statements:
            f.write(sql + "\n\n")
        
        f.write("-- COMMIT;\n")
        f.write("-- 请检查上述SQL语句后，取消注释COMMIT行并执行\n")
    
    # 保存删除SQL
    delete_filename = f"fix_hourly_ep_delete_{timestamp}.sql"
    with open(delete_filename, 'w', encoding='utf-8') as f:
        f.write("-- 修复用户小时能量点数据 - 删除主表错误数据SQL\n")
        f.write(f"-- 生成时间: {datetime.now().isoformat()}\n")
        f.write("-- 说明: 标记删除user_hourly_ep主表中2025-06-30的错误数据\n")
        f.write("-- 警告: 请在确认分表数据正确后再执行此删除操作\n\n")
        f.write("START TRANSACTION;\n\n")
        
        for sql in delete_sql_statements:
            f.write(sql + "\n\n")
        
        f.write("-- COMMIT;\n")
        f.write("-- 请检查上述SQL语句后，取消注释COMMIT行并执行\n")
    
    logging.info(f"SQL语句已保存到:")
    logging.info(f"  插入/更新SQL: {insert_filename}")
    logging.info(f"  删除SQL: {delete_filename}")
    
    return insert_filename, delete_filename

def save_data_report(user_data_map):
    """保存数据分析报告"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_filename = f"hourly_ep_data_analysis_{timestamp}.txt"
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("=== 用户小时能量点数据分析报告 ===\n")
        f.write(f"生成时间: {datetime.now().isoformat()}\n")
        f.write(f"分析日期: 2025-06-30\n\n")
        
        f.write("=== 汇总统计 ===\n")
        f.write(f"受影响用户总数: {len(user_data_map)}\n")
        
        total_hours = sum(len(data) for data in user_data_map.values())
        total_hour_ep = sum(sum(hour_data['hour_ep'] for hour_data in data.values()) for data in user_data_map.values())
        total_bonus_ep = sum(sum(hour_data['bonus_ep'] for hour_data in data.values()) for data in user_data_map.values())
        
        f.write(f"总小时记录数: {total_hours}\n")
        f.write(f"总hour_ep: {total_hour_ep}\n")
        f.write(f"总bonus_ep: {total_bonus_ep}\n\n")
        
        f.write("=== 用户详细数据 ===\n")
        f.write(f"{'用户ID':<12} {'探险ID':<12} {'小时数':<8} {'总hour_ep':<12} {'总bonus_ep':<12}\n")
        f.write("-" * 70 + "\n")
        
        for (user_id, expedition_id), hourly_data in user_data_map.items():
            user_hour_ep = sum(data['hour_ep'] for data in hourly_data.values())
            user_bonus_ep = sum(data['bonus_ep'] for data in hourly_data.values())
            
            f.write(f"{user_id:<12} {expedition_id:<12} {len(hourly_data):<8} {user_hour_ep:<12} {user_bonus_ep:<12}\n")
    
    logging.info(f"数据分析报告已保存到: {report_filename}")
    return report_filename

def main():
    """主函数"""
    logging.info("开始分析用户小时能量点数据并生成修复SQL...")
    start_time = datetime.now(timezone.utc)
    
    # 连接数据库
    conn = get_db_connection()
    if not conn:
        logging.error("无法连接数据库，退出程序")
        return
    
    try:
        # 1. 获取受影响的用户
        logging.info("步骤1: 获取受影响的用户...")
        affected_users = get_affected_users(conn)
        if not affected_users:
            logging.info("没有找到需要修复的用户")
            return
        
        # 2. 获取user_ep_record分表
        logging.info("步骤2: 获取user_ep_record分表...")
        ep_record_tables = get_user_ep_record_tables(conn)
        if not ep_record_tables:
            logging.error("没有找到user_ep_record分表")
            return
        
        # 3. 分析每个用户并生成SQL
        logging.info(f"步骤3: 开始分析 {len(affected_users)} 个用户...")
        
        all_sql_statements = []
        delete_sql_statements = []
        user_data_map = {}
        
        for i, (user_id, expedition_id) in enumerate(affected_users):
            logging.info(f"分析用户 {user_id} (expedition_id: {expedition_id}) - {i+1}/{len(affected_users)}")
            
            # 3.1 从user_ep_record重新计算小时EP数据
            hourly_data = calculate_hourly_ep_from_records(conn, user_id, expedition_id, ep_record_tables)
            
            if hourly_data:
                logging.info(f"用户 {user_id} 计算出 {len(hourly_data)} 个小时的EP数据")
                
                # 保存用户数据用于报告
                user_data_map[(user_id, expedition_id)] = hourly_data
                
                # 3.2 生成更新分表的SQL语句
                sql_statements = generate_sql_statements(user_id, expedition_id, hourly_data)
                all_sql_statements.extend(sql_statements)
                
                # 3.3 生成删除主表数据的SQL语句
                delete_sql = generate_delete_sql(user_id)
                delete_sql_statements.append(delete_sql)
            else:
                logging.warning(f"用户 {user_id} 没有找到EP记录数据")
            
            # 每处理10个用户输出一次进度
            if (i + 1) % 10 == 0:
                logging.info(f"已分析 {i + 1}/{len(affected_users)} 个用户")
        
        # 4. 保存SQL文件和报告
        logging.info("步骤4: 保存SQL文件和分析报告...")
        insert_file, delete_file = save_sql_files(all_sql_statements, delete_sql_statements)
        report_file = save_data_report(user_data_map)
        
        # 计算执行时间
        end_time = datetime.now(timezone.utc)
        duration = end_time - start_time
        logging.info(f"分析完成，总耗时: {duration}")
        
        # 输出总结
        logging.info("=== 操作总结 ===")
        logging.info(f"分析用户数: {len(affected_users)}")
        logging.info(f"有数据用户数: {len(user_data_map)}")
        logging.info(f"生成SQL语句数: {len(all_sql_statements)}")
        logging.info(f"生成的文件:")
        logging.info(f"  - {insert_file} (插入/更新SQL)")
        logging.info(f"  - {delete_file} (删除SQL)")
        logging.info(f"  - {report_file} (数据分析报告)")
        logging.info("请检查生成的SQL文件后手动执行！")
        
    except KeyboardInterrupt:
        logging.info("用户中断分析任务")
    except Exception as e:
        logging.error(f"分析任务出错: {str(e)}")
    finally:
        conn.close()
        logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 