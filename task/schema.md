3.1.1 用户挖矿状态表（1个用户多条记录）
CREATE TABLE user_expedition_status (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    expedition_id bigint NOT NULL COMMENT '远征ID',
    user_id bigint NOT NULL COMMENT '用户ID',
    device_id varchar(128) NOT NULL COMMENT '设备唯一标识，用于反作弊',
    expedition_start_time timestamp NOT NULL COMMENT '远征周期开始时间',
    expedition_end_time timestamp NOT NULL COMMENT '远征周期结束时间',
    create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    delete_status tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-删除',
    delete_time datetime NOT NULL DEFAULT '1000-01-01 00:00:00' COMMENT '删除时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_expedition_id (expedition_id) COMMENT '远征ID索引',
    INDEX idx_mining_time (expedition_start_time, expedition_end_time) COMMENT '远征周期时间索引',
    INDEX idx_device_id (device_id) COMMENT '设备ID索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户远征状态表';

3.1.2 基础能量点汇总表（1个用户记录）
CREATE TABLE user_base_ep (
    id bigint AUTO_INCREMENT COMMENT '自增ID',
    user_id bigint NOT NULL COMMENT '用户ID',
    total_base_ep INT NOT NULL DEFAULT 0 COMMENT '累积的基础能量点总数',
    create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    delete_status tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-删除',
    delete_time datetime NOT NULL DEFAULT '1000-01-01 00:00:00' COMMENT '删除时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id) COMMENT '用户ID索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '基础能量点汇总表';

3.1.3 用户小时能量表
CREATE TABLE user_hourly_ep (
    id bigint AUTO_INCREMENT COMMENT '自增ID',
    user_id bigint NOT NULL COMMENT '用户ID',
    hourly TIMESTAMP NOT NULL COMMENT '小时时间戳，精确到小时',
    hour_ep INT NOT NULL DEFAULT 0 COMMENT '该小时累积的能量点',
    create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    delete_status tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-删除',
    delete_time datetime NOT NULL DEFAULT '1000-01-01 00:00:00' COMMENT '删除时间',
    PRIMARY KEY (id),
    UNIQUE INDEX idx_user_hour (user_id, hourly) COMMENT '小时时间戳索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '基础能量点汇总表';

3.1.4 能量点记录表
CREATE TABLE ep_records (
    id BIGINT AUTO_INCREMENT COMMENT '记录ID,自增主键',
    user_id BIGINT NOT NULL COMMENT '用户ID,关联用户表',
    ep_type varchar(32) NOT NULL COMMENT '能量点类型:base(基础能量点)、hourly(小时能量点)',
    ep_amount INT NOT NULL COMMENT '能量点数量',
    ep_source_type VARCHAR(32) NOT NULL COMMENT '能量点来源类型,如:task(任务)、login(登录)、social(社交行为)',
    task_record_id BIGINT COMMENT '关联的任务记录ID,如果来源是任务',
    hourly TIMESTAMP NOT NULL COMMENT '小时时间戳，标识该能量点属于哪个小时周期',
    create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    delete_status tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-删除',
    delete_time datetime NOT NULL DEFAULT '1000-01-01 00:00:00' COMMENT '删除时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_hourly (hourly) COMMENT '小时时间戳索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '能量点记录表';

3.1.5 任务定义表
CREATE TABLE task_config (
    id BIGINT AUTO_INCREMENT COMMENT '任务ID,自增主键',
    code VARCHAR(20) NOT NULL COMMENT '任务唯一识别',
    task_type varchar(32) NOT NULL COMMENT '任务类型:base(基础任务)、hourly(小时任务)、growth(成长任务)、invitation(邀请任务)',
    reward_type varchar(32) NOT NULL COMMENT '奖励类型:ep(能量点)、item(物品)、coin(金币)、exp(经验)、other(其他)',
    ep_type varchar(32) NOT NULL COMMENT '奖励能量点类型:base(基础能量点)、hourly(小时能量点)',
    ep_amount BIGINT NOT NULL COMMENT '奖励能量点数量',
    config text COMMENT '任务附加配置,格式json,例如打卡天数、邀请人数,twitter账号等',
    step_count INT DEFAULT 1 COMMENT '',
    weight INT NOT NULL COMMENT '排序权重',
    status TINYINT DEFAULT 1 COMMENT '任务状态:1-激活,0-停用',
    create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    delete_status tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-删除',
    delete_time datetime NOT NULL DEFAULT '1000-01-01 00:00:00' COMMENT '删除时间',
    PRIMARY KEY (id),
    UNIQUE INDEX idx_code (code) COMMENT '任务唯一识别索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务配置表';

3.1.6 用户任务完成表
CREATE TABLE user_task_record (
    id BIGINT AUTO_INCREMENT COMMENT '记录ID,自增主键',
    user_id BIGINT NOT NULL COMMENT '用户ID,关联用户表',
    task_code VARCHAR(20) NOT NULL COMMENT '任务code,关联任务定义表',
    completion_time TIMESTAMP NOT NULL COMMENT '任务完成时间',
    create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    delete_status tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-删除',
    delete_time datetime NOT NULL DEFAULT '1000-01-01 00:00:00' COMMENT '删除时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_task_code (task_code) COMMENT '任务code索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户任务完成记录表';
3.1.7 好友加成
CREATE TABLE friend_ep_bonus (
    id BIGINT AUTO_INCREMENT COMMENT '记录ID,自增主键',
    user_id BIGINT NOT NULL COMMENT '获得加成的用户ID',
    friend_id BIGINT NOT NULL COMMENT '提供加成的好友ID',
    bonus_amount INT NOT NULL COMMENT '加成能量点数',
    bonus_type ENUM('invitation', 'activity') NOT NULL COMMENT '加成类型:invitation(邀请)、activity(活跃)',
    hourly TIMESTAMP NOT NULL COMMENT '小时时间戳，标识该能量点属于哪个小时周期',
    create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    delete_status tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-删除',
    delete_time datetime NOT NULL DEFAULT '1000-01-01 00:00:00' COMMENT '删除时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_friend_id (friend_id) COMMENT '好友ID索引',
    INDEX idx_hourly (hourly) COMMENT '小时时间戳索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '好友加成记录表';

3.1.8 用户社交媒体账号表
CREATE TABLE user_social_accounts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID，自增主键',
    user_id BIGINT NOT NULL COMMENT '用户ID，关联用户表',
    platform_type ENUM('twitter', 'telegram') NOT NULL COMMENT '社交平台类型：twitter、telegram等',
    platform_user_id VARCHAR(100) COMMENT '用户在社交平台上的唯一ID',
    username VARCHAR(100) COMMENT '用户在社交平台上的用户名',
    access_token VARCHAR(255) COMMENT '访问令牌，用于调用社交平台API',
    refresh_token VARCHAR(255) COMMENT '刷新令牌，用于获取新的access_token',
    token_expiry TIMESTAMP COMMENT '令牌过期时间',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证，TRUE-已验证，FALSE-未验证',
    verification_time TIMESTAMP COMMENT '验证完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引，用于快速查询用户的社交账号',
    INDEX idx_platform (platform_type) COMMENT '平台类型索引，用于按平台类型查询',
    UNIQUE KEY uk_user_platform (user_id, platform_type) COMMENT '用户ID和平台类型的唯一索引，确保用户在每个平台只有一个账号'
);

3.1.9 用户邀请关系表（原有的好友关系表，需要确认是否迁移历史数据）：
CREATE TABLE `user_invite_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `host_uid` bigint NOT NULL COMMENT '邀请人用户id',
  `invitee_uid` bigint NOT NULL COMMENT '被邀请人用户id',
  `from_award_ccy` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邀请人奖励币种',
  `from_award_amount` decimal(32,18) NOT NULL COMMENT '邀请人奖励数量',
  `to_award_ccy` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '奖励币种',
  `to_award_amount` decimal(32,18) NOT NULL COMMENT '奖励数量',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `host_inkvitee_unique` (`host_uid`,`invitee_uid`) USING BTREE COMMENT '邀请关系唯一约束',
  KEY `host_index` (`host_uid`) USING BTREE COMMENT '邀请人ID索引',
  KEY `invitee_index` (`invitee_uid`) USING BTREE COMMENT '被邀请人ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户邀请关系表';

3.1.10 每小时平均能量表
CREATE TABLE hourly_average_ep (
    hourly TIMESTAMP NOT NULL COMMENT '小时整点时间戳',
    hour_time datetime NOT NULL COMMENT '小时整点时间',
    user_count BIGINT NOT NULL COMMENT '参与用户总数',
    ep_sum BIGINT NOT NULL COMMENT '用户能量总和',
    ep_avg INT NOT NULL COMMENT '平均能量值',
    create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (hourly)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '每小时平均能量表';
******* 数据一致性保障