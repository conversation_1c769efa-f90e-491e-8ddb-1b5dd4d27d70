#!/usr/bin/env python3
import pymysql
import logging
from datetime import datetime, timezone
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("hourly_ep_comparison.log"),
        logging.StreamHandler()
    ]
)

# 数据库配置
DB_CONFIG = {
    'host': 'xme-prod-media-task-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': False
}


def get_db_connection():
    """获取数据库连接"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        logging.info("成功连接到数据库")
        return conn
    except Exception as e:
        logging.error(f"连接数据库失败: {str(e)}")
        return None

def get_main_table_data(conn):
    """获取主表user_hourly_ep中2025-06-30的数据"""
    try:
        with conn.cursor() as cursor:
            query = """
            SELECT
                user_id,
                expedition_id,
                hourly,
                hour_ep,
                bonus_ep,
                create_time,
                update_time
            FROM user_hourly_ep
            WHERE DATE(hourly) = '2025-06-30'
                AND delete_status = 0
            ORDER BY user_id, hourly
            """

            cursor.execute(query)
            results = cursor.fetchall()

            logging.info(f"主表user_hourly_ep中找到 {len(results)} 条2025-06-30的记录")

            # 转换为字典，key为(user_id, hourly)
            main_data = {}
            for row in results:
                key = (row['user_id'], row['hourly'])
                main_data[key] = row

            return main_data

    except Exception as e:
        logging.error(f"查询主表数据时出错: {str(e)}")
        return {}

def get_shard_table_data(conn):
    """获取分表user_hourly_ep_20250630的数据"""
    try:
        with conn.cursor() as cursor:
            # 先检查分表是否存在
            cursor.execute("SHOW TABLES LIKE 'user_hourly_ep_20250630'")
            if not cursor.fetchone():
                logging.warning("分表user_hourly_ep_20250630不存在")
                return {}

            query = """
            SELECT
                user_id,
                expedition_id,
                hourly,
                hour_ep,
                bonus_ep,
                create_time,
                update_time
            FROM user_hourly_ep_20250630
            WHERE delete_status = 0
            ORDER BY user_id, hourly
            """

            cursor.execute(query)
            results = cursor.fetchall()

            logging.info(f"分表user_hourly_ep_20250630中找到 {len(results)} 条记录")

            # 转换为字典，key为(user_id, hourly)
            shard_data = {}
            for row in results:
                key = (row['user_id'], row['hourly'])
                shard_data[key] = row

            return shard_data

    except Exception as e:
        logging.error(f"查询分表数据时出错: {str(e)}")
        return {}

def compare_data(main_data, shard_data):
    """对比两个表的数据"""
    differences = []
    only_in_main = []
    only_in_shard = []

    # 获取所有的key
    all_keys = set(main_data.keys()) | set(shard_data.keys())

    for key in all_keys:
        user_id, hourly = key

        main_record = main_data.get(key)
        shard_record = shard_data.get(key)

        if main_record and shard_record:
            # 两个表都有记录，比较差异
            diff_info = {
                'user_id': user_id,
                'hourly': hourly.strftime('%Y-%m-%d %H:%M:%S'),
                'expedition_id_main': main_record['expedition_id'],
                'expedition_id_shard': shard_record['expedition_id'],
                'hour_ep_main': main_record['hour_ep'],
                'hour_ep_shard': shard_record['hour_ep'],
                'bonus_ep_main': main_record['bonus_ep'],
                'bonus_ep_shard': shard_record['bonus_ep'],
                'hour_ep_diff': main_record['hour_ep'] - shard_record['hour_ep'],
                'bonus_ep_diff': main_record['bonus_ep'] - shard_record['bonus_ep']
            }

            # 只记录有差异的记录
            if (diff_info['hour_ep_diff'] != 0 or
                diff_info['bonus_ep_diff'] != 0 or
                diff_info['expedition_id_main'] != diff_info['expedition_id_shard']):
                differences.append(diff_info)

        elif main_record and not shard_record:
            # 只在主表中存在
            only_in_main.append({
                'user_id': user_id,
                'hourly': hourly.strftime('%Y-%m-%d %H:%M:%S'),
                'expedition_id': main_record['expedition_id'],
                'hour_ep': main_record['hour_ep'],
                'bonus_ep': main_record['bonus_ep']
            })

        elif not main_record and shard_record:
            # 只在分表中存在
            only_in_shard.append({
                'user_id': user_id,
                'hourly': hourly.strftime('%Y-%m-%d %H:%M:%S'),
                'expedition_id': shard_record['expedition_id'],
                'hour_ep': shard_record['hour_ep'],
                'bonus_ep': shard_record['bonus_ep']
            })

    return {
        'differences': differences,
        'only_in_main': only_in_main,
        'only_in_shard': only_in_shard
    }

def save_comparison_report(comparison_result, filename="hourly_ep_comparison_report.json"):
    """保存对比报告"""
    try:
        report = {
            'comparison_time': datetime.now(timezone.utc).isoformat(),
            'summary': {
                'total_differences': len(comparison_result['differences']),
                'only_in_main_table': len(comparison_result['only_in_main']),
                'only_in_shard_table': len(comparison_result['only_in_shard'])
            },
            'data': comparison_result
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logging.info(f"对比报告已保存到: {filename}")

        # 同时保存人类可读的文本报告
        text_filename = filename.replace('.json', '.txt')
        save_text_report(comparison_result, text_filename)

    except Exception as e:
        logging.error(f"保存对比报告时出错: {str(e)}")

def save_text_report(comparison_result, filename="hourly_ep_comparison_report.txt"):
    """保存文本格式的对比报告"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== 用户小时能量点数据对比报告 ===\n")
            f.write(f"对比时间: {datetime.now(timezone.utc).isoformat()}\n")
            f.write(f"对比日期: 2025-06-30\n\n")

            f.write("=== 汇总统计 ===\n")
            f.write(f"有差异的记录数: {len(comparison_result['differences'])}\n")
            f.write(f"只在主表中的记录数: {len(comparison_result['only_in_main'])}\n")
            f.write(f"只在分表中的记录数: {len(comparison_result['only_in_shard'])}\n\n")

            # 有差异的记录
            if comparison_result['differences']:
                f.write("=== 数据差异记录 ===\n")
                f.write(f"{'用户ID':<12} {'时间':<20} {'主表hour_ep':<12} {'分表hour_ep':<12} {'hour_ep差异':<12} {'主表bonus_ep':<13} {'分表bonus_ep':<13} {'bonus_ep差异':<13}\n")
                f.write("-" * 140 + "\n")

                for diff in comparison_result['differences']:
                    f.write(f"{diff['user_id']:<12} "
                           f"{diff['hourly']:<20} "
                           f"{diff['hour_ep_main']:<12} "
                           f"{diff['hour_ep_shard']:<12} "
                           f"{diff['hour_ep_diff']:<12} "
                           f"{diff['bonus_ep_main']:<13} "
                           f"{diff['bonus_ep_shard']:<13} "
                           f"{diff['bonus_ep_diff']:<13}\n")
                f.write("\n")

            # 只在主表中的记录
            if comparison_result['only_in_main']:
                f.write("=== 只在主表中的记录 ===\n")
                for record in comparison_result['only_in_main'][:100]:  # 只显示前100条
                    f.write(f"用户ID: {record['user_id']}, 时间: {record['hourly']}, "
                           f"hour_ep: {record['hour_ep']}, bonus_ep: {record['bonus_ep']}\n")
                if len(comparison_result['only_in_main']) > 100:
                    f.write(f"... 还有 {len(comparison_result['only_in_main']) - 100} 条记录\n")
                f.write("\n")

            # 只在分表中的记录
            if comparison_result['only_in_shard']:
                f.write("=== 只在分表中的记录 ===\n")
                for record in comparison_result['only_in_shard'][:100]:  # 只显示前100条
                    f.write(f"用户ID: {record['user_id']}, 时间: {record['hourly']}, "
                           f"hour_ep: {record['hour_ep']}, bonus_ep: {record['bonus_ep']}\n")
                if len(comparison_result['only_in_shard']) > 100:
                    f.write(f"... 还有 {len(comparison_result['only_in_shard']) - 100} 条记录\n")
                f.write("\n")

        logging.info(f"文本报告已保存到: {filename}")

    except Exception as e:
        logging.error(f"保存文本报告时出错: {str(e)}")

def main():
    """主函数"""
    logging.info("开始对比用户小时能量点数据...")
    start_time = datetime.now(timezone.utc)

    # 连接数据库
    conn = get_db_connection()
    if not conn:
        logging.error("无法连接数据库，退出程序")
        return

    try:
        # 获取主表数据
        logging.info("正在获取主表user_hourly_ep中2025-06-30的数据...")
        main_data = get_main_table_data(conn)

        # 获取分表数据
        logging.info("正在获取分表user_hourly_ep_20250630的数据...")
        shard_data = get_shard_table_data(conn)

        if not main_data and not shard_data:
            logging.warning("两个表都没有找到数据")
            return

        # 对比数据
        logging.info("正在对比数据...")
        comparison_result = compare_data(main_data, shard_data)

        # 输出结果
        logging.info("=== 数据对比结果 ===")
        logging.info(f"有差异的记录数: {len(comparison_result['differences'])}")
        logging.info(f"只在主表中的记录数: {len(comparison_result['only_in_main'])}")
        logging.info(f"只在分表中的记录数: {len(comparison_result['only_in_shard'])}")

        # 显示前几条差异记录
        if comparison_result['differences']:
            logging.info("\n=== 前5条差异记录 ===")
            for i, diff in enumerate(comparison_result['differences'][:5]):
                logging.info(f"用户{diff['user_id']} {diff['hourly']}: "
                           f"hour_ep差异={diff['hour_ep_diff']}, "
                           f"bonus_ep差异={diff['bonus_ep_diff']}")

        # 保存报告
        save_comparison_report(comparison_result)

        # 计算执行时间
        end_time = datetime.now(timezone.utc)
        duration = end_time - start_time
        logging.info(f"对比完成，总耗时: {duration}")

    except KeyboardInterrupt:
        logging.info("用户中断对比任务")
    except Exception as e:
        logging.error(f"对比任务出错: {str(e)}")
    finally:
        conn.close()
        logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
