#!/usr/bin/env python3
import logging
import pymysql
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("migrate_invitations.log"),
        logging.StreamHandler()
    ]
)

# Database server configurations
# 媒体积分数据库配置
POINT_DB = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_point'
}

# 任务数据库配置
TASK_DB = {
    'host': 'xme-envtest-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_task'
}

def get_db_config(**kwargs):
    """
    Helper function to prepare database configuration.
    Returns the database configuration dictionary with cursor settings.
    
    Args:
        **kwargs: Database connection parameters (host, user, password, database)
        
    Returns:
        dict: Database configuration with cursor settings
    """
    # Create a copy of the input config
    config = dict(kwargs)
    
    # Add cursor class configuration for dictionary results
    config['cursorclass'] = pymysql.cursors.DictCursor
    
    # Add other common configuration parameters if needed
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    
    return config

def migrate_invitation_relationships():
    """
    Migrate user invitation relationships from media_point database to task database.
    - Fetches invitation records from media_point database
    - Inserts records into media_task.user_invite_record
    - Ignores duplicate entries based on unique constraint (host_uid, invitee_uid)
    """
    # Connect to media_point database
    point_db_config = get_db_config(**POINT_DB)
    
    # Connect to media_task database
    task_db_config = get_db_config(**TASK_DB)
    
    try:
        # Step 1: Fetch invitation records from media_point database
        point_conn = pymysql.connect(**point_db_config)
        invitation_records = []
        
        try:
            with point_conn.cursor() as cursor:
                # Get invitation records from media_point
                # Adjust this query based on the actual table and column names in media_point
                query = """
                SELECT 
                    host_uid, 
                    invitee_uid, 
                    from_award_ccy, 
                    from_award_amount, 
                    to_award_ccy, 
                    to_award_amount,
                    created_time
                FROM user_invite_record 
                WHERE host_uid IS NOT NULL 
                AND invitee_uid IS NOT NULL
                """
                cursor.execute(query)
                invitation_records = cursor.fetchall()
                logging.info(f"Found {len(invitation_records)} invitation records in media_point database")
        finally:
            point_conn.close()
        
        if not invitation_records:
            logging.info("No invitation records found. Exiting.")
            return
        
        # Step 2: Insert records into media_task database
        task_conn = pymysql.connect(**task_db_config)
        
        try:
            with task_conn.cursor() as cursor:
                # Track statistics
                total_records = len(invitation_records)
                inserted_count = 0
                duplicate_count = 0
                error_count = 0
                
                for record in invitation_records:
                    # Begin transaction
                    try:
                        # Insert record into user_invite_record table
                        # Using INSERT IGNORE to skip duplicates based on unique constraint
                        insert_query = """
                        INSERT IGNORE INTO user_invite_record 
                        (host_uid, invitee_uid, from_award_ccy, from_award_amount, 
                         to_award_ccy, to_award_amount, created_time) 
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        # Execute the insert query
                        result = cursor.execute(
                            insert_query, 
                            (
                                record['host_uid'], 
                                record['invitee_uid'], 
                                record['from_award_ccy'], 
                                record['from_award_amount'], 
                                record['to_award_ccy'], 
                                record['to_award_amount'],
                                record.get('created_time', datetime.now())
                            )
                        )
                        
                        # Check if the record was inserted or ignored
                        if result == 1:
                            inserted_count += 1
                        else:
                            duplicate_count += 1
                        
                        # Commit the transaction
                        task_conn.commit()
                        
                    except Exception as e:
                        task_conn.rollback()
                        error_count += 1
                        logging.error(f"Error processing invitation record {record['host_uid']} -> {record['invitee_uid']}: {str(e)}")
                
                # Log the results
                logging.info(f"Migration completed: {inserted_count} records inserted, {duplicate_count} duplicates ignored, {error_count} errors")
        finally:
            task_conn.close()
            
        logging.info("Invitation relationship migration completed")
        
    except Exception as e:
        logging.error(f"Error in migrate_invitation_relationships: {str(e)}")

def main():
    """
    Main function to run the migration
    """
    logging.info("Starting invitation relationship migration")
    migrate_invitation_relationships()
    logging.info("Migration process completed")

if __name__ == "__main__":
    main()
