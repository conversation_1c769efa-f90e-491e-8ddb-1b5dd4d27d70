#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图文混排内容统计脚本
基于posts表的media_data和segments_data字段进行精确统计
"""

import pymysql
import json
import re
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'your_username',
    'password': 'your_password',
    'database': 'ugc_hub',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

class MixedContentAnalyzer:
    def __init__(self, db_config):
        self.db_config = db_config
        self.connection = None
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def close_db(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def parse_media_data(self, media_data_str):
        """解析media_data字段"""
        if not media_data_str or media_data_str.strip() == '':
            return []
        
        try:
            media_data = json.loads(media_data_str)
            if not isinstance(media_data, list):
                return []
            return media_data
        except (json.JSONDecodeError, TypeError):
            return []
    
    def parse_segments_data(self, segments_data_str):
        """解析segments_data字段"""
        if not segments_data_str or segments_data_str.strip() == '':
            return []
        
        try:
            segments_data = json.loads(segments_data_str)
            if not isinstance(segments_data, list):
                return []
            return segments_data
        except (json.JSONDecodeError, TypeError):
            return []
    
    def analyze_content_composition(self, post):
        """分析单个帖子的内容组成"""
        media_data = self.parse_media_data(post.get('media_data', ''))
        segments_data = self.parse_segments_data(post.get('segments_data', ''))
        content = post.get('content', '') or ''
        
        # 媒体类型分析
        media_types = set()
        media_count = {'images': 0, 'videos': 0, 'total': 0}
        
        for media in media_data:
            media_type = media.get('type')
            if media_type == 1:  # 图片
                media_types.add('image')
                media_count['images'] += 1
            elif media_type == 2:  # 视频
                media_types.add('video')
                media_count['videos'] += 1
            media_count['total'] += 1
        
        # 文本内容分析
        text_info = {
            'has_text': bool(content and content.strip()),
            'text_length': len(content.strip()) if content else 0,
            'has_segments': bool(segments_data),
            'text_segments': 0,
            'topic_segments': 0,
            'other_segments': 0
        }
        
        # 分析segments_data
        for segment in segments_data:
            segment_type = segment.get('type', '')
            if segment_type == 'text':
                text_info['text_segments'] += 1
            elif segment_type == 'topic':
                text_info['topic_segments'] += 1
            else:
                text_info['other_segments'] += 1
        
        # 判断混排类型
        mixed_type = self.classify_mixed_content(media_types, text_info, media_count)
        
        return {
            'media_types': list(media_types),
            'media_count': media_count,
            'text_info': text_info,
            'mixed_type': mixed_type,
            'is_mixed_content': len(media_types) > 0 and text_info['has_text']
        }
    
    def classify_mixed_content(self, media_types, text_info, media_count):
        """分类混排内容类型"""
        has_text = text_info['has_text']
        has_image = 'image' in media_types
        has_video = 'video' in media_types
        
        if not has_text and not (has_image or has_video):
            return '纯文本或空内容'
        elif has_text and not (has_image or has_video):
            return '纯文本'
        elif not has_text and has_image and not has_video:
            return '纯图片'
        elif not has_text and has_video and not has_image:
            return '纯视频'
        elif not has_text and has_image and has_video:
            return '图片+视频（无文字）'
        elif has_text and has_image and not has_video:
            return '图片+文字混排'
        elif has_text and has_video and not has_image:
            return '视频+文字混排'
        elif has_text and has_image and has_video:
            return '图片+视频+文字混排'
        else:
            return '其他类型'
    
    def get_posts_data(self, limit=10000):
        """获取帖子数据"""
        query = """
        SELECT 
            id, user_id, content_type, post_type, content, 
            segments_data, lang, media_data, status, visibility,
            is_recommended, created_at, updated_at
        FROM posts 
        WHERE is_del = 0
        ORDER BY created_at DESC
        LIMIT %s
        """
        
        with self.connection.cursor() as cursor:
            cursor.execute(query, (limit,))
            return cursor.fetchall()
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        logger.info("开始获取数据...")
        posts = self.get_posts_data()
        logger.info(f"获取到 {len(posts)} 条帖子数据")
        
        # 统计维度
        stats = {
            'total_posts': len(posts),
            'mixed_type_distribution': Counter(),
            'media_count_distribution': Counter(),
            'text_length_distribution': Counter(),
            'user_stats': defaultdict(lambda: {
                'total_posts': 0,
                'mixed_posts': 0,
                'image_text_posts': 0,
                'video_text_posts': 0,
                'image_video_text_posts': 0
            }),
            'daily_stats': defaultdict(lambda: {
                'total_posts': 0,
                'mixed_posts': 0,
                'image_text': 0,
                'video_text': 0,
                'image_video_text': 0
            }),
            'language_stats': defaultdict(lambda: {
                'total_posts': 0,
                'mixed_posts': 0
            }),
            'status_stats': defaultdict(lambda: {
                'total_posts': 0,
                'mixed_posts': 0
            }),
            'segments_analysis': {
                'posts_with_segments': 0,
                'text_segments_total': 0,
                'topic_segments_total': 0,
                'other_segments_total': 0
            }
        }
        
        logger.info("开始分析数据...")
        for i, post in enumerate(posts):
            if i % 1000 == 0:
                logger.info(f"已处理 {i}/{len(posts)} 条数据")
            
            analysis = self.analyze_content_composition(post)
            user_id = post['user_id']
            created_date = post['created_at'].date().isoformat()
            lang = post.get('lang', 'unknown')
            status = post.get('status', 0)
            
            # 混排类型分布
            mixed_type = analysis['mixed_type']
            stats['mixed_type_distribution'][mixed_type] += 1
            
            # 媒体数量分布
            total_media = analysis['media_count']['total']
            if total_media > 0:
                if total_media <= 1:
                    range_key = '1个媒体文件'
                elif total_media <= 3:
                    range_key = '2-3个媒体文件'
                elif total_media <= 5:
                    range_key = '4-5个媒体文件'
                else:
                    range_key = '6个以上媒体文件'
                stats['media_count_distribution'][range_key] += 1
            
            # 文字长度分布
            text_length = analysis['text_info']['text_length']
            if text_length == 0:
                length_range = '无文字'
            elif text_length <= 20:
                length_range = '1-20字符'
            elif text_length <= 50:
                length_range = '21-50字符'
            elif text_length <= 100:
                length_range = '51-100字符'
            else:
                length_range = '100字符以上'
            stats['text_length_distribution'][length_range] += 1
            
            # 用户统计
            user_stat = stats['user_stats'][user_id]
            user_stat['total_posts'] += 1
            if analysis['is_mixed_content']:
                user_stat['mixed_posts'] += 1
                
                if mixed_type == '图片+文字混排':
                    user_stat['image_text_posts'] += 1
                elif mixed_type == '视频+文字混排':
                    user_stat['video_text_posts'] += 1
                elif mixed_type == '图片+视频+文字混排':
                    user_stat['image_video_text_posts'] += 1
            
            # 日期统计
            daily_stat = stats['daily_stats'][created_date]
            daily_stat['total_posts'] += 1
            if analysis['is_mixed_content']:
                daily_stat['mixed_posts'] += 1
                if mixed_type == '图片+文字混排':
                    daily_stat['image_text'] += 1
                elif mixed_type == '视频+文字混排':
                    daily_stat['video_text'] += 1
                elif mixed_type == '图片+视频+文字混排':
                    daily_stat['image_video_text'] += 1
            
            # 语言统计
            lang_stat = stats['language_stats'][lang]
            lang_stat['total_posts'] += 1
            if analysis['is_mixed_content']:
                lang_stat['mixed_posts'] += 1
            
            # 状态统计
            status_stat = stats['status_stats'][status]
            status_stat['total_posts'] += 1
            if analysis['is_mixed_content']:
                status_stat['mixed_posts'] += 1
            
            # segments分析
            if analysis['text_info']['has_segments']:
                stats['segments_analysis']['posts_with_segments'] += 1
                stats['segments_analysis']['text_segments_total'] += analysis['text_info']['text_segments']
                stats['segments_analysis']['topic_segments_total'] += analysis['text_info']['topic_segments']
                stats['segments_analysis']['other_segments_total'] += analysis['text_info']['other_segments']
        
        return stats
    
    def print_statistics(self, stats):
        """打印统计结果"""
        print("\n" + "="*80)
        print("图文混排内容统计报告")
        print("="*80)
        
        print(f"\n📊 总体概况:")
        print(f"总帖子数: {stats['total_posts']:,}")
        
        print(f"\n🎯 混排类型分布:")
        for mixed_type, count in stats['mixed_type_distribution'].most_common():
            percentage = (count / stats['total_posts']) * 100
            print(f"  {mixed_type}: {count:,} ({percentage:.2f}%)")
        
        print(f"\n📱 媒体文件数量分布:")
        for range_key, count in stats['media_count_distribution'].most_common():
            percentage = (count / stats['total_posts']) * 100
            print(f"  {range_key}: {count:,} ({percentage:.2f}%)")
        
        print(f"\n📝 文字长度分布:")
        for length_range, count in stats['text_length_distribution'].most_common():
            percentage = (count / stats['total_posts']) * 100
            print(f"  {length_range}: {count:,} ({percentage:.2f}%)")
        
        print(f"\n🌍 语言分布 (Top 10):")
        for lang, lang_stat in list(stats['language_stats'].items())[:10]:
            mixed_rate = (lang_stat['mixed_posts'] / lang_stat['total_posts']) * 100 if lang_stat['total_posts'] > 0 else 0
            print(f"  {lang}: 总数{lang_stat['total_posts']:,}, 混排{lang_stat['mixed_posts']:,} ({mixed_rate:.1f}%)")
        
        print(f"\n📈 审核状态分布:")
        status_names = {0: '待审核', 1: '审核中', 2: '已发布', 3: '审核不通过'}
        for status, status_stat in stats['status_stats'].items():
            status_name = status_names.get(status, f'状态{status}')
            mixed_rate = (status_stat['mixed_posts'] / status_stat['total_posts']) * 100 if status_stat['total_posts'] > 0 else 0
            print(f"  {status_name}: 总数{status_stat['total_posts']:,}, 混排{status_stat['mixed_posts']:,} ({mixed_rate:.1f}%)")
        
        print(f"\n🎨 Segments分析:")
        segments_stats = stats['segments_analysis']
        print(f"  包含segments的帖子: {segments_stats['posts_with_segments']:,}")
        print(f"  文本片段总数: {segments_stats['text_segments_total']:,}")
        print(f"  话题片段总数: {segments_stats['topic_segments_total']:,}")
        print(f"  其他片段总数: {segments_stats['other_segments_total']:,}")
        
        print(f"\n👥 活跃创作者 Top 10 (混排内容):")
        top_users = sorted(
            [(user_id, user_stat) for user_id, user_stat in stats['user_stats'].items()],
            key=lambda x: x[1]['mixed_posts'],
            reverse=True
        )[:10]
        
        for user_id, user_stat in top_users:
            if user_stat['mixed_posts'] > 0:
                print(f"  用户{user_id}: 总帖子{user_stat['total_posts']}, "
                      f"混排{user_stat['mixed_posts']}, "
                      f"图文{user_stat['image_text_posts']}, "
                      f"视频文字{user_stat['video_text_posts']}, "
                      f"图视频文字{user_stat['image_video_text_posts']}")
        
        print(f"\n📅 最近7天趋势:")
        recent_dates = sorted(stats['daily_stats'].keys())[-7:]
        for date in recent_dates:
            daily_stat = stats['daily_stats'][date]
            mixed_rate = (daily_stat['mixed_posts'] / daily_stat['total_posts']) * 100 if daily_stat['total_posts'] > 0 else 0
            print(f"  {date}: 总数{daily_stat['total_posts']}, "
                  f"混排{daily_stat['mixed_posts']} ({mixed_rate:.1f}%), "
                  f"图文{daily_stat['image_text']}, "
                  f"视频文字{daily_stat['video_text']}, "
                  f"图视频文字{daily_stat['image_video_text']}")

def main():
    """主函数"""
    analyzer = MixedContentAnalyzer(DB_CONFIG)
    
    try:
        analyzer.connect_db()
        stats = analyzer.run_comprehensive_analysis()
        analyzer.print_statistics(stats)
        
    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}")
        raise
    finally:
        analyzer.close_db()

if __name__ == "__main__":
    main() 