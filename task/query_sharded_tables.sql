-- 查询分表的user_like和user_read表 (1024张分表，后缀_0到_1023)
-- 适用于MySQL数据库

-- =============================================================================
-- 1. 查询所有user_like分表的数据汇总
-- =============================================================================

-- 生成查询所有user_like分表的UNION语句
SELECT 'user_like表数据汇总查询' as query_type;

-- 示例：统计所有user_like分表的总记录数
SELECT 
    'user_like' as table_type,
    SUM(total_count) as total_records,
    COUNT(*) as table_count
FROM (
    SELECT COUNT(*) as total_count FROM user_like_0
    UNION ALL SELECT COUNT(*) FROM user_like_1
    UNION ALL SELECT COUNT(*) FROM user_like_2
    UNION ALL SELECT COUNT(*) FROM user_like_3
    UNION ALL SELECT COUNT(*) FROM user_like_4
    UNION ALL SELECT COUNT(*) FROM user_like_5
    UNION ALL SELECT COUNT(*) FROM user_like_6
    UNION ALL SELECT COUNT(*) FROM user_like_7
    UNION ALL SELECT COUNT(*) FROM user_like_8
    UNION ALL SELECT COUNT(*) FROM user_like_9
    -- ... 这里需要继续添加到user_like_1023
    -- 由于SQL太长，建议使用程序生成完整的UNION语句
) as like_counts;

-- =============================================================================
-- 2. 查询所有user_read分表的数据汇总
-- =============================================================================

-- 示例：统计所有user_read分表的总记录数
SELECT 
    'user_read' as table_type,
    SUM(total_count) as total_records,
    COUNT(*) as table_count
FROM (
    SELECT COUNT(*) as total_count FROM user_read_0
    UNION ALL SELECT COUNT(*) FROM user_read_1
    UNION ALL SELECT COUNT(*) FROM user_read_2
    UNION ALL SELECT COUNT(*) FROM user_read_3
    UNION ALL SELECT COUNT(*) FROM user_read_4
    UNION ALL SELECT COUNT(*) FROM user_read_5
    UNION ALL SELECT COUNT(*) FROM user_read_6
    UNION ALL SELECT COUNT(*) FROM user_read_7
    UNION ALL SELECT COUNT(*) FROM user_read_8
    UNION ALL SELECT COUNT(*) FROM user_read_9
    -- ... 这里需要继续添加到user_read_1023
) as read_counts;

-- =============================================================================
-- 3. 根据用户ID查询特定用户的点赞数据 (需要计算分表索引)
-- =============================================================================

-- 示例：查询用户ID为12345的点赞记录
-- 假设分表规则是 user_id % 1024
SET @user_id = 12345;
SET @table_suffix = @user_id % 1024;

-- 注意：MySQL不支持动态表名，需要使用存储过程或程序生成具体SQL
-- 以下是示例模板，实际使用时需要替换表名

-- 如果用户ID为12345，则 12345 % 1024 = 345，应查询user_like_345表
SELECT * FROM user_like_345 WHERE user_id = 12345;

-- =============================================================================
-- 4. 根据用户ID查询特定用户的阅读数据
-- =============================================================================

-- 查询用户ID为12345的阅读记录
SELECT * FROM user_read_345 WHERE user_id = 12345;

-- =============================================================================
-- 5. 统计每个分表的记录数 (前10个表示例)
-- =============================================================================

SELECT 'user_like分表记录统计' as info;
SELECT 'user_like_0' as table_name, COUNT(*) as record_count FROM user_like_0
UNION ALL
SELECT 'user_like_1' as table_name, COUNT(*) as record_count FROM user_like_1
UNION ALL
SELECT 'user_like_2' as table_name, COUNT(*) as record_count FROM user_like_2
UNION ALL
SELECT 'user_like_3' as table_name, COUNT(*) as record_count FROM user_like_3
UNION ALL
SELECT 'user_like_4' as table_name, COUNT(*) as record_count FROM user_like_4
UNION ALL
SELECT 'user_like_5' as table_name, COUNT(*) as record_count FROM user_like_5
UNION ALL
SELECT 'user_like_6' as table_name, COUNT(*) as record_count FROM user_like_6
UNION ALL
SELECT 'user_like_7' as table_name, COUNT(*) as record_count FROM user_like_7
UNION ALL
SELECT 'user_like_8' as table_name, COUNT(*) as record_count FROM user_like_8
UNION ALL
SELECT 'user_like_9' as table_name, COUNT(*) as record_count FROM user_like_9
ORDER BY table_name;

SELECT 'user_read分表记录统计' as info;
SELECT 'user_read_0' as table_name, COUNT(*) as record_count FROM user_read_0
UNION ALL
SELECT 'user_read_1' as table_name, COUNT(*) as record_count FROM user_read_1
UNION ALL
SELECT 'user_read_2' as table_name, COUNT(*) as record_count FROM user_read_2
UNION ALL
SELECT 'user_read_3' as table_name, COUNT(*) as record_count FROM user_read_3
UNION ALL
SELECT 'user_read_4' as table_name, COUNT(*) as record_count FROM user_read_4
UNION ALL
SELECT 'user_read_5' as table_name, COUNT(*) as record_count FROM user_read_5
UNION ALL
SELECT 'user_read_6' as table_name, COUNT(*) as record_count FROM user_read_6
UNION ALL
SELECT 'user_read_7' as table_name, COUNT(*) as record_count FROM user_read_7
UNION ALL
SELECT 'user_read_8' as table_name, COUNT(*) as record_count FROM user_read_8
UNION ALL
SELECT 'user_read_9' as table_name, COUNT(*) as record_count FROM user_read_9
ORDER BY table_name;

-- =============================================================================
-- 6. 联合查询用户的点赞和阅读数据
-- =============================================================================

-- 示例：查询用户12345的点赞和阅读统计
-- 需要根据实际的分表索引替换表名
SELECT 
    u.user_id,
    COUNT(l.id) as like_count,
    COUNT(r.id) as read_count
FROM 
    (SELECT DISTINCT user_id FROM user_like_345 WHERE user_id = 12345
     UNION 
     SELECT DISTINCT user_id FROM user_read_345 WHERE user_id = 12345) u
LEFT JOIN user_like_345 l ON u.user_id = l.user_id
LEFT JOIN user_read_345 r ON u.user_id = r.user_id
WHERE u.user_id = 12345
GROUP BY u.user_id;

-- =============================================================================
-- 注意事项：
-- 1. 由于MySQL不支持动态表名，实际使用时需要：
--    - 使用存储过程
--    - 通过程序生成完整的SQL语句
--    - 使用预处理语句
-- 
-- 2. 分表规则通常是：table_name_${user_id % 1024}
-- 
-- 3. 查询跨多个分表时，建议使用程序循环查询而不是长UNION语句
-- 
-- 4. 对于大数据量查询，建议添加适当的WHERE条件和LIMIT限制
-- ============================================================================= 