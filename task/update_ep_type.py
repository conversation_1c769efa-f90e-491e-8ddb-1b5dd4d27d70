import pymysql
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("update_ep_type.log"),
        logging.StreamHandler()
    ]
)

# 任务数据库配置
TASK_DB = {
    'host': 'xme-envtest-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_task'
}

def get_db_connection():
    """
    Get database connection for media_task database
    
    Returns:
        pymysql.Connection: Database connection
    """
    return pymysql.connect(
        host=TASK_DB['host'],
        user=TASK_DB['user'],
        password=TASK_DB['password'],
        database=TASK_DB['database'],
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

def update_ep_type_for_shard(conn, shard_num):
    """
    Update ep_type from 0 to 1 for a specific shard table
    
    Args:
        conn: Database connection
        shard_num: Shard number (0-1023)
        
    Returns:
        int: Number of records updated
    """
    table_name = f"user_ep_records_{shard_num}"
    
    try:
        with conn.cursor() as cursor:
            # Check if there are records to update
            check_query = f"SELECT COUNT(*) as count FROM {table_name} WHERE ep_type = 0"
            cursor.execute(check_query)
            result = cursor.fetchone()
            count = result['count']
            
            if count == 0:
                logging.info(f"No records to update in {table_name}")
                return 0
            
            # Update records
            update_query = f"UPDATE {table_name} SET ep_type = 1 WHERE ep_type = 0"
            cursor.execute(update_query)
            conn.commit()
            
            updated_count = cursor.rowcount
            logging.info(f"Updated {updated_count} records in {table_name}")
            return updated_count
            
    except Exception as e:
        conn.rollback()
        logging.error(f"Error updating {table_name}: {str(e)}")
        return 0

def main():
    """
    Main function to update ep_type from 0 to 1 across all 1024 sharded tables
    """
    start_time = datetime.now()
    logging.info(f"Starting update process at {start_time}")
    
    total_updated = 0
    conn = get_db_connection()
    
    try:
        for shard_num in range(1024):
            updated = update_ep_type_for_shard(conn, shard_num)
            total_updated += updated
            
            # Log progress every 100 shards
            if shard_num % 100 == 99:
                logging.info(f"Progress: {shard_num + 1}/1024 shards processed")
    
    except Exception as e:
        logging.error(f"Error in main process: {str(e)}")
    
    finally:
        conn.close()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    logging.info(f"Update process completed at {end_time}")
    logging.info(f"Total duration: {duration:.2f} seconds")
    logging.info(f"Total records updated: {total_updated}")

if __name__ == "__main__":
    main()
