#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的分析功能测试脚本
"""

from user_login_analysis import UserLoginAnalyzer

def main():
    print("🧪 测试修复后的APK vs Google Play分析功能")
    print("=" * 50)
    
    # 创建分析器实例
    analyzer = UserLoginAnalyzer(
        host="xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com",
        database="media_user",
        user="pro-user-user",
        password="VcEVqaE5HX",
        port=3306
    )
    
    try:
        print("📊 开始进行分析...")
        
        # 测试修复后的APK vs Google Play分析
        result = analyzer.analyze_android_apk_vs_gp(days=3, sample_size=100)  # 最近3天，抽样100个用户
        
        if result:
            print("✅ 分析成功！")
            print(f"📈 分析时间范围: {result.get('time_range', 'N/A')}")
            
            # 显示分析结果
            print("\n📱 APK用户数据:")
            apk_data = result.get('apk_users', {})
            print(f"  总数: {apk_data.get('total_count', 0)}")
            print(f"  平台分布: {apk_data.get('platform_distribution', {})}")
            print(f"  国家分布: {apk_data.get('country_distribution', {})}")
            print(f"  访问频次统计: {apk_data.get('login_frequency_stats', {})}")
            
            print("\n🏪 Google Play用户数据:")
            gp_data = result.get('gp_users', {})
            print(f"  总数: {gp_data.get('total_count', 0)}")
            print(f"  平台分布: {gp_data.get('platform_distribution', {})}")
            print(f"  国家分布: {gp_data.get('country_distribution', {})}")
            print(f"  访问频次统计: {gp_data.get('login_frequency_stats', {})}")
            
            print("\n🔍 对比分析:")
            comparison = result.get('comparison', {})
            print(f"  用户数量比例 (APK:GP): {comparison.get('user_count_ratio', 'N/A')}")
            print(f"  平均访问频次对比: {comparison.get('avg_login_frequency_comparison', 'N/A')}")
            
            # 显示样本数据
            print("\n📋 APK用户样本数据 (前5个):")
            apk_samples = apk_data.get('sample_data', [])[:5]
            for user in apk_samples:
                print(f"  用户ID: {user.get('uid')}, 国家: {user.get('country_code')}, 访问次数: {user.get('login_count')}")
            
            print("\n📋 Google Play用户样本数据 (前5个):")
            gp_samples = gp_data.get('sample_data', [])[:5]
            for user in gp_samples:
                print(f"  用户ID: {user.get('uid')}, 国家: {user.get('country_code')}, 访问次数: {user.get('login_count')}")
        else:
            print("❌ 分析返回空结果")
    
    except Exception as e:
        print(f"❌ 分析Android APK vs Google Play用户时出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        analyzer.close_connection()

if __name__ == "__main__":
    main() 