select * from user_expedition_status where user_id not in (select user_id from system_user_record) order by expedition_end_time desc limit 30;



#!/usr/bin/env python3
import pymysql
import logging
import redis
from datetime import datetime
from redis.cluster import RedisCluster
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cleanup_invalid_expedition_users.log"),
        logging.StreamHandler()
    ]
)

# 数据库配置
DB_CONFIG = {
    'host': 'xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': False
}

def get_db_connection():
    """
    获取数据库连接
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        logging.info("成功连接到数据库")
        return conn
    except Exception as e:
        logging.error(f"连接数据库失败: {str(e)}")
        return None

def get_redis_connection():
    """
    获取Redis连接
    """
    try:
        # Redis连接参数
        host = "xme-prod-task.otyftu.clustercfg.apse1.cache.amazonaws.com"
        port = 6379
        
        # 创建RedisCluster连接
        redis_conn = RedisCluster(
            host=host,
            port=port,
            decode_responses=True,
            ssl=False
        )
        
        redis_conn.ping()
        logging.info("成功连接到Redis集群")
        return redis_conn
    except Exception as e:
        logging.error(f"连接Redis时出错: {str(e)}")
        return None

def get_invalid_expedition_users(conn, batch_size=1000):
    """
    获取无效的expedition用户ID列表
    
    Args:
        conn: 数据库连接
        batch_size: 每批处理的数量
    
    Returns:
        list: 无效用户ID列表
    """
    logging.info("开始查询无效的expedition用户...")
    
    invalid_users = []
    offset = 0
    
    try:
        with conn.cursor() as cursor:
            while True:
                # 分批查询无效用户
                query = f"""
                SELECT DISTINCT ues.user_id 
                FROM user_expedition_status ues
                WHERE ues.user_id NOT IN (
                    SELECT sur.user_id 
                    FROM system_user_record sur 
                    WHERE sur.user_id = ues.user_id
                )
                LIMIT {batch_size} OFFSET {offset}
                """
                
                cursor.execute(query)
                batch_results = cursor.fetchall()
                
                if not batch_results:
                    break
                
                batch_user_ids = [row['user_id'] for row in batch_results]
                invalid_users.extend(batch_user_ids)
                
                logging.info(f"已查询到 {len(batch_user_ids)} 个无效用户 (总计: {len(invalid_users)})")
                offset += batch_size
                
                # 如果返回的结果少于batch_size，说明已经查询完毕
                if len(batch_results) < batch_size:
                    break
            
            logging.info(f"查询完成，共找到 {len(invalid_users)} 个无效的expedition用户")
            return invalid_users
            
    except Exception as e:
        logging.error(f"查询无效用户时出错: {str(e)}")
        return []

def cleanup_redis_keys(redis_conn, user_ids, batch_size=100):
    """
    批量删除Redis中的expedition信息键
    
    Args:
        redis_conn: Redis连接
        user_ids: 要删除的用户ID列表
        batch_size: 每批处理的数量
    """
    if not user_ids:
        logging.info("没有需要清理的用户ID")
        return
    
    logging.info(f"开始清理 {len(user_ids)} 个用户的Redis键...")
    
    total_deleted = 0
    failed_deletions = []
    
    # 分批处理
    for i in range(0, len(user_ids), batch_size):
        batch_user_ids = user_ids[i:i + batch_size]
        batch_deleted = 0
        
        for user_id in batch_user_ids:
            try:
                redis_key = f"user:cur:expedition:info:{user_id}"
                
                # 检查键是否存在
                if redis_conn.exists(redis_key):
                    # 删除键
                    result = redis_conn.delete(redis_key)
                    if result:
                        batch_deleted += 1
                        total_deleted += 1
                        logging.debug(f"成功删除Redis键: {redis_key}")
                    else:
                        logging.warning(f"删除Redis键失败: {redis_key}")
                        failed_deletions.append(user_id)
                else:
                    logging.debug(f"Redis键不存在: {redis_key}")
                    
            except Exception as e:
                logging.error(f"删除用户 {user_id} 的Redis键时出错: {str(e)}")
                failed_deletions.append(user_id)
        
        # 进度日志
        progress = min(i + batch_size, len(user_ids))
        logging.info(f"批次删除进度: {progress}/{len(user_ids)}, 本批删除: {batch_deleted}")
        
        # 短暂休息，避免对Redis造成过大压力
        time.sleep(0.1)
    
    # 输出最终统计
    logging.info("=== 清理统计结果 ===")
    logging.info(f"待清理用户总数: {len(user_ids)}")
    logging.info(f"成功删除Redis键数量: {total_deleted}")
    logging.info(f"删除失败数量: {len(failed_deletions)}")
    
    if failed_deletions:
        logging.warning("删除失败的用户ID:")
        for user_id in failed_deletions[:20]:  # 只显示前20个
            logging.warning(f"  用户ID: {user_id}")
        if len(failed_deletions) > 20:
            logging.warning(f"  ... 还有 {len(failed_deletions) - 20} 个")

def verify_cleanup_results(redis_conn, sample_user_ids):
    """
    验证清理结果
    
    Args:
        redis_conn: Redis连接
        sample_user_ids: 抽样验证的用户ID列表
    """
    if not sample_user_ids:
        return
    
    logging.info("开始验证清理结果...")
    
    remaining_keys = 0
    sample_size = min(50, len(sample_user_ids))
    
    for user_id in sample_user_ids[:sample_size]:
        redis_key = f"user:cur:expedition:info:{user_id}"
        try:
            if redis_conn.exists(redis_key):
                remaining_keys += 1
                logging.warning(f"验证失败: Redis键仍然存在 - {redis_key}")
        except Exception as e:
            logging.error(f"验证用户 {user_id} 时出错: {str(e)}")
    
    logging.info(f"验证完成: 抽样 {sample_size} 个用户，发现 {remaining_keys} 个键仍然存在")

def save_user_list_to_file(user_ids, filename="invalid_expedition_users.txt"):
    """
    将无效用户ID列表保存到文件
    
    Args:
        user_ids: 用户ID列表
        filename: 保存的文件名
    """
    try:
        with open(filename, 'w') as f:
            f.write(f"# 无效的expedition用户ID列表\n")
            f.write(f"# 生成时间: {datetime.now().isoformat()}\n")
            f.write(f"# 总数量: {len(user_ids)}\n\n")
            
            for user_id in user_ids:
                f.write(f"{user_id}\n")
        
        logging.info(f"用户ID列表已保存到文件: {filename}")
    except Exception as e:
        logging.error(f"保存用户ID列表到文件时出错: {str(e)}")

def main():
    """
    主函数
    """
    logging.info("开始清理无效expedition用户的Redis数据")
    start_time = datetime.now()
    
    # 连接数据库
    db_conn = get_db_connection()
    if not db_conn:
        logging.error("无法连接数据库，退出程序")
        return
    
    # 连接Redis
    redis_conn = get_redis_connection()
    if not redis_conn:
        logging.error("无法连接Redis，退出程序")
        db_conn.close()
        return
    
    try:
        # 步骤1: 查询无效的expedition用户
        invalid_user_ids = get_invalid_expedition_users(db_conn, batch_size=2000)
        
        if not invalid_user_ids:
            logging.info("没有找到无效的expedition用户，程序结束")
            return
        
        # 步骤2: 保存用户列表到文件（备份）
        save_user_list_to_file(invalid_user_ids)
        
        # 步骤3: 清理Redis键
        cleanup_redis_keys(redis_conn, invalid_user_ids, batch_size=200)
        
        # 步骤4: 验证清理结果
        verify_cleanup_results(redis_conn, invalid_user_ids)
        
        end_time = datetime.now()
        duration = end_time - start_time
        logging.info(f"清理任务完成，总耗时: {duration}")
        
    except KeyboardInterrupt:
        logging.info("用户中断清理任务")
    except Exception as e:
        logging.error(f"清理任务出错: {str(e)}")
    finally:
        db_conn.close()
        logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 
