import pymysql
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("update_system_user_virtual_type.log"),
        logging.StreamHandler()
    ]
)

# Database server configurations
# 媒体用户数据库配置
USER_DB = {
    'host': 'xme-prod-rds-user.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_user'
}

# 任务数据库配置
TASK_DB = {
    'host': 'xme-prod-media-task.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'media-task',
    'password': 'xYjU9s10',
    'database': 'media_task'
}

def get_db_config(**kwargs):
    """
    Helper function to prepare database configuration.
    Returns the database configuration dictionary with cursor settings.
    
    Args:
        **kwargs: Database connection parameters (host, user, password, database)
        
    Returns:
        dict: Database configuration with cursor settings
    """
    # Create a copy of the input config
    config = dict(kwargs)
    
    # Add cursor class configuration for dictionary results
    config['cursorclass'] = pymysql.cursors.DictCursor
    
    # Add other common configuration parameters if needed
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False
    
    return config

def update_system_user_virtual_type():
    """
    Update virtual_type to 2 in client_user table for users that correspond to 
    user_id in system_user_record table.
    """
    # Connect to media_task database to get system user IDs
    task_db_config = get_db_config(**TASK_DB)
    
    # Connect to media_user database to update client_user table
    user_db_config = get_db_config(**USER_DB)
    
    try:
        # Step 1: Fetch system user IDs from media_task.system_user_record
        task_conn = pymysql.connect(**task_db_config)
        system_user_ids = []
        
        try:
            with task_conn.cursor() as cursor:
                # Get all non-deleted system user IDs
                query = """
                SELECT user_id 
                FROM system_user_record 
                WHERE delete_status = 0
                """
                cursor.execute(query)
                system_users = cursor.fetchall()
                system_user_ids = [user['user_id'] for user in system_users]
                logging.info(f"Found {len(system_user_ids)} system users")
        except Exception as e:
            logging.error(f"Error fetching system users: {str(e)}")
            task_conn.close()
            return
        finally:
            task_conn.close()
        
        if not system_user_ids:
            logging.info("No system users found. Exiting.")
            return
        
        # Step 2: Update virtual_type in media_user.client_user table
        user_conn = pymysql.connect(**user_db_config)
        
        try:
            with user_conn.cursor() as cursor:
                # Process system users in batches to avoid too many parameters in query
                batch_size = 100
                total_updated = 0
                
                for i in range(0, len(system_user_ids), batch_size):
                    batch = system_user_ids[i:i+batch_size]
                    
                    # Format placeholders for the IN clause
                    placeholders = ', '.join(['%s'] * len(batch))
                    
                    # Update virtual_type to 2 for system users
                    update_query = f"""
                    UPDATE client_user 
                    SET virtual_type = 2 
                    WHERE uid IN ({placeholders})
                    """
                    
                    cursor.execute(update_query, batch)
                    affected_rows = cursor.rowcount
                    total_updated += affected_rows
                    
                    user_conn.commit()
                    logging.info(f"Batch {i//batch_size + 1}: Updated {affected_rows} users")
                
                logging.info(f"Total users updated: {total_updated}")
                
                # Verify the update
                if system_user_ids:
                    placeholders = ', '.join(['%s'] * len(system_user_ids))
                    verify_query = f"""
                    SELECT COUNT(*) as count 
                    FROM client_user 
                    WHERE uid IN ({placeholders}) AND virtual_type = 2
                    """
                    cursor.execute(verify_query, system_user_ids)
                    result = cursor.fetchone()
                    verified_count = result['count'] if result else 0
                    
                    logging.info(f"Verification: {verified_count} out of {len(system_user_ids)} system users now have virtual_type = 2")
                
        except Exception as e:
            user_conn.rollback()
            logging.error(f"Error updating client_user table: {str(e)}")
        finally:
            user_conn.close()
            
        logging.info("System user virtual_type update completed")
        
    except Exception as e:
        logging.error(f"Error in update_system_user_virtual_type: {str(e)}")

def main():
    """
    Main function to run the update process
    """
    logging.info("Starting system user virtual_type update")
    
    # Update virtual_type for system users
    update_system_user_virtual_type()
    
    logging.info("System user virtual_type update completed")

if __name__ == "__main__":
    main()
