#!/usr/bin/env python3
import pymysql
import logging
from datetime import datetime, timezone, timedelta
import decimal
import requests
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("xme_income_statistics.log"),
        logging.StreamHandler()
    ]
)

# 数据库配置 - 使用您提供的数据库配置
DB_CONFIG = {
    'host': 'xme-prod-media-point-readonly.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'pro-user-user',
    'password': 'VcEVqaE5HX',
    'database': 'media_point',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': False
}

# Lark机器人配置
LARK_CONFIG = {
    'webhook_url': 'https://open.larksuite.com/open-apis/bot/v2/hook/1c388384-1221-455c-a82c-06396a5c4472',  # 请填入您的Lark机器人Webhook URL
    'secret': ''        # 请填入您的Lark机器人签名密钥（如果有的话）
}

def get_db_connection():
    """
    获取数据库连接
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        logging.info("成功连接到数据库")
        return conn
    except Exception as e:
        logging.error(f"连接数据库失败: {str(e)}")
        return None

def get_utc_time_range(target_date=None):
    """
    获取指定日期UTC 0点到次日UTC 0点的时间范围
    
    Args:
        target_date: 目标日期，格式为 'YYYY-MM-DD'，如 '2024-01-15'
                    如果为None，则默认统计昨天的数据
    
    Returns:
        tuple: (start_time, end_time) UTC时间
    """
    # 获取当前UTC时间
    now_utc = datetime.now(timezone.utc)
    
    if target_date:
        # 如果指定了目标日期，解析该日期
        try:
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            target_utc = target_dt.replace(tzinfo=timezone.utc)
            start_time = target_utc.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = start_time + timedelta(days=1)
        except ValueError:
            logging.error(f"日期格式错误: {target_date}，应为 'YYYY-MM-DD' 格式")
            raise
    else:
        # 默认统计昨天的数据
        # 今天UTC 0点
        today_utc_start = now_utc.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # 昨天UTC 0点
        yesterday_utc_start = today_utc_start - timedelta(days=1)
        
        start_time = yesterday_utc_start
        end_time = today_utc_start
    
    return start_time, end_time

def get_xme_income_from_table(conn, table_name, start_time, end_time):
    """
    从单个account_journal表中获取XME币的收入统计
    
    Args:
        conn: 数据库连接
        table_name: 表名
        start_time: 开始时间
        end_time: 结束时间
    
    Returns:
        dict: 统计结果
    """
    try:
        with conn.cursor() as cursor:
            # 查询XME币收入的SQL
            # change_type = 1 表示收入
            # ccy = 'XME' 表示XME币种
            # status = 4 表示完成的交易
            # deleted = 0 表示未删除的记录
            query = f"""
            SELECT 
                COUNT(*) as transaction_count,
                COALESCE(SUM(amount), 0) as total_income,
                COALESCE(SUM(gas_fee), 0) as total_gas_fee
            FROM {table_name}
            WHERE change_type = 1
                AND ccy = 'XME'
                AND status = 4
                AND deleted = 0
                AND created_time >= %s
                AND created_time < %s
            """
            
            cursor.execute(query, (start_time, end_time))
            result = cursor.fetchone()
            
            if result:
                return {
                    'table_name': table_name,
                    'transaction_count': result['transaction_count'],
                    'total_income': decimal.Decimal(str(result['total_income'])),
                    'total_gas_fee': decimal.Decimal(str(result['total_gas_fee']))
                }
            else:
                return {
                    'table_name': table_name,
                    'transaction_count': 0,
                    'total_income': decimal.Decimal('0'),
                    'total_gas_fee': decimal.Decimal('0')
                }
                
    except Exception as e:
        logging.error(f"查询表 {table_name} 时出错: {str(e)}")
        return {
            'table_name': table_name,
            'transaction_count': 0,
            'total_income': decimal.Decimal('0'),
            'total_gas_fee': decimal.Decimal('0'),
            'error': str(e)
        }

def get_xme_income_by_event_from_table(conn, table_name, start_time, end_time):
    """
    从单个account_journal表中获取按event_id分组的XME币收入统计
    
    Args:
        conn: 数据库连接
        table_name: 表名
        start_time: 开始时间
        end_time: 结束时间
    
    Returns:
        list: 按event_id分组的统计结果
    """
    try:
        with conn.cursor() as cursor:
            # 按event_id分组查询XME币收入的SQL
            query = f"""
            SELECT 
                event_id,
                COUNT(*) as transaction_count,
                COALESCE(SUM(amount), 0) as total_income,
                COALESCE(SUM(gas_fee), 0) as total_gas_fee
            FROM {table_name}
            WHERE change_type = 1
                AND ccy = 'XME'
                AND status = 4
                AND deleted = 0
                AND created_time >= %s
                AND created_time < %s
            GROUP BY event_id
            ORDER BY total_income DESC
            """
            
            cursor.execute(query, (start_time, end_time))
            results = cursor.fetchall()
            
            event_stats = []
            for result in results:
                event_stats.append({
                    'table_name': table_name,
                    'event_id': result['event_id'],
                    'transaction_count': result['transaction_count'],
                    'total_income': decimal.Decimal(str(result['total_income'])),
                    'total_gas_fee': decimal.Decimal(str(result['total_gas_fee']))
                })
            
            return event_stats
                
    except Exception as e:
        logging.error(f"查询表 {table_name} 按event_id统计时出错: {str(e)}")
        return []

def check_table_exists(conn, table_name):
    """
    检查表是否存在
    
    Args:
        conn: 数据库连接
        table_name: 表名
    
    Returns:
        bool: 表是否存在
    """
    try:
        with conn.cursor() as cursor:
            cursor.execute("SHOW TABLES LIKE %s", (table_name,))
            return cursor.fetchone() is not None
    except Exception as e:
        logging.error(f"检查表 {table_name} 是否存在时出错: {str(e)}")
        return False

def get_all_xme_incomes(conn, start_time, end_time):
    """
    从所有account_journal表中获取XME币的收入统计
    
    Args:
        conn: 数据库连接
        start_time: 开始时间
        end_time: 结束时间
    
    Returns:
        dict: 汇总统计结果
    """
    total_transaction_count = 0
    total_income = decimal.Decimal('0')
    total_gas_fee = decimal.Decimal('0')
    
    processed_tables = 0
    error_tables = []
    table_results = []
    
    # 按event_id汇总的数据
    event_summary = {}
    
    logging.info("开始查询1024张account_journal表...")
    
    # 遍历1024张表（从0到1023或从1到1024，根据实际情况调整）
    for i in range(1024):
        table_name = f"account_journal{i}"
        
        # 检查表是否存在
        if not check_table_exists(conn, table_name):
            logging.debug(f"表 {table_name} 不存在，跳过")
            continue
        
        # 查询单表汇总数据
        result = get_xme_income_from_table(conn, table_name, start_time, end_time)
        table_results.append(result)
        
        # 查询单表按event_id分组的数据
        event_results = get_xme_income_by_event_from_table(conn, table_name, start_time, end_time)
        
        if 'error' in result:
            error_tables.append(table_name)
            logging.warning(f"表 {table_name} 查询出错: {result['error']}")
        else:
            # 累加汇总统计数据
            total_transaction_count += result['transaction_count']
            total_income += result['total_income']
            total_gas_fee += result['total_gas_fee']
            processed_tables += 1
            
            # 累加event_id统计数据
            for event_result in event_results:
                event_id = event_result['event_id']
                if event_id not in event_summary:
                    event_summary[event_id] = {
                        'transaction_count': 0,
                        'total_income': decimal.Decimal('0'),
                        'total_gas_fee': decimal.Decimal('0'),
                        'table_count': 0
                    }
                
                event_summary[event_id]['transaction_count'] += event_result['transaction_count']
                event_summary[event_id]['total_income'] += event_result['total_income']
                event_summary[event_id]['total_gas_fee'] += event_result['total_gas_fee']
                event_summary[event_id]['table_count'] += 1
            
            # 如果该表有数据，记录日志
            if result['transaction_count'] > 0:
                logging.info(f"表 {table_name}: {result['transaction_count']} 笔交易, "
                           f"收入: {result['total_income']} XME, "
                           f"Gas费: {result['total_gas_fee']} XME")
        
        # 每处理100张表输出一次进度
        if (i + 1) % 100 == 0:
            logging.info(f"已处理 {i + 1}/1024 张表...")
    
    return {
        'total_transaction_count': total_transaction_count,
        'total_income': total_income,
        'total_gas_fee': total_gas_fee,
        'total_income_minus_gas': total_income - total_gas_fee,
        'processed_tables': processed_tables,
        'error_tables': error_tables,
        'table_results': table_results,
        'event_summary': event_summary
    }

def save_detailed_report(results, start_time, end_time, filename="xme_income_detailed_report.txt"):
    """
    保存详细报告到文件
    
    Args:
        results: 统计结果
        start_time: 开始时间
        end_time: 结束时间
        filename: 文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== XME币收入统计详细报告 ===\n")
            f.write(f"统计时间范围: {start_time} 至 {end_time} (UTC)\n")
            f.write(f"生成时间: {datetime.now(timezone.utc).isoformat()}\n\n")
            
            f.write("=== 汇总结果 ===\n")
            f.write(f"总交易笔数: {results['total_transaction_count']:,}\n")
            f.write(f"总收入金额: {results['total_income']:,.18f} XME\n")
            f.write(f"总Gas费用: {results['total_gas_fee']:,.18f} XME\n")
            f.write(f"净收入（扣除Gas费）: {results['total_income_minus_gas']:,.18f} XME\n")
            f.write(f"成功处理表数: {results['processed_tables']}/1024\n")
            f.write(f"错误表数: {len(results['error_tables'])}\n\n")
            
            # 按event_id的统计结果
            if results['event_summary']:
                f.write("=== 按Event ID统计结果 ===\n")
                # 按收入金额降序排列
                sorted_events = sorted(results['event_summary'].items(), 
                                     key=lambda x: x[1]['total_income'], reverse=True)
                
                f.write(f"{'Event ID':<15} {'交易笔数':<12} {'总收入(XME)':<25} {'Gas费(XME)':<25} {'净收入(XME)':<25} {'涉及表数':<10}\n")
                f.write("-" * 120 + "\n")
                
                for event_id, stats in sorted_events:
                    net_income = stats['total_income'] - stats['total_gas_fee']
                    f.write(f"{str(event_id):<15} "
                           f"{stats['transaction_count']:<12,} "
                           f"{stats['total_income']:<25,.6f} "
                           f"{stats['total_gas_fee']:<25,.6f} "
                           f"{net_income:<25,.6f} "
                           f"{stats['table_count']:<10}\n")
                f.write("\n")
            
            if results['error_tables']:
                f.write("=== 错误表列表 ===\n")
                for table in results['error_tables']:
                    f.write(f"- {table}\n")
                f.write("\n")
            
            f.write("=== 各表详细数据 ===\n")
            for table_result in results['table_results']:
                if table_result['transaction_count'] > 0:
                    f.write(f"{table_result['table_name']}: "
                           f"{table_result['transaction_count']} 笔, "
                           f"收入: {table_result['total_income']:,.18f} XME, "
                           f"Gas: {table_result['total_gas_fee']:,.18f} XME\n")
        
        logging.info(f"详细报告已保存到: {filename}")
    except Exception as e:
        logging.error(f"保存详细报告时出错: {str(e)}")

def send_to_lark(results, start_time, end_time):
    """
    发送统计结果到Lark群组
    
    Args:
        results: 统计结果
        start_time: 开始时间
        end_time: 结束时间
    """
    if not LARK_CONFIG['webhook_url']:
        logging.warning("Lark Webhook URL未配置，跳过发送到Lark")
        return
    
    try:
        # 格式化时间
        start_str = start_time.strftime('%Y-%m-%d %H:%M:%S UTC')
        end_str = end_time.strftime('%Y-%m-%d %H:%M:%S UTC')
        
        # 构建富文本消息
        event_details = ""
        if results['event_summary']:
            # 按收入金额降序排列，取前5名
            sorted_events = sorted(results['event_summary'].items(), 
                                 key=lambda x: x[1]['total_income'], reverse=True)
            
            event_details = "\n\n**按Event ID统计（前5名）:**\n"
            for i, (event_id, stats) in enumerate(sorted_events[:5]):
                net_income = stats['total_income'] - stats['total_gas_fee']
                event_details += f"• Event {event_id}: {stats['transaction_count']:,} 笔交易, 收入: {stats['total_income']:,.2f} XME\n"
            
            if len(sorted_events) > 5:
                event_details += f"• ... 还有 {len(sorted_events) - 5} 个event_id"
        
        # 构建消息内容
        content = f"""**📊 XME币收入统计报告**

**⏰ 统计时间范围:**
{start_str} 至 {end_str}

**📈 汇总结果:**
• 总交易笔数: {results['total_transaction_count']:,} 笔
• 总收入金额: {results['total_income']:,.2f} XME
• 总Gas费用: {results['total_gas_fee']:,.2f} XME
• 净收入（扣除Gas费）: {results['total_income_minus_gas']:,.2f} XME
• 成功处理表数: {results['processed_tables']}/1024{event_details}

📄 详细报告已保存到服务器文件"""
        
        # 构建Lark消息格式
        message = {
            "msg_type": "text",
            "content": {
                "text": content
            }
        }
        
        # 发送消息
        headers = {'Content-Type': 'application/json'}
        response = requests.post(
            LARK_CONFIG['webhook_url'], 
            headers=headers,
            data=json.dumps(message),
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            if response_data.get('code') == 0:
                logging.info("统计结果已成功发送到Lark群组")
            else:
                logging.error(f"发送到Lark失败: {response_data}")
        else:
            logging.error(f"发送到Lark失败，HTTP状态码: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        logging.error(f"发送到Lark时网络错误: {str(e)}")
    except Exception as e:
        logging.error(f"发送到Lark时出错: {str(e)}")

def send_rich_card_to_lark(results, start_time, end_time):
    """
    发送富文本卡片格式的统计结果到Lark群组
    
    Args:
        results: 统计结果
        start_time: 开始时间
        end_time: 结束时间
    """
    if not LARK_CONFIG['webhook_url']:
        logging.warning("Lark Webhook URL未配置，跳过发送到Lark")
        return
    
    try:
        # 格式化时间
        start_str = start_time.strftime('%Y-%m-%d %H:%M:%S UTC')
        end_str = end_time.strftime('%Y-%m-%d %H:%M:%S UTC')
        
        # 构建Event ID统计表格（使用markdown格式）
        event_content = ""
        if results['event_summary']:
            sorted_events = sorted(results['event_summary'].items(), 
                                 key=lambda x: x[1]['total_income'], reverse=True)
            
            # 根据图片内容添加具体的event说明
            event_descriptions = {
                8: "挖矿奖励，主力收入来源，占比约69.5%",
                34: "排行榜奖励", 
                30: "邀请奖励",
                35: "预约奖励",
                33: "每日分享奖励（金额较小，可忽略）"
            }
            
            # 构建Event ID统计内容
            event_content = "**按EventID统计**\n\n"
            
            # 只显示前5个event，按照卡片格式展示
            for event_id, stats in sorted_events[:5]:
                description = event_descriptions.get(event_id, "其他奖励")
                # 将收入金额转换为万为单位
                income_wan = float(stats['total_income']) / 10000
                
                event_content += f"**Event {event_id}** - {description}\n"
                event_content += f"• 交易笔数: {stats['transaction_count']:,} 笔\n"
                event_content += f"• 支出金额: {income_wan:.2f} 万XME\n\n"
        
        # 构建富文本卡片消息
        card = {
            "msg_type": "interactive",
            "card": {
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "tag": "lark_md",
                            "content": f"📊 **XME币支出统计报告**\n⏰ **统计时间:** {start_str} 至 {end_str}"
                        }
                    },
                    {"tag": "hr"},
                    {
                        "tag": "div",
                        "fields": [
                            {
                                "is_short": True,
                                "text": {
                                    "tag": "lark_md",
                                    "content": f"**总交易笔数**\n{results['total_transaction_count']:,} 笔"
                                }
                            },
                            {
                                "is_short": True,
                                "text": {
                                    "tag": "lark_md",
                                    "content": f"**总支出金额**\n{float(results['total_income'])/10000:.2f} 万XME"
                                }
                            },
                            {
                                "is_short": True,
                                "text": {
                                    "tag": "lark_md",
                                    "content": f"**总Gas费用**\n{float(results['total_gas_fee'])/10000:.2f} 万XME"
                                }
                            },
                            {
                                "is_short": True,
                                "text": {
                                    "tag": "lark_md",
                                    "content": f"**净支出**\n{float(results['total_income_minus_gas'])/10000:.2f} 万XME"
                                }
                            }
                        ]
                    },
                    {"tag": "hr"}
                ]
            }
        }
        
        # 如果有event统计数据，添加markdown表格
        if event_content:
            card["card"]["elements"].append({
                "tag": "div",
                "text": {
                    "tag": "lark_md",
                    "content": event_content
                }
            })
        
        # 发送消息
        headers = {'Content-Type': 'application/json'}
        response = requests.post(
            LARK_CONFIG['webhook_url'], 
            headers=headers,
            data=json.dumps(card),
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            if response_data.get('code') == 0:
                logging.info("富文本统计结果已成功发送到Lark群组")
            else:
                logging.error(f"发送富文本到Lark失败: {response_data}")
        else:
            logging.error(f"发送富文本到Lark失败，HTTP状态码: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        logging.error(f"发送富文本到Lark时网络错误: {str(e)}")
    except Exception as e:
        logging.error(f"发送富文本到Lark时出错: {str(e)}")

def main():
    """
    主函数
    
    Args:
        target_date: 目标日期，格式为 'YYYY-MM-DD'，如 '2024-01-15'
                    如果为None，则默认统计昨天的数据
    """
    target_date = '2025-06-27'
    if target_date:
        logging.info(f"开始XME币支出统计 - 指定日期: {target_date}...")
    else:
        logging.info("开始XME币支出统计 - 默认昨天数据...")
    start_time_script = datetime.now(timezone.utc)
    
    # 获取时间范围
    start_time, end_time = get_utc_time_range(target_date)
    logging.info(f"统计时间范围: {start_time} 至 {end_time} (UTC)")
    
    # 连接数据库
    conn = get_db_connection()
    if not conn:
        logging.error("无法连接数据库，退出程序")
        return
    
    try:
        # 执行统计
        results = get_all_xme_incomes(conn, start_time, end_time)
        
        # 输出汇总结果
        logging.info("=== XME币收入统计结果 ===")
        logging.info(f"统计时间范围: {start_time} 至 {end_time} (UTC)")
        logging.info(f"总交易笔数: {results['total_transaction_count']:,}")
        logging.info(f"总收入金额: {results['total_income']:,.18f} XME")
        logging.info(f"总Gas费用: {results['total_gas_fee']:,.18f} XME")
        logging.info(f"净收入（扣除Gas费）: {results['total_income_minus_gas']:,.18f} XME")
        logging.info(f"成功处理表数: {results['processed_tables']}/1024")
        
        # 输出按event_id的统计结果
        if results['event_summary']:
            logging.info("\n=== 按Event ID统计（前10名）===")
            sorted_events = sorted(results['event_summary'].items(), 
                                 key=lambda x: x[1]['total_income'], reverse=True)
            
            for i, (event_id, stats) in enumerate(sorted_events[:10]):
                net_income = stats['total_income'] - stats['total_gas_fee']
                logging.info(f"Event {event_id}: {stats['transaction_count']:,} 笔, "
                           f"收入: {stats['total_income']:,.6f} XME, "
                           f"净收入: {net_income:,.6f} XME")
            
            if len(sorted_events) > 10:
                logging.info(f"... 还有 {len(sorted_events) - 10} 个event_id（详见报告文件）")
        
        if results['error_tables']:
            logging.warning(f"处理失败的表: {len(results['error_tables'])} 张")
            logging.warning(f"失败表列表: {', '.join(results['error_tables'][:10])}" + 
                          ("..." if len(results['error_tables']) > 10 else ""))
        
        # 保存详细报告
        save_detailed_report(results, start_time, end_time)
        
        # 发送结果到Lark
        logging.info("开始发送统计结果到Lark...")
        send_rich_card_to_lark(results, start_time, end_time)
        
        # 计算脚本执行时间
        end_time_script = datetime.now(timezone.utc)
        duration = end_time_script - start_time_script
        logging.info(f"统计完成，总耗时: {duration}")
        
    except KeyboardInterrupt:
        logging.info("用户中断统计任务")
    except Exception as e:
        logging.error(f"统计任务出错: {str(e)}")
    finally:
        conn.close()
        logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 