package util

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
)

// HmacSha256 constants
const (
	HmacSha256 = "HmacSHA256"
)

// EncryptToHex 使用 HMAC-SHA256 加密并返回十六进制字符串
// data: 要加密的数据
// key: 密钥
// 返回加密后的十六进制字符串
func EncryptToHex(data, key string) (string, error) {
	hmacBytes, err := encrypt(data, key)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(hmacBytes), nil
}

// EncryptToBase64 使用 HMAC-SHA256 加密并返回 Base64 字符串
// data: 要加密的数据
// key: 密钥
// 返回加密后的 Base64 字符串
func EncryptToBase64(data, key string) (string, error) {
	hmacBytes, err := encrypt(data, key)
	if err != nil {
		return "", err
	}
	
	// 标准的base64编码
	// standardBase64String := base64.StdEncoding.EncodeToString(hmacBytes)
	
	// URL-safe, 无 "+"、"/"，并去掉 "=" 填充
	// urlSafeBase64 := base64.RawURLEncoding.EncodeToString(hmacBytes)
	
	// 无填充模式RawURLEncoding，跟端上对齐
	return base64.RawURLEncoding.EncodeToString(hmacBytes), nil
}

// encrypt 使用 HMAC-SHA256 加密
// data: 要加密的数据
// key: 密钥
// 返回加密后的字节数组
func encrypt(data, key string) ([]byte, error) {
	h := hmac.New(sha256.New, []byte(key))
	_, err := h.Write([]byte(data))
	if err != nil {
		return nil, err
	}
	return h.Sum(nil), nil
}

// 以下是测试代码，可以取消注释进行测试
/*
func main() {
	data := "Hello, HMAC-SHA256!"
	key := "secret-key"

	hexResult, err := EncryptToHex(data, key)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	
	base64Result, err := EncryptToBase64(data, key)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	fmt.Println("Hex:", hexResult)
	fmt.Println("Base64:", base64Result)
}
*/
