-- 修改字段定义和更新数据 - push_message表 (256-511)
-- 生成时间: 2025-06-24 11:21:20.958265

-- 修改 created_time 字段默认值 (256-511)
ALTER TABLE media_message.push_message256 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message257 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message258 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message259 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message260 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message261 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message262 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message263 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message264 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message265 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message266 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message267 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message268 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message269 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message270 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message271 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message272 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message273 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message274 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message275 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message276 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message277 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message278 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message279 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message280 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message281 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message282 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message283 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message284 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message285 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message286 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message287 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message288 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message289 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message290 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message291 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message292 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message293 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message294 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message295 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message296 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message297 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message298 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message299 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message300 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message301 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message302 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message303 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message304 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message305 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message306 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message307 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message308 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message309 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message310 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message311 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message312 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message313 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message314 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message315 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message316 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message317 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message318 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message319 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message320 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message321 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message322 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message323 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message324 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message325 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message326 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message327 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message328 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message329 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message330 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message331 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message332 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message333 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message334 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message335 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message336 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message337 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message338 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message339 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message340 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message341 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message342 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message343 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message344 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message345 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message346 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message347 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message348 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message349 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message350 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message351 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message352 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message353 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message354 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message355 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message356 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message357 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message358 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message359 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message360 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message361 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message362 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message363 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message364 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message365 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message366 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message367 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message368 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message369 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message370 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message371 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message372 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message373 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message374 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message375 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message376 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message377 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message378 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message379 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message380 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message381 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message382 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message383 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message384 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message385 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message386 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message387 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message388 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message389 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message390 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message391 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message392 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message393 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message394 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message395 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message396 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message397 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message398 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message399 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message400 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message401 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message402 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message403 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message404 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message405 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message406 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message407 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message408 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message409 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message410 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message411 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message412 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message413 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message414 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message415 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message416 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message417 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message418 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message419 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message420 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message421 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message422 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message423 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message424 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message425 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message426 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message427 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message428 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message429 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message430 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message431 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message432 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message433 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message434 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message435 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message436 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message437 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message438 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message439 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message440 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message441 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message442 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message443 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message444 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message445 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message446 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message447 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message448 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message449 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message450 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message451 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message452 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message453 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message454 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message455 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message456 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message457 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message458 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message459 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message460 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message461 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message462 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message463 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message464 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message465 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message466 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message467 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message468 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message469 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message470 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message471 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message472 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message473 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message474 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message475 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message476 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message477 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message478 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message479 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message480 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message481 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message482 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message483 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message484 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message485 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message486 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message487 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message488 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message489 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message490 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message491 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message492 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message493 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message494 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message495 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message496 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message497 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message498 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message499 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message500 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message501 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message502 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message503 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message504 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message505 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message506 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message507 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message508 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message509 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message510 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message511 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 修改 updated_time 字段默认值 (256-511)
ALTER TABLE media_message.push_message256 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message257 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message258 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message259 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message260 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message261 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message262 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message263 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message264 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message265 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message266 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message267 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message268 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message269 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message270 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message271 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message272 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message273 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message274 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message275 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message276 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message277 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message278 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message279 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message280 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message281 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message282 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message283 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message284 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message285 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message286 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message287 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message288 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message289 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message290 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message291 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message292 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message293 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message294 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message295 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message296 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message297 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message298 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message299 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message300 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message301 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message302 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message303 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message304 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message305 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message306 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message307 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message308 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message309 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message310 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message311 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message312 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message313 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message314 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message315 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message316 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message317 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message318 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message319 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message320 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message321 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message322 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message323 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message324 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message325 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message326 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message327 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message328 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message329 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message330 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message331 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message332 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message333 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message334 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message335 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message336 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message337 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message338 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message339 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message340 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message341 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message342 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message343 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message344 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message345 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message346 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message347 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message348 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message349 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message350 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message351 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message352 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message353 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message354 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message355 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message356 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message357 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message358 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message359 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message360 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message361 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message362 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message363 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message364 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message365 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message366 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message367 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message368 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message369 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message370 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message371 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message372 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message373 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message374 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message375 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message376 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message377 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message378 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message379 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message380 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message381 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message382 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message383 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message384 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message385 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message386 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message387 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message388 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message389 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message390 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message391 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message392 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message393 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message394 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message395 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message396 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message397 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message398 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message399 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message400 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message401 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message402 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message403 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message404 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message405 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message406 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message407 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message408 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message409 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message410 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message411 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message412 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message413 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message414 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message415 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message416 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message417 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message418 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message419 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message420 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message421 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message422 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message423 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message424 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message425 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message426 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message427 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message428 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message429 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message430 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message431 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message432 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message433 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message434 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message435 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message436 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message437 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message438 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message439 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message440 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message441 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message442 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message443 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message444 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message445 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message446 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message447 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message448 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message449 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message450 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message451 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message452 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message453 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message454 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message455 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message456 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message457 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message458 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message459 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message460 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message461 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message462 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message463 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message464 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message465 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message466 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message467 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message468 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message469 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message470 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message471 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message472 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message473 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message474 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message475 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message476 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message477 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message478 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message479 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message480 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message481 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message482 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message483 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message484 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message485 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message486 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message487 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message488 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message489 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message490 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message491 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message492 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message493 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message494 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message495 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message496 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message497 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message498 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message499 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message500 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message501 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message502 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message503 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message504 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message505 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message506 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message507 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message508 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message509 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message510 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message511 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 更新空值数据 (256-511)
UPDATE media_message.push_message256 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message257 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message258 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message259 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message260 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message261 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message262 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message263 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message264 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message265 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message266 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message267 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message268 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message269 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message270 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message271 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message272 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message273 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message274 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message275 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message276 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message277 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message278 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message279 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message280 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message281 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message282 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message283 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message284 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message285 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message286 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message287 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message288 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message289 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message290 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message291 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message292 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message293 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message294 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message295 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message296 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message297 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message298 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message299 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message300 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message301 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message302 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message303 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message304 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message305 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message306 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message307 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message308 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message309 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message310 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message311 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message312 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message313 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message314 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message315 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message316 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message317 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message318 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message319 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message320 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message321 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message322 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message323 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message324 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message325 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message326 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message327 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message328 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message329 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message330 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message331 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message332 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message333 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message334 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message335 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message336 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message337 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message338 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message339 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message340 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message341 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message342 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message343 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message344 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message345 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message346 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message347 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message348 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message349 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message350 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message351 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message352 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message353 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message354 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message355 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message356 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message357 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message358 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message359 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message360 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message361 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message362 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message363 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message364 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message365 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message366 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message367 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message368 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message369 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message370 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message371 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message372 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message373 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message374 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message375 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message376 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message377 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message378 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message379 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message380 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message381 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message382 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message383 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message384 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message385 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message386 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message387 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message388 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message389 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message390 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message391 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message392 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message393 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message394 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message395 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message396 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message397 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message398 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message399 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message400 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message401 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message402 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message403 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message404 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message405 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message406 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message407 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message408 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message409 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message410 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message411 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message412 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message413 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message414 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message415 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message416 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message417 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message418 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message419 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message420 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message421 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message422 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message423 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message424 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message425 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message426 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message427 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message428 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message429 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message430 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message431 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message432 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message433 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message434 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message435 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message436 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message437 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message438 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message439 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message440 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message441 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message442 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message443 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message444 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message445 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message446 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message447 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message448 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message449 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message450 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message451 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message452 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message453 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message454 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message455 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message456 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message457 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message458 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message459 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message460 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message461 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message462 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message463 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message464 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message465 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message466 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message467 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message468 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message469 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message470 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message471 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message472 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message473 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message474 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message475 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message476 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message477 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message478 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message479 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message480 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message481 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message482 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message483 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message484 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message485 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message486 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message487 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message488 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message489 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message490 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message491 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message492 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message493 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message494 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message495 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message496 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message497 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message498 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message499 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message500 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message501 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message502 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message503 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message504 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message505 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message506 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message507 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message508 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message509 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message510 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message511 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;