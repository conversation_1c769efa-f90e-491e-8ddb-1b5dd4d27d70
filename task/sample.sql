CREATE TABLE `user_login_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` bigint NOT NULL,
  `login_ip` varchar(64) DEFAULT NULL COMMENT '登录ip',
  `login_time` varchar(10) NOT NULL COMMENT '登录日期(yyyy-mm-dd)',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `login_type` varchar(100) DEFAULT NULL COMMENT '登录平台(platform, bee, google)',
  `source_type` bigint DEFAULT NULL COMMENT '用户设备来源(ANDROID，IOS， WEB)',
  `country_code` varchar(10) DEFAULT NULL COMMENT '国家码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_idx_uid_day` (`uid`,`login_time`),
  KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=4093821 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户登录日志表'; 