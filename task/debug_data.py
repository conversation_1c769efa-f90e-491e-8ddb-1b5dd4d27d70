#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本 - 检查数据库中的实际数据情况
"""

import pymysql
from datetime import datetime, timedelta

def check_data():
    print("🔍 检查数据库中的实际数据...")
    print("=" * 60)
    
    # 连接数据库
    try:
        connection = pymysql.connect(
            host="xme-prod-rds-user-instance-1.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com",
            database="media_user",
            user="pro-user-user",
            password="VcEVqaE5HX",
            port=3306,
            charset='utf8mb4'
        )
        print("✅ 数据库连接成功!")
        
        cursor = connection.cursor()
        
        # 1. 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'user_login_log'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ 表 'user_login_log' 不存在!")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print("📋 数据库中存在的表:")
            for table in tables:
                print(f"  - {table[0]}")
            return
        
        print("✅ 表 'user_login_log' 存在")
        
        # 2. 检查表结构
        cursor.execute("DESCRIBE user_login_log")
        columns = cursor.fetchall()
        print("\n📋 表结构:")
        for col in columns:
            print(f"  {col[0]} - {col[1]}")
        
        # 3. 检查数据总量
        cursor.execute("SELECT COUNT(*) FROM user_login_log")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 总记录数: {total_count:,}")
        
        if total_count == 0:
            print("❌ 表中没有任何数据!")
            return
        
        # 4. 检查最近的数据
        cursor.execute("SELECT MAX(login_time), MIN(login_time) FROM user_login_log")
        date_range = cursor.fetchone()
        print(f"📅 数据时间范围: {date_range[1]} 到 {date_range[0]}")
        
        # 5. 检查最近7天的数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        cursor.execute("""
            SELECT COUNT(*) FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
        """, (start_date, end_date))
        recent_count = cursor.fetchone()[0]
        print(f"📊 最近7天记录数 ({start_date} 到 {end_date}): {recent_count:,}")
        
        # 6. 检查source_type分布
        cursor.execute("""
            SELECT source_type, COUNT(*) as count
            FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
                AND source_type IS NOT NULL
            GROUP BY source_type
            ORDER BY count DESC
        """, (start_date, end_date))
        source_stats = cursor.fetchall()
        
        print(f"\n📱 最近7天source_type分布:")
        source_mapping = {
            0: 'ANDROID (APK)',
            1: 'IOS', 
            2: 'WEB',
            3: 'CONTENT',
            4: 'ANDROID-GP (Google Play)',
            5: 'UNKNOWN'
        }
        
        for row in source_stats:
            source_type, count = row
            source_name = source_mapping.get(source_type, f"未知({source_type})")
            print(f"  {source_name}: {count:,} 条记录")
        
        # 7. 检查login_type分布
        cursor.execute("""
            SELECT login_type, COUNT(*) as count
            FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s
                AND login_type IS NOT NULL
            GROUP BY login_type
            ORDER BY count DESC
        """, (start_date, end_date))
        platform_stats = cursor.fetchall()
        
        print(f"\n🎯 最近7天login_type分布:")
        for row in platform_stats:
            login_type, count = row
            print(f"  {login_type}: {count:,} 条记录")
        
        # 8. 专门检查APK和Google Play数据
        print(f"\n🔍 APK vs Google Play 数据检查:")
        
        # APK用户 (source_type = 0)
        cursor.execute("""
            SELECT COUNT(*) FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s AND source_type = 0
        """, (start_date, end_date))
        apk_count = cursor.fetchone()[0]
        print(f"  APK用户 (source_type=0): {apk_count:,} 条记录")
        
        # Google Play用户 (source_type = 4)
        cursor.execute("""
            SELECT COUNT(*) FROM user_login_log 
            WHERE login_time >= %s AND login_time <= %s AND source_type = 4
        """, (start_date, end_date))
        gp_count = cursor.fetchone()[0]
        print(f"  Google Play用户 (source_type=4): {gp_count:,} 条记录")
        
        # 9. 检查样本数据
        if apk_count > 0:
            cursor.execute("""
                SELECT uid, login_type, source_type, country_code, login_time 
                FROM user_login_log 
                WHERE login_time >= %s AND login_time <= %s AND source_type = 0
                LIMIT 5
            """, (start_date, end_date))
            apk_samples = cursor.fetchall()
            print(f"\n📋 APK用户样本数据 (前5条):")
            for row in apk_samples:
                print(f"  UID: {row[0]}, 平台: {row[1]}, 来源: {row[2]}, 国家: {row[3]}, 时间: {row[4]}")
        
        if gp_count > 0:
            cursor.execute("""
                SELECT uid, login_type, source_type, country_code, login_time 
                FROM user_login_log 
                WHERE login_time >= %s AND login_time <= %s AND source_type = 4
                LIMIT 5
            """, (start_date, end_date))
            gp_samples = cursor.fetchall()
            print(f"\n📋 Google Play用户样本数据 (前5条):")
            for row in gp_samples:
                print(f"  UID: {row[0]}, 平台: {row[1]}, 来源: {row[2]}, 国家: {row[3]}, 时间: {row[4]}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_data() 