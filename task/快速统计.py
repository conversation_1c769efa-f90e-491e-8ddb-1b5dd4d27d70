#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图文混排快速统计脚本
可以直接分析给定的数据而无需数据库连接
"""

import json
from collections import Counter

def parse_media_data(media_data_str):
    """解析media_data字段"""
    if not media_data_str or media_data_str.strip() == '':
        return []
    
    try:
        media_data = json.loads(media_data_str)
        if not isinstance(media_data, list):
            return []
        return media_data
    except (json.JSONDecodeError, TypeError):
        return []

def parse_segments_data(segments_data_str):
    """解析segments_data字段"""
    if not segments_data_str or segments_data_str.strip() == '':
        return []
    
    try:
        segments_data = json.loads(segments_data_str)
        if not isinstance(segments_data, list):
            return []
        return segments_data
    except (json.JSONDecodeError, TypeError):
        return []

def analyze_single_post(content, segments_data, media_data):
    """分析单个帖子"""
    # 解析数据
    media_list = parse_media_data(media_data)
    segments_list = parse_segments_data(segments_data)
    
    # 媒体分析
    media_count = {'images': 0, 'videos': 0, 'total': 0}
    media_types = set()
    
    for media in media_list:
        media_type = media.get('type')
        if media_type == 1:  # 图片
            media_types.add('image')
            media_count['images'] += 1
        elif media_type == 2:  # 视频
            media_types.add('video')
            media_count['videos'] += 1
        media_count['total'] += 1
    
    # 文本分析
    text_length = len(content.strip()) if content else 0
    has_text = bool(content and content.strip())
    
    # segments分析
    text_segments = sum(1 for s in segments_list if s.get('type') == 'text')
    topic_segments = sum(1 for s in segments_list if s.get('type') == 'topic')
    other_segments = len(segments_list) - text_segments - topic_segments
    
    # 判定混排类型
    has_image = 'image' in media_types
    has_video = 'video' in media_types
    
    if not has_text and not (has_image or has_video):
        mixed_type = '纯文本或空内容'
    elif has_text and not (has_image or has_video):
        mixed_type = '纯文本'
    elif not has_text and has_image and not has_video:
        mixed_type = '纯图片'
    elif not has_text and has_video and not has_image:
        mixed_type = '纯视频'
    elif not has_text and has_image and has_video:
        mixed_type = '图片+视频（无文字）'
    elif has_text and has_image and not has_video:
        mixed_type = '图片+文字混排'
    elif has_text and has_video and not has_image:
        mixed_type = '视频+文字混排'
    elif has_text and has_image and has_video:
        mixed_type = '图片+视频+文字混排'
    else:
        mixed_type = '其他类型'
    
    return {
        'content': content,
        'text_length': text_length,
        'has_text': has_text,
        'media_count': media_count,
        'segments': {
            'text': text_segments,
            'topic': topic_segments,
            'other': other_segments,
            'total': len(segments_list)
        },
        'mixed_type': mixed_type,
        'is_mixed_content': len(media_types) > 0 and has_text
    }

def quick_stats(posts_data):
    """快速统计多个帖子"""
    stats = {
        'total_posts': len(posts_data),
        'mixed_type_distribution': Counter(),
        'media_count_distribution': Counter(),
        'text_length_distribution': Counter(),
        'mixed_content_count': 0
    }
    
    print(f"📊 开始分析 {len(posts_data)} 个帖子...")
    
    for i, post in enumerate(posts_data):
        analysis = analyze_single_post(
            post.get('content', ''),
            post.get('segments_data', ''),
            post.get('media_data', '')
        )
        
        # 统计混排类型
        stats['mixed_type_distribution'][analysis['mixed_type']] += 1
        
        # 统计媒体数量
        total_media = analysis['media_count']['total']
        if total_media > 0:
            if total_media <= 1:
                range_key = '1个媒体文件'
            elif total_media <= 3:
                range_key = '2-3个媒体文件'
            elif total_media <= 5:
                range_key = '4-5个媒体文件'
            else:
                range_key = '6个以上媒体文件'
            stats['media_count_distribution'][range_key] += 1
        
        # 统计文字长度
        text_length = analysis['text_length']
        if text_length == 0:
            length_range = '无文字'
        elif text_length <= 20:
            length_range = '1-20字符'
        elif text_length <= 50:
            length_range = '21-50字符'
        elif text_length <= 100:
            length_range = '51-100字符'
        else:
            length_range = '100字符以上'
        stats['text_length_distribution'][length_range] += 1
        
        # 统计混排内容
        if analysis['is_mixed_content']:
            stats['mixed_content_count'] += 1
        
        # 每100个显示进度
        if (i + 1) % 100 == 0:
            print(f"  已处理 {i + 1}/{len(posts_data)} 个帖子")
    
    return stats

def print_stats(stats):
    """打印统计结果"""
    print("\n" + "="*60)
    print("📈 图文混排统计报告")
    print("="*60)
    
    print(f"\n📊 总体概况:")
    print(f"  总帖子数: {stats['total_posts']:,}")
    print(f"  混排内容数: {stats['mixed_content_count']:,}")
    print(f"  混排比例: {(stats['mixed_content_count'] / stats['total_posts'] * 100):.2f}%")
    
    print(f"\n🎯 混排类型分布:")
    for mixed_type, count in stats['mixed_type_distribution'].most_common():
        percentage = (count / stats['total_posts']) * 100
        print(f"  {mixed_type}: {count:,} ({percentage:.2f}%)")
    
    print(f"\n📱 媒体文件数量分布:")
    for range_key, count in stats['media_count_distribution'].most_common():
        percentage = (count / stats['total_posts']) * 100
        print(f"  {range_key}: {count:,} ({percentage:.2f}%)")
    
    print(f"\n📝 文字长度分布:")
    for length_range, count in stats['text_length_distribution'].most_common():
        percentage = (count / stats['total_posts']) * 100
        print(f"  {length_range}: {count:,} ({percentage:.2f}%)")

def main():
    """主函数 - 示例用法"""
    print("🚀 图文混排快速统计工具")
    print("="*60)
    
    # 示例数据
    sample_posts = [
        {
            'content': "what 's the man",
            'segments_data': '[{"type":"text","id":0,"value":"what \'s the man"},{"type":"topic","id":38,"value":"今日穿搭"}]',
            'media_data': '[{"type":1,"media":"image1.jpg","sort":1},{"type":1,"media":"image2.jpg","sort":2}]'
        },
        {
            'content': '这是纯文本内容',
            'segments_data': '[{"type":"text","id":0,"value":"这是纯文本内容"}]',
            'media_data': '[]'
        },
        {
            'content': '',
            'segments_data': '[]',
            'media_data': '[{"type":1,"media":"image1.jpg","sort":1}]'
        },
        {
            'content': '视频分享',
            'segments_data': '[{"type":"text","id":0,"value":"视频分享"}]',
            'media_data': '[{"type":2,"media":"video1.mp4","sort":1}]'
        },
        {
            'content': '多媒体内容',
            'segments_data': '[{"type":"text","id":0,"value":"多媒体内容"},{"type":"topic","id":1,"value":"测试"}]',
            'media_data': '[{"type":1,"media":"image1.jpg","sort":1},{"type":2,"media":"video1.mp4","sort":2}]'
        }
    ]
    
    # 运行统计
    stats = quick_stats(sample_posts)
    print_stats(stats)
    
    print("\n💡 使用说明:")
    print("  1. 准备数据格式: [{'content': '文字', 'segments_data': 'JSON', 'media_data': 'JSON'}, ...]")
    print("  2. 调用: quick_stats(your_posts_data)")
    print("  3. 查看: print_stats(stats)")

# 单个帖子快速分析函数
def analyze_post(content, segments_data, media_data):
    """快速分析单个帖子"""
    analysis = analyze_single_post(content, segments_data, media_data)
    
    print(f"\n📄 帖子分析结果:")
    print(f"  内容: '{content}'")
    print(f"  文字长度: {analysis['text_length']} 字符")
    print(f"  媒体: {analysis['media_count']['images']}图片, {analysis['media_count']['videos']}视频")
    print(f"  片段: {analysis['segments']['text']}文本, {analysis['segments']['topic']}话题")
    print(f"  类型: {analysis['mixed_type']}")
    print(f"  混排: {'是' if analysis['is_mixed_content'] else '否'}")
    
    return analysis

if __name__ == "__main__":
    main() 