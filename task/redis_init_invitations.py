#!/usr/bin/env python3
import pymysql
import logging
import redis
from datetime import datetime
from redis.cluster import RedisCluster

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("redis_init_invitations.log"),
        logging.StreamHandler()
    ]
)


# 媒体用户数据库配置
USER_DB = {
    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    'user': 'envtest-user',
    'password': 'k7xetf5YKB',
    'database': 'media_user'
}

def get_db_config(**kwargs):
    """
    Helper function to prepare database configuration.
    Returns the database configuration dictionary with cursor settings.

    Args:
        **kwargs: Database connection parameters (host, user, password, database)

    Returns:
        dict: Database configuration with cursor settings
    """
    # Create a copy of the input config
    config = dict(kwargs)

    # Add cursor class configuration for dictionary results
    config['cursorclass'] = pymysql.cursors.DictCursor

    # Add other common configuration parameters if needed
    config['charset'] = 'utf8mb4'
    config['autocommit'] = False

    return config

def get_redis_connection():
    """
    Get Redis connection instance

    Returns:
        redis.Redis: Redis connection instance
    """
    try:
        # Redis connection parameters
        host = "xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com"
        port = 6379
        
        # Create RedisCluster connection
        # Using the host and port directly instead of startup_nodes
        redis_conn = RedisCluster(
            host=host,
            port=port,
            decode_responses=True,
            ssl=False
        )
        
        redis_conn.ping()
        logging.info("Connected to Redis cluster successfully")
        return redis_conn
    except Exception as e:
        logging.error(f"Error connecting to Redis: {str(e)}")
        return None

def get_eligible_users():
    """
    Fetch eligible users for invitation data from database

    Returns:
        list: List of eligible users with invitation data
    """
    # Connect to database
    user_db_config = get_db_config(**USER_DB)

    try:
        conn = pymysql.connect(**user_db_config)
        eligible_users = []

        try:
            with conn.cursor() as cursor:
                # Query to get invitation data
                # Adjust this query based on your actual table structure
                query = """
                SELECT DISTINCT from_uid, to_uid,created_time
                FROM user_invite_face_record
                WHERE face_type = 2
                AND from_uid IS NOT NULL
                """
                cursor.execute(query)
                eligible_users = cursor.fetchall()
                logging.info(f"Found {len(eligible_users)} eligible users for Redis initialization")
        finally:
            conn.close()

        return eligible_users
    except Exception as e:
        logging.error(f"Error fetching eligible users: {str(e)}")
        return []

def initialize_redis_invitations():
    """
    Initialize Redis with invitation data
    """
    # Get eligible users
    eligible_users = get_eligible_users()

    if not eligible_users:
        logging.info("No eligible users found. Exiting.")
        return

    # Connect to Redis
    redis_conn = get_redis_connection()
    if redis_conn:
        try:
            # Initialize counter for successful entries
            success_count = 0

            for user in eligible_users:
                inviter_uid = user['from_uid']
                invited_uid = user['to_uid']

                # Convert timestamp to 13 digits (milliseconds precision)
                if isinstance(user['created_time'], datetime):
                    # Get seconds timestamp and multiply by 1000 to get milliseconds
                    timestamp = int(user['created_time'].timestamp() * 1000)
                else:
                    # Use current time in milliseconds
                    timestamp = int(datetime.now().timestamp() * 1000)

                # Store in Redis: key is friend:expedition:end:time:%s, value is invited_uid, score is timestamp
                redis_key = f"friend:expedition:end:time:{inviter_uid}"
                redis_conn.zadd(redis_key, {invited_uid: timestamp})
                redis_invite_key = "invite:user:%s" %invited_uid
                redis_conn.set(redis_invite_key, inviter_uid)
                success_count += 1

                # Log every 100 entries to avoid excessive logging
                if success_count % 100 == 0:
                    logging.info(f"Processed {success_count}/{len(eligible_users)} invitation entries")

            logging.info(f"Successfully stored {success_count} invitation entries in Redis")

            # Optional: Verify some data was stored correctly
            sample_size = min(5, len(eligible_users))
            logging.info(f"Verifying sample of {sample_size} entries:")

            for i in range(sample_size):
                user = eligible_users[i]
                inviter_uid = user['from_uid']
                invited_uid = user['to_uid']
                redis_key = f"friend:expedition:end:time:{inviter_uid}"

                # Get the score (timestamp) for this invited_uid
                score = redis_conn.zscore(redis_key, invited_uid)
                if score:
                    logging.info(f"Verified: {inviter_uid} invited {invited_uid} at timestamp {int(score)}")
                else:
                    logging.warning(f"Verification failed for {inviter_uid} invited {invited_uid}")

        except Exception as e:
            logging.error(f"Error storing invitation data in Redis: {str(e)}")

def main():
    """
    Main function to run the Redis initialization
    """
    logging.info("Starting Redis invitation data initialization")
    initialize_redis_invitations()
    logging.info("Redis initialization process completed")

if __name__ == "__main__":
    main()
