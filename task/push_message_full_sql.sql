-- 修改字段定义和更新数据 - push_message表 (0-1023)
-- 生成时间: 2025-06-24 11:21:20.956893

-- 修改 created_time 字段默认值
ALTER TABLE media_message.push_message0 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message2 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message3 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message4 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message5 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message6 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message7 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message8 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message9 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message10 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message11 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message12 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message13 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message14 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message15 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message16 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message17 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message18 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message19 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message20 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message21 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message22 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message23 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message24 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message25 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message26 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message27 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message28 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message29 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message30 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message31 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message32 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message33 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message34 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message35 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message36 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message37 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message38 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message39 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message40 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message41 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message42 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message43 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message44 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message45 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message46 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message47 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message48 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message49 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message50 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message51 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message52 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message53 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message54 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message55 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message56 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message57 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message58 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message59 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message60 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message61 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message62 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message63 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message64 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message65 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message66 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message67 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message68 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message69 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message70 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message71 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message72 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message73 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message74 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message75 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message76 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message77 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message78 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message79 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message80 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message81 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message82 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message83 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message84 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message85 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message86 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message87 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message88 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message89 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message90 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message91 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message92 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message93 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message94 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message95 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message96 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message97 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message98 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message99 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message100 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message101 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message102 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message103 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message104 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message105 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message106 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message107 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message108 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message109 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message110 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message111 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message112 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message113 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message114 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message115 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message116 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message117 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message118 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message119 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message120 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message121 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message122 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message123 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message124 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message125 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message126 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message127 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message128 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message129 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message130 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message131 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message132 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message133 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message134 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message135 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message136 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message137 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message138 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message139 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message140 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message141 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message142 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message143 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message144 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message145 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message146 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message147 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message148 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message149 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message150 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message151 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message152 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message153 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message154 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message155 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message156 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message157 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message158 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message159 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message160 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message161 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message162 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message163 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message164 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message165 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message166 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message167 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message168 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message169 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message170 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message171 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message172 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message173 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message174 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message175 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message176 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message177 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message178 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message179 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message180 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message181 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message182 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message183 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message184 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message185 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message186 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message187 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message188 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message189 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message190 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message191 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message192 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message193 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message194 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message195 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message196 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message197 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message198 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message199 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message200 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message201 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message202 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message203 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message204 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message205 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message206 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message207 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message208 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message209 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message210 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message211 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message212 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message213 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message214 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message215 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message216 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message217 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message218 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message219 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message220 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message221 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message222 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message223 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message224 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message225 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message226 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message227 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message228 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message229 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message230 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message231 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message232 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message233 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message234 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message235 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message236 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message237 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message238 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message239 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message240 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message241 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message242 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message243 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message244 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message245 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message246 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message247 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message248 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message249 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message250 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message251 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message252 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message253 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message254 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message255 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message256 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message257 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message258 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message259 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message260 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message261 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message262 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message263 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message264 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message265 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message266 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message267 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message268 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message269 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message270 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message271 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message272 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message273 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message274 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message275 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message276 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message277 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message278 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message279 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message280 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message281 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message282 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message283 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message284 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message285 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message286 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message287 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message288 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message289 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message290 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message291 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message292 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message293 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message294 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message295 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message296 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message297 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message298 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message299 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message300 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message301 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message302 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message303 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message304 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message305 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message306 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message307 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message308 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message309 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message310 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message311 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message312 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message313 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message314 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message315 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message316 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message317 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message318 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message319 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message320 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message321 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message322 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message323 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message324 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message325 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message326 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message327 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message328 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message329 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message330 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message331 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message332 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message333 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message334 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message335 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message336 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message337 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message338 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message339 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message340 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message341 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message342 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message343 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message344 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message345 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message346 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message347 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message348 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message349 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message350 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message351 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message352 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message353 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message354 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message355 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message356 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message357 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message358 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message359 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message360 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message361 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message362 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message363 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message364 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message365 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message366 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message367 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message368 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message369 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message370 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message371 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message372 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message373 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message374 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message375 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message376 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message377 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message378 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message379 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message380 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message381 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message382 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message383 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message384 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message385 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message386 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message387 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message388 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message389 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message390 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message391 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message392 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message393 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message394 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message395 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message396 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message397 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message398 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message399 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message400 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message401 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message402 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message403 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message404 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message405 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message406 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message407 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message408 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message409 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message410 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message411 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message412 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message413 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message414 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message415 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message416 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message417 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message418 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message419 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message420 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message421 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message422 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message423 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message424 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message425 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message426 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message427 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message428 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message429 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message430 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message431 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message432 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message433 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message434 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message435 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message436 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message437 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message438 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message439 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message440 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message441 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message442 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message443 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message444 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message445 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message446 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message447 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message448 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message449 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message450 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message451 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message452 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message453 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message454 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message455 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message456 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message457 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message458 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message459 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message460 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message461 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message462 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message463 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message464 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message465 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message466 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message467 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message468 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message469 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message470 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message471 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message472 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message473 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message474 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message475 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message476 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message477 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message478 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message479 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message480 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message481 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message482 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message483 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message484 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message485 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message486 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message487 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message488 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message489 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message490 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message491 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message492 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message493 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message494 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message495 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message496 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message497 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message498 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message499 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message500 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message501 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message502 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message503 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message504 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message505 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message506 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message507 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message508 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message509 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message510 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message511 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message512 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message513 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message514 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message515 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message516 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message517 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message518 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message519 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message520 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message521 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message522 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message523 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message524 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message525 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message526 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message527 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message528 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message529 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message530 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message531 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message532 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message533 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message534 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message535 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message536 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message537 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message538 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message539 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message540 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message541 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message542 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message543 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message544 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message545 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message546 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message547 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message548 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message549 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message550 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message551 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message552 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message553 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message554 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message555 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message556 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message557 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message558 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message559 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message560 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message561 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message562 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message563 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message564 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message565 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message566 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message567 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message568 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message569 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message570 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message571 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message572 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message573 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message574 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message575 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message576 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message577 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message578 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message579 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message580 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message581 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message582 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message583 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message584 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message585 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message586 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message587 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message588 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message589 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message590 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message591 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message592 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message593 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message594 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message595 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message596 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message597 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message598 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message599 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message600 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message601 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message602 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message603 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message604 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message605 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message606 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message607 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message608 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message609 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message610 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message611 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message612 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message613 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message614 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message615 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message616 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message617 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message618 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message619 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message620 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message621 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message622 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message623 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message624 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message625 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message626 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message627 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message628 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message629 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message630 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message631 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message632 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message633 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message634 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message635 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message636 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message637 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message638 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message639 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message640 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message641 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message642 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message643 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message644 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message645 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message646 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message647 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message648 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message649 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message650 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message651 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message652 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message653 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message654 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message655 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message656 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message657 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message658 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message659 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message660 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message661 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message662 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message663 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message664 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message665 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message666 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message667 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message668 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message669 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message670 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message671 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message672 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message673 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message674 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message675 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message676 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message677 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message678 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message679 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message680 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message681 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message682 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message683 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message684 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message685 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message686 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message687 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message688 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message689 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message690 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message691 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message692 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message693 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message694 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message695 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message696 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message697 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message698 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message699 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message700 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message701 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message702 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message703 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message704 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message705 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message706 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message707 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message708 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message709 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message710 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message711 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message712 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message713 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message714 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message715 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message716 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message717 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message718 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message719 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message720 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message721 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message722 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message723 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message724 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message725 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message726 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message727 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message728 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message729 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message730 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message731 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message732 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message733 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message734 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message735 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message736 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message737 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message738 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message739 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message740 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message741 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message742 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message743 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message744 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message745 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message746 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message747 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message748 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message749 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message750 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message751 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message752 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message753 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message754 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message755 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message756 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message757 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message758 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message759 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message760 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message761 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message762 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message763 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message764 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message765 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message766 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message767 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message768 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message769 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message770 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message771 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message772 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message773 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message774 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message775 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message776 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message777 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message778 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message779 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message780 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message781 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message782 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message783 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message784 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message785 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message786 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message787 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message788 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message789 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message790 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message791 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message792 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message793 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message794 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message795 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message796 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message797 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message798 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message799 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message800 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message801 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message802 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message803 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message804 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message805 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message806 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message807 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message808 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message809 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message810 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message811 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message812 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message813 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message814 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message815 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message816 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message817 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message818 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message819 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message820 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message821 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message822 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message823 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message824 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message825 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message826 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message827 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message828 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message829 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message830 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message831 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message832 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message833 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message834 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message835 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message836 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message837 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message838 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message839 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message840 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message841 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message842 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message843 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message844 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message845 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message846 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message847 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message848 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message849 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message850 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message851 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message852 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message853 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message854 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message855 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message856 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message857 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message858 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message859 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message860 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message861 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message862 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message863 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message864 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message865 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message866 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message867 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message868 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message869 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message870 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message871 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message872 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message873 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message874 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message875 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message876 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message877 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message878 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message879 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message880 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message881 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message882 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message883 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message884 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message885 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message886 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message887 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message888 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message889 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message890 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message891 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message892 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message893 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message894 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message895 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message896 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message897 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message898 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message899 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message900 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message901 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message902 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message903 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message904 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message905 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message906 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message907 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message908 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message909 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message910 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message911 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message912 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message913 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message914 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message915 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message916 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message917 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message918 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message919 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message920 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message921 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message922 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message923 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message924 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message925 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message926 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message927 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message928 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message929 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message930 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message931 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message932 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message933 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message934 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message935 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message936 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message937 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message938 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message939 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message940 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message941 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message942 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message943 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message944 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message945 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message946 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message947 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message948 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message949 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message950 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message951 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message952 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message953 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message954 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message955 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message956 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message957 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message958 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message959 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message960 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message961 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message962 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message963 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message964 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message965 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message966 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message967 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message968 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message969 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message970 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message971 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message972 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message973 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message974 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message975 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message976 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message977 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message978 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message979 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message980 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message981 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message982 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message983 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message984 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message985 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message986 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message987 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message988 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message989 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message990 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message991 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message992 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message993 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message994 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message995 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message996 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message997 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message998 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message999 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1000 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1001 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1002 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1003 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1004 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1005 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1006 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1007 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1008 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1009 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1010 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1011 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1012 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1013 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1014 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1015 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1016 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1017 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1018 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1019 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1020 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1021 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1022 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE media_message.push_message1023 MODIFY `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 修改 updated_time 字段默认值
ALTER TABLE media_message.push_message0 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message2 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message3 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message4 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message5 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message6 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message7 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message8 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message9 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message10 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message11 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message12 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message13 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message14 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message15 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message16 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message17 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message18 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message19 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message20 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message21 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message22 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message23 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message24 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message25 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message26 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message27 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message28 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message29 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message30 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message31 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message32 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message33 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message34 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message35 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message36 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message37 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message38 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message39 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message40 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message41 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message42 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message43 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message44 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message45 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message46 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message47 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message48 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message49 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message50 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message51 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message52 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message53 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message54 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message55 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message56 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message57 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message58 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message59 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message60 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message61 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message62 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message63 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message64 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message65 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message66 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message67 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message68 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message69 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message70 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message71 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message72 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message73 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message74 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message75 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message76 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message77 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message78 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message79 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message80 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message81 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message82 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message83 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message84 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message85 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message86 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message87 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message88 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message89 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message90 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message91 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message92 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message93 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message94 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message95 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message96 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message97 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message98 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message99 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message100 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message101 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message102 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message103 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message104 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message105 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message106 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message107 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message108 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message109 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message110 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message111 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message112 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message113 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message114 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message115 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message116 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message117 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message118 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message119 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message120 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message121 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message122 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message123 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message124 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message125 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message126 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message127 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message128 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message129 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message130 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message131 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message132 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message133 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message134 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message135 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message136 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message137 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message138 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message139 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message140 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message141 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message142 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message143 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message144 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message145 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message146 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message147 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message148 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message149 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message150 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message151 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message152 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message153 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message154 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message155 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message156 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message157 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message158 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message159 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message160 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message161 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message162 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message163 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message164 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message165 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message166 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message167 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message168 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message169 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message170 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message171 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message172 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message173 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message174 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message175 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message176 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message177 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message178 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message179 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message180 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message181 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message182 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message183 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message184 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message185 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message186 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message187 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message188 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message189 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message190 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message191 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message192 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message193 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message194 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message195 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message196 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message197 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message198 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message199 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message200 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message201 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message202 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message203 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message204 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message205 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message206 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message207 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message208 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message209 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message210 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message211 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message212 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message213 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message214 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message215 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message216 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message217 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message218 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message219 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message220 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message221 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message222 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message223 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message224 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message225 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message226 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message227 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message228 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message229 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message230 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message231 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message232 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message233 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message234 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message235 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message236 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message237 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message238 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message239 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message240 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message241 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message242 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message243 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message244 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message245 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message246 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message247 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message248 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message249 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message250 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message251 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message252 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message253 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message254 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message255 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message256 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message257 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message258 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message259 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message260 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message261 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message262 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message263 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message264 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message265 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message266 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message267 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message268 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message269 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message270 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message271 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message272 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message273 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message274 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message275 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message276 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message277 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message278 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message279 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message280 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message281 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message282 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message283 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message284 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message285 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message286 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message287 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message288 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message289 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message290 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message291 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message292 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message293 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message294 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message295 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message296 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message297 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message298 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message299 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message300 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message301 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message302 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message303 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message304 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message305 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message306 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message307 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message308 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message309 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message310 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message311 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message312 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message313 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message314 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message315 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message316 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message317 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message318 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message319 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message320 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message321 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message322 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message323 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message324 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message325 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message326 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message327 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message328 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message329 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message330 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message331 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message332 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message333 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message334 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message335 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message336 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message337 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message338 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message339 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message340 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message341 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message342 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message343 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message344 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message345 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message346 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message347 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message348 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message349 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message350 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message351 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message352 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message353 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message354 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message355 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message356 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message357 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message358 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message359 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message360 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message361 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message362 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message363 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message364 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message365 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message366 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message367 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message368 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message369 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message370 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message371 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message372 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message373 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message374 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message375 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message376 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message377 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message378 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message379 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message380 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message381 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message382 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message383 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message384 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message385 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message386 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message387 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message388 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message389 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message390 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message391 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message392 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message393 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message394 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message395 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message396 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message397 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message398 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message399 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message400 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message401 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message402 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message403 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message404 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message405 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message406 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message407 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message408 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message409 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message410 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message411 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message412 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message413 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message414 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message415 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message416 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message417 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message418 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message419 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message420 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message421 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message422 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message423 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message424 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message425 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message426 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message427 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message428 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message429 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message430 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message431 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message432 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message433 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message434 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message435 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message436 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message437 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message438 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message439 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message440 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message441 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message442 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message443 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message444 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message445 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message446 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message447 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message448 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message449 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message450 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message451 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message452 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message453 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message454 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message455 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message456 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message457 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message458 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message459 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message460 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message461 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message462 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message463 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message464 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message465 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message466 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message467 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message468 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message469 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message470 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message471 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message472 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message473 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message474 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message475 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message476 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message477 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message478 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message479 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message480 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message481 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message482 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message483 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message484 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message485 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message486 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message487 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message488 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message489 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message490 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message491 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message492 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message493 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message494 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message495 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message496 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message497 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message498 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message499 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message500 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message501 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message502 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message503 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message504 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message505 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message506 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message507 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message508 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message509 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message510 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message511 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message512 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message513 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message514 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message515 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message516 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message517 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message518 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message519 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message520 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message521 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message522 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message523 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message524 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message525 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message526 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message527 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message528 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message529 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message530 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message531 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message532 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message533 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message534 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message535 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message536 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message537 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message538 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message539 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message540 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message541 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message542 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message543 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message544 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message545 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message546 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message547 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message548 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message549 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message550 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message551 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message552 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message553 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message554 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message555 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message556 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message557 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message558 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message559 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message560 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message561 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message562 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message563 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message564 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message565 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message566 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message567 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message568 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message569 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message570 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message571 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message572 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message573 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message574 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message575 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message576 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message577 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message578 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message579 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message580 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message581 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message582 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message583 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message584 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message585 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message586 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message587 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message588 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message589 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message590 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message591 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message592 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message593 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message594 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message595 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message596 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message597 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message598 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message599 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message600 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message601 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message602 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message603 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message604 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message605 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message606 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message607 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message608 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message609 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message610 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message611 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message612 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message613 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message614 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message615 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message616 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message617 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message618 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message619 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message620 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message621 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message622 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message623 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message624 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message625 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message626 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message627 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message628 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message629 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message630 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message631 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message632 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message633 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message634 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message635 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message636 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message637 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message638 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message639 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message640 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message641 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message642 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message643 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message644 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message645 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message646 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message647 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message648 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message649 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message650 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message651 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message652 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message653 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message654 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message655 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message656 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message657 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message658 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message659 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message660 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message661 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message662 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message663 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message664 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message665 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message666 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message667 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message668 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message669 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message670 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message671 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message672 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message673 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message674 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message675 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message676 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message677 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message678 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message679 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message680 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message681 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message682 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message683 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message684 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message685 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message686 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message687 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message688 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message689 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message690 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message691 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message692 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message693 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message694 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message695 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message696 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message697 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message698 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message699 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message700 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message701 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message702 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message703 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message704 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message705 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message706 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message707 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message708 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message709 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message710 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message711 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message712 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message713 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message714 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message715 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message716 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message717 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message718 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message719 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message720 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message721 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message722 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message723 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message724 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message725 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message726 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message727 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message728 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message729 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message730 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message731 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message732 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message733 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message734 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message735 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message736 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message737 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message738 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message739 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message740 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message741 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message742 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message743 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message744 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message745 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message746 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message747 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message748 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message749 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message750 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message751 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message752 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message753 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message754 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message755 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message756 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message757 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message758 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message759 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message760 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message761 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message762 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message763 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message764 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message765 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message766 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message767 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message768 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message769 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message770 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message771 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message772 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message773 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message774 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message775 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message776 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message777 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message778 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message779 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message780 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message781 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message782 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message783 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message784 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message785 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message786 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message787 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message788 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message789 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message790 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message791 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message792 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message793 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message794 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message795 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message796 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message797 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message798 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message799 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message800 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message801 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message802 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message803 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message804 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message805 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message806 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message807 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message808 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message809 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message810 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message811 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message812 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message813 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message814 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message815 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message816 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message817 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message818 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message819 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message820 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message821 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message822 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message823 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message824 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message825 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message826 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message827 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message828 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message829 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message830 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message831 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message832 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message833 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message834 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message835 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message836 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message837 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message838 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message839 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message840 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message841 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message842 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message843 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message844 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message845 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message846 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message847 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message848 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message849 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message850 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message851 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message852 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message853 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message854 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message855 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message856 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message857 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message858 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message859 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message860 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message861 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message862 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message863 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message864 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message865 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message866 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message867 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message868 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message869 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message870 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message871 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message872 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message873 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message874 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message875 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message876 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message877 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message878 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message879 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message880 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message881 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message882 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message883 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message884 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message885 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message886 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message887 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message888 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message889 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message890 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message891 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message892 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message893 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message894 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message895 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message896 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message897 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message898 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message899 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message900 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message901 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message902 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message903 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message904 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message905 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message906 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message907 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message908 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message909 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message910 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message911 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message912 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message913 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message914 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message915 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message916 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message917 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message918 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message919 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message920 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message921 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message922 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message923 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message924 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message925 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message926 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message927 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message928 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message929 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message930 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message931 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message932 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message933 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message934 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message935 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message936 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message937 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message938 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message939 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message940 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message941 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message942 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message943 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message944 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message945 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message946 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message947 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message948 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message949 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message950 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message951 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message952 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message953 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message954 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message955 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message956 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message957 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message958 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message959 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message960 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message961 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message962 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message963 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message964 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message965 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message966 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message967 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message968 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message969 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message970 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message971 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message972 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message973 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message974 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message975 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message976 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message977 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message978 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message979 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message980 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message981 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message982 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message983 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message984 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message985 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message986 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message987 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message988 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message989 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message990 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message991 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message992 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message993 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message994 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message995 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message996 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message997 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message998 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message999 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1000 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1001 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1002 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1003 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1004 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1005 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1006 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1007 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1008 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1009 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1010 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1011 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1012 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1013 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1014 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1015 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1016 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1017 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1018 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1019 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1020 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1021 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1022 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE media_message.push_message1023 MODIFY `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 更新空值数据
UPDATE media_message.push_message0 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message2 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message3 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message4 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message5 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message6 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message7 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message8 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message9 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message10 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message11 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message12 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message13 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message14 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message15 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message16 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message17 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message18 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message19 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message20 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message21 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message22 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message23 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message24 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message25 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message26 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message27 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message28 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message29 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message30 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message31 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message32 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message33 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message34 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message35 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message36 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message37 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message38 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message39 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message40 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message41 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message42 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message43 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message44 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message45 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message46 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message47 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message48 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message49 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message50 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message51 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message52 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message53 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message54 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message55 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message56 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message57 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message58 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message59 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message60 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message61 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message62 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message63 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message64 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message65 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message66 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message67 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message68 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message69 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message70 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message71 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message72 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message73 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message74 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message75 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message76 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message77 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message78 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message79 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message80 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message81 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message82 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message83 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message84 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message85 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message86 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message87 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message88 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message89 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message90 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message91 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message92 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message93 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message94 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message95 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message96 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message97 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message98 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message99 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message100 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message101 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message102 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message103 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message104 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message105 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message106 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message107 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message108 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message109 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message110 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message111 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message112 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message113 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message114 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message115 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message116 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message117 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message118 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message119 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message120 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message121 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message122 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message123 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message124 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message125 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message126 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message127 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message128 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message129 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message130 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message131 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message132 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message133 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message134 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message135 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message136 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message137 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message138 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message139 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message140 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message141 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message142 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message143 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message144 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message145 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message146 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message147 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message148 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message149 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message150 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message151 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message152 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message153 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message154 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message155 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message156 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message157 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message158 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message159 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message160 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message161 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message162 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message163 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message164 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message165 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message166 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message167 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message168 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message169 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message170 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message171 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message172 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message173 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message174 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message175 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message176 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message177 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message178 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message179 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message180 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message181 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message182 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message183 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message184 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message185 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message186 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message187 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message188 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message189 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message190 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message191 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message192 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message193 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message194 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message195 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message196 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message197 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message198 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message199 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message200 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message201 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message202 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message203 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message204 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message205 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message206 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message207 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message208 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message209 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message210 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message211 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message212 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message213 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message214 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message215 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message216 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message217 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message218 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message219 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message220 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message221 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message222 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message223 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message224 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message225 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message226 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message227 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message228 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message229 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message230 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message231 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message232 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message233 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message234 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message235 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message236 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message237 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message238 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message239 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message240 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message241 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message242 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message243 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message244 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message245 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message246 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message247 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message248 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message249 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message250 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message251 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message252 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message253 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message254 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message255 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message256 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message257 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message258 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message259 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message260 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message261 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message262 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message263 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message264 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message265 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message266 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message267 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message268 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message269 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message270 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message271 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message272 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message273 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message274 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message275 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message276 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message277 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message278 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message279 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message280 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message281 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message282 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message283 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message284 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message285 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message286 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message287 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message288 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message289 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message290 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message291 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message292 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message293 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message294 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message295 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message296 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message297 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message298 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message299 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message300 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message301 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message302 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message303 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message304 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message305 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message306 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message307 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message308 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message309 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message310 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message311 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message312 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message313 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message314 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message315 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message316 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message317 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message318 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message319 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message320 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message321 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message322 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message323 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message324 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message325 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message326 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message327 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message328 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message329 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message330 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message331 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message332 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message333 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message334 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message335 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message336 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message337 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message338 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message339 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message340 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message341 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message342 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message343 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message344 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message345 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message346 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message347 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message348 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message349 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message350 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message351 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message352 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message353 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message354 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message355 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message356 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message357 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message358 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message359 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message360 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message361 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message362 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message363 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message364 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message365 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message366 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message367 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message368 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message369 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message370 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message371 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message372 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message373 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message374 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message375 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message376 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message377 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message378 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message379 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message380 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message381 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message382 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message383 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message384 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message385 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message386 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message387 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message388 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message389 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message390 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message391 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message392 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message393 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message394 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message395 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message396 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message397 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message398 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message399 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message400 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message401 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message402 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message403 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message404 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message405 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message406 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message407 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message408 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message409 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message410 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message411 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message412 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message413 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message414 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message415 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message416 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message417 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message418 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message419 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message420 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message421 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message422 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message423 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message424 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message425 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message426 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message427 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message428 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message429 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message430 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message431 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message432 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message433 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message434 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message435 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message436 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message437 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message438 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message439 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message440 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message441 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message442 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message443 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message444 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message445 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message446 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message447 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message448 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message449 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message450 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message451 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message452 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message453 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message454 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message455 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message456 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message457 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message458 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message459 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message460 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message461 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message462 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message463 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message464 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message465 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message466 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message467 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message468 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message469 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message470 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message471 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message472 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message473 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message474 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message475 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message476 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message477 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message478 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message479 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message480 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message481 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message482 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message483 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message484 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message485 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message486 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message487 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message488 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message489 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message490 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message491 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message492 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message493 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message494 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message495 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message496 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message497 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message498 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message499 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message500 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message501 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message502 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message503 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message504 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message505 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message506 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message507 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message508 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message509 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message510 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message511 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message512 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message513 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message514 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message515 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message516 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message517 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message518 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message519 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message520 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message521 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message522 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message523 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message524 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message525 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message526 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message527 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message528 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message529 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message530 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message531 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message532 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message533 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message534 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message535 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message536 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message537 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message538 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message539 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message540 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message541 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message542 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message543 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message544 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message545 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message546 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message547 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message548 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message549 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message550 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message551 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message552 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message553 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message554 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message555 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message556 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message557 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message558 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message559 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message560 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message561 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message562 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message563 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message564 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message565 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message566 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message567 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message568 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message569 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message570 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message571 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message572 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message573 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message574 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message575 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message576 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message577 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message578 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message579 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message580 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message581 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message582 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message583 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message584 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message585 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message586 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message587 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message588 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message589 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message590 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message591 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message592 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message593 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message594 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message595 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message596 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message597 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message598 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message599 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message600 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message601 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message602 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message603 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message604 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message605 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message606 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message607 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message608 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message609 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message610 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message611 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message612 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message613 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message614 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message615 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message616 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message617 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message618 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message619 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message620 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message621 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message622 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message623 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message624 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message625 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message626 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message627 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message628 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message629 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message630 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message631 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message632 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message633 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message634 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message635 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message636 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message637 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message638 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message639 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message640 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message641 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message642 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message643 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message644 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message645 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message646 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message647 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message648 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message649 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message650 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message651 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message652 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message653 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message654 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message655 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message656 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message657 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message658 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message659 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message660 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message661 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message662 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message663 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message664 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message665 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message666 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message667 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message668 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message669 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message670 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message671 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message672 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message673 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message674 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message675 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message676 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message677 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message678 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message679 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message680 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message681 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message682 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message683 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message684 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message685 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message686 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message687 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message688 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message689 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message690 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message691 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message692 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message693 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message694 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message695 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message696 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message697 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message698 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message699 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message700 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message701 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message702 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message703 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message704 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message705 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message706 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message707 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message708 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message709 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message710 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message711 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message712 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message713 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message714 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message715 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message716 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message717 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message718 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message719 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message720 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message721 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message722 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message723 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message724 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message725 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message726 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message727 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message728 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message729 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message730 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message731 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message732 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message733 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message734 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message735 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message736 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message737 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message738 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message739 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message740 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message741 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message742 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message743 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message744 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message745 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message746 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message747 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message748 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message749 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message750 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message751 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message752 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message753 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message754 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message755 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message756 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message757 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message758 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message759 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message760 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message761 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message762 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message763 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message764 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message765 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message766 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message767 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message768 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message769 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message770 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message771 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message772 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message773 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message774 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message775 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message776 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message777 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message778 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message779 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message780 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message781 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message782 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message783 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message784 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message785 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message786 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message787 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message788 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message789 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message790 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message791 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message792 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message793 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message794 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message795 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message796 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message797 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message798 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message799 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message800 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message801 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message802 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message803 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message804 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message805 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message806 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message807 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message808 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message809 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message810 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message811 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message812 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message813 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message814 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message815 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message816 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message817 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message818 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message819 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message820 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message821 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message822 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message823 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message824 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message825 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message826 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message827 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message828 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message829 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message830 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message831 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message832 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message833 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message834 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message835 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message836 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message837 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message838 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message839 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message840 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message841 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message842 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message843 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message844 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message845 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message846 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message847 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message848 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message849 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message850 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message851 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message852 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message853 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message854 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message855 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message856 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message857 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message858 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message859 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message860 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message861 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message862 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message863 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message864 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message865 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message866 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message867 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message868 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message869 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message870 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message871 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message872 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message873 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message874 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message875 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message876 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message877 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message878 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message879 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message880 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message881 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message882 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message883 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message884 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message885 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message886 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message887 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message888 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message889 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message890 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message891 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message892 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message893 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message894 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message895 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message896 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message897 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message898 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message899 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message900 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message901 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message902 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message903 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message904 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message905 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message906 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message907 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message908 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message909 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message910 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message911 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message912 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message913 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message914 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message915 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message916 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message917 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message918 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message919 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message920 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message921 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message922 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message923 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message924 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message925 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message926 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message927 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message928 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message929 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message930 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message931 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message932 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message933 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message934 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message935 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message936 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message937 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message938 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message939 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message940 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message941 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message942 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message943 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message944 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message945 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message946 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message947 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message948 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message949 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message950 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message951 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message952 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message953 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message954 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message955 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message956 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message957 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message958 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message959 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message960 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message961 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message962 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message963 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message964 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message965 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message966 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message967 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message968 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message969 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message970 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message971 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message972 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message973 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message974 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message975 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message976 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message977 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message978 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message979 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message980 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message981 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message982 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message983 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message984 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message985 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message986 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message987 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message988 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message989 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message990 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message991 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message992 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message993 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message994 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message995 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message996 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message997 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message998 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message999 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1000 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1001 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1002 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1003 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1004 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1005 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1006 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1007 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1008 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1009 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1010 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1011 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1012 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1013 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1014 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1015 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1016 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1017 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1018 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1019 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1020 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1021 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1022 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;
UPDATE media_message.push_message1023 SET created_time = NOW(), updated_time = NOW() WHERE created_time IS NULL AND updated_time IS NULL;

-- 注意：此脚本包含0-1023共1024个表的修改，实际执行时请确保所有表都存在
-- 建议分批执行，避免数据库压力过大
-- 可以按照以下方式分批执行：
-- 第1批：0-255
-- 第2批：256-511
-- 第3批：512-767
-- 第4批：768-1023