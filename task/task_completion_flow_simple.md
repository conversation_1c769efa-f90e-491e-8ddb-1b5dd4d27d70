# 任务完成流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant TaskService as 任务服务
    participant DB as 数据库

    User->>TaskService: 提交任务完成请求
    TaskService->>DB: 验证任务条件
    
    alt 任务条件未满足
        TaskService-->>User: 返回任务未完成错误
    else 任务条件已满足
        TaskService->>DB: 开始事务
        TaskService->>DB: 更新user_task_completions表
        TaskService->>DB: 写入energy_points_records表
        
        alt points_type == "base"
            TaskService->>DB: 更新user_base_energy表
        else points_type == "hourly"
            TaskService->>DB: 更新user_hourly_energy表
        end
        TaskService->>DB: 提交事务
        TaskService-->>User: 返回任务完成成功
    end

    Note over User,DB: 用户领取任务奖励流程类似，\n区别在于验证领取条件而不是完成条件
```

## 数据流转说明

1. **Base类型任务**：
   - 更新`user_task_completions`
   - 写入`energy_points_records`
   - 更新`user_base_energy`

2. **Hourly类型任务**：
   - 更新`user_task_completions`
   - 写入`energy_points_records`
   - 更新`user_hourly_energy`

所有操作在同一个事务中完成，确保数据一致性。
