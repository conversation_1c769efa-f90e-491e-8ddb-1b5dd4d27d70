# 电报群组加入任务流程

## 流程概述

电报群组加入任务是XME挖矿系统中的一种社交互动任务，要求用户加入项目的官方Telegram群组以获取能量点奖励。

## 详细流程设计

### 1. 用户发起加入任务
- 客户端展示加入电报群组任务和奖励说明
- 用户点击"加入官方Telegram群组"按钮

### 2. 跳转到官方群组
- 客户端获取官方Telegram群组信息：`GET /task/v1/social/official-groups`
- 客户端打开Telegram应用或网页，定位到官方群组页面
- 用户在Telegram上完成加入群组操作

### 3. 生成唯一验证码
- 客户端发送：`POST /task/v1/telegram/generate-code`
- 服务端生成唯一验证码并关联到当前登录用户
- 服务端返回验证码和加入指引
- 客户端展示验证码和加入指引给用户

### 4. 用户在Telegram群组中验证
- 用户加入Telegram群组
- 用户在群组中发送命令：`/verify 验证码`
- 群组中的机器人接收验证命令
- 机器人验证码是否有效并记录用户的Telegram信息
- 机器人向服务端发送验证成功通知：`POST /task/v1/telegram/webhook`
- 服务端标记用户验证成功

### 5. 检查验证状态
- 客户端定期轮询：`GET /task/v1/telegram/verify-status`
- 请求参数：`{task_id}`
- 服务端返回验证状态
- 如果验证成功，客户端显示成功提示

### 6. 任务完成与奖励
- 服务端验证加入成功后自动标记任务完成
- 计算并发放能量点奖励
- 客户端显示任务完成和奖励信息

## API接口设计

### 1. 获取官方群组信息接口

**请求**：
```
GET /task/v1/social/official-groups
Content-Type: application/json

{
  "platform": "telegram"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "telegram_group_name": "Official Group",
    "telegram_group_link": "https://t.me/official_group",
    "telegram_bot_username": "@official_bot"
  },
  "success": true
}
```

### 2. 生成验证码接口

**请求**：
```
POST /task/v1/telegram/generate-code
Content-Type: application/json

{
  "task_id": "任务ID"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "verification_code": "ABC123",
    "expires_in": 3600,
    "instructions": "请在Telegram群组中发送: /verify ABC123"
  },
  "success": true
}
```

### 3. Telegram机器人回调接口

**请求**：
```
POST /task/v1/telegram/webhook
Content-Type: application/json

{
  "verification_code": "ABC123",
  "telegram_user_id": 12345678,
  "telegram_username": "user_telegram",
  "telegram_first_name": "User",
  "telegram_last_name": "Name",
  "verified_at": "2025-05-30T02:20:52+08:00"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "processed": true
  },
  "success": true
}
```

### 4. 检查验证状态接口

**请求**：
```
GET /task/v1/telegram/verify-status
Content-Type: application/json

{
  "task_id": "任务ID"
}
```

**响应**：
```
{
  "code": 200,
  "message": "success",
  "result": {
    "verified": true,
    "telegram_username": "user_telegram",
    "verification_time": "2025-05-30T02:20:52+08:00"
  },
  "success": true
}
```
