#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import requests
import concurrent.futures
import time
import logging
from urllib.parse import urlparse
import os
from typing import List, Tuple, Optional
import csv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cover_url_check.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CoverURLChecker:
    def __init__(self, db_config: dict):
        """初始化数据库连接配置"""
        self.db_config = db_config
        self.session = requests.Session()
        self.session.timeout = 10
        
    def get_cover_urls(self, limit: int = None, offset: int = 0) -> List[Tuple]:
        """从数据库获取cover_url数据"""
        connection = None
        try:
            connection = pymysql.connect(**self.db_config)
            with connection.cursor() as cursor:
                # 查询非空的cover_url
                sql = """
                SELECT id, post_id, cover_url, type, status 
                FROM post_medias 
                WHERE cover_url IS NOT NULL 
                AND cover_url != '' 
                AND is_del = 0
                ORDER BY created_at DESC
                """
                
                if limit:
                    sql += f" LIMIT {limit} OFFSET {offset}"
                
                cursor.execute(sql)
                results = cursor.fetchall()
                logger.info(f"从数据库获取到 {len(results)} 条记录")
                return results
                
        except Exception as e:
            logger.error(f"数据库查询失败: {e}")
            return []
        finally:
            if connection:
                connection.close()
    
    def check_url_exists(self, url: str) -> Tuple[bool, int, str]:
        """检查单个URL是否存在"""
        try:
            # 发送HEAD请求检查文件是否存在
            response = self.session.head(url, allow_redirects=True)
            status_code = response.status_code
            
            if status_code == 200:
                return True, status_code, "OK"
            elif status_code == 404:
                return False, status_code, "Not Found"
            elif status_code == 403:
                return False, status_code, "Forbidden"
            else:
                return False, status_code, f"HTTP {status_code}"
                
        except requests.exceptions.Timeout:
            return False, 0, "Timeout"
        except requests.exceptions.ConnectionError:
            return False, 0, "Connection Error"
        except requests.exceptions.RequestException as e:
            return False, 0, f"Request Error: {str(e)}"
        except Exception as e:
            return False, 0, f"Unknown Error: {str(e)}"
    
    def check_urls_batch(self, urls_data: List[Tuple], max_workers: int = 10) -> List[dict]:
        """批量检查URL是否存在"""
        results = []
        
        def check_single_record(record):
            record_id, post_id, cover_url, media_type, status = record
            exists, status_code, message = self.check_url_exists(cover_url)
            
            result = {
                'id': record_id,
                'post_id': post_id,
                'cover_url': cover_url,
                'type': media_type,
                'status': status,
                'url_exists': exists,
                'status_code': status_code,
                'message': message
            }
            
            if not exists:
                logger.warning(f"URL不存在: {cover_url} (ID: {record_id})")
            
            return result
        
        # 使用线程池并发检查
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_record = {
                executor.submit(check_single_record, record): record 
                for record in urls_data
            }
            
            for i, future in enumerate(concurrent.futures.as_completed(future_to_record)):
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 显示进度
                    if (i + 1) % 100 == 0:
                        logger.info(f"已检查 {i + 1}/{len(urls_data)} 个URL")
                        
                except Exception as e:
                    record = future_to_record[future]
                    logger.error(f"检查记录失败 {record[0]}: {e}")
        
        return results
    
    def save_results_to_csv(self, results: List[dict], filename: str = "cover_url_check_results.csv"):
        """保存结果到CSV文件"""
        if not results:
            return
            
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['id', 'post_id', 'cover_url', 'type', 'status', 'url_exists', 'status_code', 'message']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in results:
                writer.writerow(result)
        
        logger.info(f"结果已保存到 {filename}")
    
    def update_invalid_urls(self, invalid_records: List[dict]):
        """更新数据库中无效的URL记录"""
        if not invalid_records:
            logger.info("没有需要更新的无效URL记录")
            return
        
        connection = None
        try:
            connection = pymysql.connect(**self.db_config)
            with connection.cursor() as cursor:
                # 批量更新状态为失败
                invalid_ids = [str(record['id']) for record in invalid_records]
                sql = f"""
                UPDATE post_medias 
                SET status = 3, updated_at = NOW() 
                WHERE id IN ({','.join(invalid_ids)})
                """
                
                affected_rows = cursor.execute(sql)
                connection.commit()
                logger.info(f"已更新 {affected_rows} 条无效URL记录的状态")
                
        except Exception as e:
            logger.error(f"更新数据库失败: {e}")
            if connection:
                connection.rollback()
        finally:
            if connection:
                connection.close()
    
    def generate_report(self, results: List[dict]) -> dict:
        """生成检查报告"""
        total = len(results)
        valid_count = sum(1 for r in results if r['url_exists'])
        invalid_count = total - valid_count
        
        # 按错误类型统计
        error_stats = {}
        for result in results:
            if not result['url_exists']:
                message = result['message']
                error_stats[message] = error_stats.get(message, 0) + 1
        
        report = {
            'total_checked': total,
            'valid_urls': valid_count,
            'invalid_urls': invalid_count,
            'valid_percentage': (valid_count / total * 100) if total > 0 else 0,
            'error_breakdown': error_stats
        }
        
        return report
    
    def run_check(self, limit: int = None, batch_size: int = 1000, max_workers: int = 10, 
                  save_to_csv: bool = True, update_db: bool = False):
        """运行完整的检查流程"""
        logger.info("开始检查cover_url...")
        start_time = time.time()
        
        all_results = []
        offset = 0
        
        while True:
            # 分批获取数据
            current_limit = min(batch_size, limit - offset) if limit else batch_size
            urls_data = self.get_cover_urls(limit=current_limit, offset=offset)
            
            if not urls_data:
                break
            
            logger.info(f"检查第 {offset//batch_size + 1} 批数据，共 {len(urls_data)} 条记录")
            
            # 检查当前批次
            batch_results = self.check_urls_batch(urls_data, max_workers)
            all_results.extend(batch_results)
            
            offset += len(urls_data)
            
            # 如果达到限制或获取的数据少于批次大小，则停止
            if limit and offset >= limit:
                break
            if len(urls_data) < batch_size:
                break
        
        # 生成报告
        report = self.generate_report(all_results)
        
        logger.info("=" * 50)
        logger.info("检查完成！报告如下：")
        logger.info(f"总共检查: {report['total_checked']} 个URL")
        logger.info(f"有效URL: {report['valid_urls']} 个")
        logger.info(f"无效URL: {report['invalid_urls']} 个")
        logger.info(f"有效率: {report['valid_percentage']:.2f}%")
        logger.info(f"耗时: {time.time() - start_time:.2f} 秒")
        
        if report['error_breakdown']:
            logger.info("错误类型统计:")
            for error_type, count in report['error_breakdown'].items():
                logger.info(f"  {error_type}: {count} 个")
        
        # 保存结果到CSV
        if save_to_csv:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"cover_url_check_results_{timestamp}.csv"
            self.save_results_to_csv(all_results, filename)
        
        # 更新数据库中的无效记录
        if update_db:
            invalid_records = [r for r in all_results if not r['url_exists']]
            self.update_invalid_urls(invalid_records)
        
        return all_results, report

def main():
    # 数据库配置
    DB_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'your_username',
        'password': 'your_password',
        'database': 'your_database',
        'charset': 'utf8mb4'
    }
    
    # 创建检查器
    checker = CoverURLChecker(DB_CONFIG)
    
    # 运行检查
    # limit: 限制检查的记录数，None表示检查所有
    # batch_size: 每批处理的记录数
    # max_workers: 并发线程数
    # save_to_csv: 是否保存结果到CSV
    # update_db: 是否更新数据库中的无效记录
    results, report = checker.run_check(
        limit=10000,        # 检查1万条记录
        batch_size=1000,    # 每批1000条
        max_workers=20,     # 20个并发线程
        save_to_csv=True,   # 保存结果
        update_db=False     # 不更新数据库（可以先查看结果再决定是否更新）
    )

if __name__ == "__main__":
    main() 