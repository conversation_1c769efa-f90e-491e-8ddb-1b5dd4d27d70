#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评论内容垃圾检测脚本
从MySQL数据库读取评论数据，调用API检测垃圾内容，并导出到Excel文件
"""

import pymysql
import requests
import pandas as pd
import json
import time
from datetime import datetime
import logging
from typing import List, Dict, Any
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comment_filter.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CommentFilter:
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化评论过滤器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.api_url = "http://api.x.me/textshield/api/v1/spam/detect"
        self.spam_comments = []
        
    def get_database_connection(self):
        """
        获取数据库连接
        """
        try:
            connection = pymysql.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def fetch_comments(self, batch_size: int = 1000) -> List[Dict[str, Any]]:
        """
        从数据库获取需要检测的评论数据
        
        Args:
            batch_size: 批次大小
            
        Returns:
            评论数据列表
        """
        connection = None
        try:
            connection = self.get_database_connection()
            
            with connection.cursor() as cursor:
                # 查询source_type不是1的数据
                sql = """
                SELECT id, comment_id, content_id, user_id, content, type
                FROM comment_audit_record 
                WHERE source_type != 1 AND deleted = 0
                ORDER BY id
                """
                
                cursor.execute(sql)
                results = cursor.fetchall()
                
                logger.info(f"从数据库获取到 {len(results)} 条评论数据")
                return results
                
        except Exception as e:
            logger.error(f"获取评论数据失败: {e}")
            raise
        finally:
            if connection:
                connection.close()
    
    def detect_spam(self, text: str) -> Dict[str, Any]:
        """
        调用API检测垃圾内容
        
        Args:
            text: 要检测的文本内容
            
        Returns:
            API响应结果
        """
        payload = {
            "text": text,
            "check_sensitive": True,
            "use_multilingual": True
        }
        
        try:
            response = requests.post(
                self.api_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"API请求失败，状态码: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求异常: {e}")
            return None
    
    def process_comments(self, comments: List[Dict[str, Any]]) -> None:
        """
        处理评论数据，检测垃圾内容
        
        Args:
            comments: 评论数据列表
        """
        total_count = len(comments)
        spam_count = 0
        
        logger.info(f"开始处理 {total_count} 条评论")
        
        for i, comment in enumerate(comments, 1):
            try:
                content = comment['content']
                if not content or not content.strip():
                    continue
                
                # 调用API检测
                result = self.detect_spam(content)
                
                if result and result.get('success'):
                    result_data = result.get('result', {})
                    is_spam = result_data.get('is_spam', False)
                    has_sensitive = result_data.get('has_sensitive', False)
                    
                    # 如果检测到垃圾内容或敏感内容
                    if is_spam or has_sensitive:
                        spam_info = {
                            'content_id': comment['content_id'],
                            'comment_id': comment['comment_id'],
                            'content': content,
                            'user_id': comment['user_id'],
                            'type': comment['type'],
                            'is_spam': is_spam,
                            'has_sensitive': has_sensitive,
                            'confidence': result_data.get('confidence', 0),
                            'risk_level': result_data.get('risk_level', ''),
                            'content_type': result_data.get('content_type', ''),
                            'detected_patterns': ', '.join(result_data.get('detected_patterns', [])),
                            'sensitive_words': ', '.join(result_data.get('sensitive_words', [])),
                            'sensitive_count': result_data.get('sensitive_count', 0),
                            'max_level': result_data.get('max_level', 0)
                        }
                        
                        self.spam_comments.append(spam_info)
                        spam_count += 1
                        
                        logger.info(f"发现垃圾内容 [{spam_count}]: comment_id={comment['comment_id']}, is_spam={is_spam}, has_sensitive={has_sensitive}")
                
                # 进度显示
                if i % 100 == 0:
                    logger.info(f"处理进度: {i}/{total_count} ({i/total_count*100:.1f}%), 发现垃圾内容: {spam_count}")
                
                # 避免请求过于频繁
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"处理评论失败 comment_id={comment.get('comment_id')}: {e}")
                continue
        
        logger.info(f"处理完成！总共处理: {total_count}, 发现垃圾内容: {spam_count}")
    
    def export_to_excel(self, filename: str = None) -> str:
        """
        导出垃圾评论到Excel文件
        
        Args:
            filename: 输出文件名
            
        Returns:
            导出的文件路径
        """
        if not self.spam_comments:
            logger.warning("没有发现垃圾评论，无需导出")
            return None
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"spam_comments_{timestamp}.xlsx"
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(self.spam_comments)
            
            # 重新排列列的顺序
            columns_order = [
                'content_id', 'comment_id', 'content', 'user_id', 'type',
                'is_spam', 'has_sensitive', 'confidence', 'risk_level', 'content_type',
                'detected_patterns', 'sensitive_words', 'sensitive_count', 'max_level'
            ]
            
            df = df[columns_order]
            
            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='垃圾评论', index=False)
                
                # 调整列宽
                worksheet = writer.sheets['垃圾评论']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"垃圾评论已导出到: {filename}")
            logger.info(f"导出记录数: {len(self.spam_comments)}")
            
            return filename
            
        except Exception as e:
            logger.error(f"导出Excel失败: {e}")
            raise
    
    def run(self, output_filename: str = None) -> str:
        """
        运行完整的过滤流程
        
        Args:
            output_filename: 输出文件名
            
        Returns:
            导出的文件路径
        """
        logger.info("开始评论垃圾内容检测")
        
        # 1. 获取评论数据
        comments = self.fetch_comments()
        
        if not comments:
            logger.warning("没有找到需要处理的评论数据")
            return None
        
        # 2. 处理评论
        self.process_comments(comments)
        
        # 3. 导出结果
        return self.export_to_excel(output_filename)

def main():
    """
    主函数
    """
    # 数据库配置
    db_config = {
        'host': 'localhost',  # 请修改为实际的数据库地址
        'user': 'your_username',  # 请修改为实际的用户名
        'password': 'your_password',  # 请修改为实际的密码
        'database': 'your_database'  # 请修改为实际的数据库名
    }
    
    try:
        # 创建过滤器实例
        filter_instance = CommentFilter(db_config)
        
        # 运行过滤流程
        output_file = filter_instance.run()
        
        if output_file:
            print(f"\n处理完成！结果已保存到: {output_file}")
        else:
            print("\n没有发现垃圾内容或处理失败")
            
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"程序执行失败: {e}")

if __name__ == "__main__":
    main()