# 评论内容垃圾检测工具

这个工具用于从MySQL数据库读取评论数据，调用API检测垃圾内容和敏感内容，并将检测结果导出到Excel文件。

## 功能特性

- 从MySQL数据库读取`comment_audit_record`表中`source_type != 1`的评论数据
- 调用垃圾检测API检测内容是否为垃圾或包含敏感词
- 支持批量处理和并发处理
- 自动重试机制，提高API调用成功率
- 详细的日志记录和进度显示
- 将检测到的垃圾内容导出到Excel文件
- 生成处理摘要报告

## 文件说明

- `filter.py` - 基础版本的垃圾检测脚本
- `filter_advanced.py` - 高级版本，支持配置文件、并发处理、重试机制等
- `config_example.py` - 配置文件示例
- `requirements.txt` - Python依赖包列表
- `README.md` - 使用说明文档
- `start.py` - 一键启动脚本

## 快速开始

### 🚀 一键启动（推荐）

使用交互式启动脚本，自动选择最佳配置：

```bash
python3 start.py
```

### 📋 手动配置

#### 1. 安装依赖

```bash
pip install -r requirements.txt
```

#### 2. 配置数据库和API

复制配置文件模板：
```bash
cp config_example.py config.py
```

编辑 `config.py` 文件，填入你的数据库连接信息和API配置：

```python
# 数据库配置
DB_CONFIG = {
    'host': 'your_database_host',
    'user': 'your_username', 
    'password': 'your_password',
    'database': 'your_database_name',
    'port': 3306,
    'charset': 'utf8mb4'
}

# API配置
API_CONFIG = {
    'url': 'http://api.x.me/textshield/api/v1/spam/detect',
    'timeout': 10,
    'retry_count': 3,
    'delay_between_requests': 0.1
}
```

## 使用方法

### 🚀 推荐方式：一键启动

```bash
python3 start.py
```

交互式界面提供以下选项：
- 🧪 测试模式：小规模数据测试
- 🏭 生产模式：大数据量处理
- 🛠️ 自定义模式：灵活配置参数
- 📊 监控工具：实时查看处理进度

### 📊 进度监控

在处理过程中，可以启动监控工具实时查看进度：

```bash
python3 monitor.py
```

监控功能：
- ⚡ 实时处理速度和进度
- 🧠 系统资源使用情况
- 🚫 垃圾内容检测统计
- ❌ 错误和异常监控

### 💻 命令行方式

#### 基础版本

```bash
python filter.py
```

#### 高级版本 - 小数据量

```bash
# 基本使用
python filter_advanced.py

# 限制处理1000条记录
python filter_advanced.py --limit 1000

# 使用10个并发线程
python filter_advanced.py --workers 10

# 指定输出文件名
python filter_advanced.py --output spam_comments_20240107.xlsx

# 组合使用
python filter_advanced.py --limit 5000 --workers 8 --output my_spam_report.xlsx
```

#### 高级版本 - 大数据量（2000万+记录）

```bash
# 流式处理模式（推荐用于大数据量）
python filter_advanced.py --stream --batch-size 2000 --workers 8 --auto-save 50000

# 超大数据量优化配置
python filter_advanced.py --stream --batch-size 3000 --workers 10 --auto-save 100000

# 内存受限环境
python filter_advanced.py --stream --batch-size 1000 --workers 3 --auto-save 20000
```

## 输出文件

检测到的垃圾内容将导出到Excel文件，包含以下字段：

- `content_id` - 内容ID
- `comment_id` - 评论ID
- `content` - 评论内容
- `user_id` - 用户ID
- `type` - 类型（1=评论，2=回复）
- `is_spam` - 是否为垃圾内容
- `has_sensitive` - 是否包含敏感词
- `confidence` - 置信度
- `risk_level` - 风险等级
- `content_type` - 内容类型
- `detected_patterns` - 检测到的模式
- `sensitive_words` - 敏感词列表
- `sensitive_count` - 敏感词数量
- `max_level` - 最高敏感等级
- `processing_time` - 处理时间

## API接口说明

工具调用的垃圾检测API：
- **URL**: `http://api.x.me/textshield/api/v1/spam/detect`
- **方法**: POST
- **参数**:
  ```json
  {
    "text": "要检测的文本内容",
    "check_sensitive": true,
    "use_multilingual": true
  }
  ```

## 日志文件

程序运行时会生成日志文件`comment_filter.log`，记录：
- 处理进度
- 发现的垃圾内容
- API调用错误
- 数据库连接状态
- 导出结果

## 性能优化建议

1. **并发数设置**: 根据API服务器性能调整`--workers`参数，建议5-10个并发
2. **批量处理**: 对于大量数据，可以使用`--limit`参数分批处理
3. **网络优化**: 确保网络连接稳定，API调用有重试机制
4. **数据库连接**: 使用连接池可以提高数据库查询性能

## 错误处理

- **数据库连接失败**: 检查数据库配置和网络连接
- **API调用失败**: 程序会自动重试，检查API服务状态
- **内存不足**: 使用`--limit`参数限制处理数量
- **文件写入失败**: 检查磁盘空间和文件权限

## 注意事项

1. 确保数据库用户有读取`comment_audit_record`表的权限
2. API调用有频率限制，程序已加入延时控制
3. 大量数据处理时建议在服务器环境运行
4. 定期清理日志文件，避免占用过多磁盘空间

## 故障排除

### 常见问题

**Q: 数据库连接失败**
A: 检查config.py中的数据库配置信息是否正确，确认数据库服务正常运行

**Q: API调用超时**
A: 检查网络连接，可以在config.py中调整timeout参数

**Q: Excel文件无法打开**
A: 确保安装了openpyxl库，检查文件权限

**Q: 内存占用过高**
A: 使用--limit参数限制处理数量，或减少并发数

## 🛠️ 工具文件说明

### 核心处理脚本
- `filter.py` - 基础版本，适合小数据量测试
- `filter_advanced.py` - 高级版本，支持大数据量和并发处理
- `start.py` - 一键启动脚本，提供交互式界面
- `monitor.py` - 实时监控工具，显示处理进度和系统状态

### 配置文件
- `config_example.py` - 配置文件模板
- `config_big_data.py` - 大数据量专用配置，包含性能优化参数
- `optimize_database.sql` - 数据库索引优化SQL脚本

### 文档
- `README.md` - 基础使用说明
- `PERFORMANCE_GUIDE.md` - 大数据量性能优化指南
- `requirements.txt` - Python依赖包列表

### 使用建议

**小于10万条记录**：
```bash
python3 start.py  # 选择测试模式
```

**10万-1000万条记录**：
```bash
python3 start.py  # 选择标准处理模式
```

**1000万+记录**：
```bash
python3 start.py  # 选择大数据流式处理模式
# 同时运行监控：python3 monitor.py
```

**数据库优化**：
```bash
# 在MySQL中执行索引优化
mysql -u username -p database_name < optimize_database.sql
```

## 📈 性能参考

| 数据量 | 推荐配置 | 预估处理时间 | 内存占用 |
|--------|---------|-------------|----------|
| 1万条 | 3并发, 1000批次 | 2-5分钟 | ~50MB |
| 10万条 | 5并发, 2000批次 | 20-40分钟 | ~100MB |
| 100万条 | 8并发, 3000批次 | 3-6小时 | ~200MB |
| 1000万条 | 10并发, 流式处理 | 1-2天 | ~300MB |
| 2000万条+ | 10-12并发, 流式处理 | 2-4天 | ~500MB |

*注意：实际性能取决于网络状况、API响应时间和服务器配置*

## 更新日志

### v2.0 - 大数据量优化版本
- ✨ 新增一键启动脚本 (`start.py`)
- 📊 新增实时监控工具 (`monitor.py`)
- 🚀 支持流式处理，可处理2000万+记录
- 💾 自动保存功能，防止数据丢失
- 🔧 数据库查询优化，避免OFFSET性能问题
- 📋 大数据量专用配置文件
- 📖 详细的性能优化指南

### v1.0 - 基础版本
- 基础的评论垃圾检测功能
- 支持MySQL数据库连接
- 支持API调用和重试机制
- Excel结果导出

- v1.0: 基础功能实现
- v2.0: 添加高级功能，支持配置文件和并发处理
