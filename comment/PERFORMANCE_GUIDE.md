# 大数据量性能优化指南

针对2000万+评论数据的处理优化方案

## 核心优化策略

### 1. 数据库查询优化

**问题**: 原始查询使用OFFSET会导致性能急剧下降
```sql
-- 慢查询（不推荐）
SELECT * FROM comment_audit_record WHERE source_type != 1 LIMIT 1000 OFFSET 1000000;
```

**解决方案**: 使用基于ID的范围查询
```sql
-- 快速查询（推荐）
SELECT * FROM comment_audit_record 
WHERE source_type != 1 AND deleted = 0 AND id > 1000000
ORDER BY id LIMIT 1000;
```

### 2. 建议添加的数据库索引

```sql
-- 核心索引（强烈推荐）
CREATE INDEX idx_source_type_deleted_id ON comment_audit_record(source_type, deleted, id);

-- 或者分别创建
CREATE INDEX idx_source_type ON comment_audit_record(source_type);
CREATE INDEX idx_deleted ON comment_audit_record(deleted);
CREATE INDEX idx_id ON comment_audit_record(id);
```

### 3. 内存优化策略

- **流式处理**: 避免一次性加载所有数据到内存
- **分批处理**: 每批处理1000-5000条记录
- **定期清理**: 处理完的数据及时释放内存
- **自动保存**: 定期保存结果避免数据丢失

## 使用方法

### 大数据量处理（推荐）

```bash
# 使用流式处理，适合2000万+数据
python filter_advanced.py --stream --batch-size 2000 --workers 8 --auto-save 20000

# 参数说明：
# --stream: 启用流式处理模式
# --batch-size 2000: 每批处理2000条记录
# --workers 8: 使用8个并发线程
# --auto-save 20000: 每处理2万条自动保存一次
```

### 中等数据量处理

```bash
# 处理100万条记录
python filter_advanced.py --limit 1000000 --workers 10

# 处理指定范围的数据
python filter_advanced.py --limit 500000 --workers 5 --output batch1_results.xlsx
```

### 小数据量测试

```bash
# 测试前1万条
python filter_advanced.py --limit 10000 --workers 3
```

## 性能参数调优

### 批次大小 (batch-size)

| 数据量 | 推荐批次大小 | 说明 |
|--------|-------------|------|
| < 10万 | 1000 | 标准大小 |
| 10万-100万 | 2000-3000 | 平衡内存和效率 |
| 100万-1000万 | 3000-5000 | 提高吞吐量 |
| > 1000万 | 2000-3000 | 避免内存压力 |

### 并发数 (workers)

| 服务器配置 | 推荐并发数 | 说明 |
|-----------|-----------|------|
| 2核4G | 3-5 | 避免资源争抢 |
| 4核8G | 5-8 | 平衡CPU和内存 |
| 8核16G | 8-12 | 充分利用资源 |
| 16核32G+ | 10-15 | 注意API限制 |

### 自动保存间隔 (auto-save)

- **小于100万**: 不需要自动保存
- **100万-500万**: 每10万条保存一次
- **500万-2000万**: 每20万条保存一次
- **大于2000万**: 每50万条保存一次

## 监控和故障处理

### 实时监控

脚本会自动输出以下信息：
- 处理进度和速度
- 内存使用情况
- API调用成功率
- 发现的垃圾内容统计

### 中断恢复

```bash
# 如果处理中断，可以从指定位置继续
# 查看日志找到最后处理的ID，然后重新开始
grep "批次处理完成" comment_filter.log | tail -1
```

### 常见问题处理

**内存不足**:
```bash
# 减少批次大小和并发数
python filter_advanced.py --stream --batch-size 1000 --workers 3
```

**API调用过于频繁**:
```bash
# 在config.py中增加延时
API_CONFIG = {
    'delay_between_requests': 0.2  # 增加到200ms
}
```

**数据库连接超时**:
```bash
# 在config.py中增加超时时间
DB_CONFIG = {
    'connect_timeout': 60,
    'read_timeout': 60,
    'write_timeout': 60
}
```

## 预期性能表现

### 处理速度估算

基于不同配置的预期处理速度：

| 配置 | 预期速度 | 2000万数据耗时 |
|------|---------|---------------|
| 基础配置 (3并发) | 50-100条/秒 | 55-111小时 |
| 标准配置 (5并发) | 100-200条/秒 | 28-55小时 |
| 高性能配置 (10并发) | 200-400条/秒 | 14-28小时 |
| 极限配置 (15并发) | 300-500条/秒 | 11-18小时 |

*注意：实际速度取决于网络状况、API响应时间和服务器性能*

### 资源消耗估算

| 批次大小 | 内存占用 | 磁盘空间 |
|---------|---------|---------|
| 1000条 | ~50MB | 日志~100MB |
| 2000条 | ~100MB | 日志~200MB |
| 5000条 | ~250MB | 日志~500MB |

## 最佳实践建议

1. **分阶段处理**: 先处理一小部分数据测试效果
2. **错峰运行**: 在系统负载较低时运行
3. **监控资源**: 关注CPU、内存、网络使用情况
4. **备份数据**: 处理前备份原始数据
5. **日志分析**: 定期分析日志优化参数

## 紧急处理方案

如果需要快速处理，可以考虑：

1. **多机并行**: 在多台服务器上同时运行，按ID范围分片
2. **优先处理**: 先处理最新的数据
3. **采样处理**: 随机采样处理部分数据

```bash
# 多机并行示例
# 机器1: 处理ID 1-5000000
# 机器2: 处理ID 5000001-10000000
# 机器3: 处理ID 10000001-15000000
# 机器4: 处理ID 15000001-20000000
```
