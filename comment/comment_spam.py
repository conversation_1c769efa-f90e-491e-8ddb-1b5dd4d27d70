#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评论内容垃圾检测脚本（高级版本）
支持配置文件、批量处理、错误重试等功能
"""

import pymysql
import requests

import json
import time
from datetime import datetime
import logging
from typing import List, Dict, Any, Optional
import os
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse

# 尝试导入配置文件
try:
    from config import DB_CONFIG, API_CONFIG, OUTPUT_CONFIG
except ImportError:
    print("警告: 未找到config.py配置文件，使用默认配置")
    print("请复制config_example.py为config.py并修改配置信息")

    DB_CONFIG = {
        'host': 'xme-prod-comment.cluster-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
        'user': 'audit',
        'password': '1IjzQ1Vk',
        'database': 'audit',
        'port': 3306
    }

    #DB_CONFIG = {
    #    'host': 'xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    #    'user': 'envtest-user',
    #    'password': 'k7xetf5YKB',
   #     'database': 'comment_audit',
   #     'port': 3306
   # }

    API_CONFIG = {
        'url': 'http://api.x.me/textshield/api/v1/spam/detect',
        'timeout': 10,
        'retry_count': 3,
        'delay_between_requests': 0.01
    }

    DELETE_API_CONFIG = {
        'url': 'http://int.x.me/internal/comment/delete',
        'timeout': 10,
        'retry_count': 1
    }

    OUTPUT_CONFIG = {
        'log_filename': 'comment_filter.log'
    }

class CommentFilterAdvanced:
    def __init__(self, db_config: Dict[str, Any] = None, api_config: Dict[str, Any] = None, delete_api_config: Dict[str, Any] = None):
        """
        初始化评论过滤器

        Args:
            db_config: 数据库配置
            api_config: API配置
            delete_api_config: 删除API配置
        """
        self.db_config = db_config or DB_CONFIG
        self.api_config = api_config or API_CONFIG
        self.delete_api_config = delete_api_config or DELETE_API_CONFIG
        self.deleted_comments = []
        self.processed_count = 0
        self.error_count = 0
        self.delete_success_count = 0
        self.delete_error_count = 0
        self.db_update_error_count = 0

        # 设置日志
        self.setup_logging()

    def setup_logging(self):
        """
        设置日志配置
        """
        log_filename = OUTPUT_CONFIG.get('log_filename', 'comment_filter.log')

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def get_database_connection(self):
        """
        获取数据库连接
        """
        try:
            connection = pymysql.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                port=self.db_config.get('port', 3306),
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise

    def get_total_count(self) -> int:
        """
        获取需要处理的总记录数

        Returns:
            总记录数
        """
        connection = None
        try:
            connection = self.get_database_connection()

            with connection.cursor() as cursor:
                sql = """
                SELECT COUNT(*) as total
                FROM comment_audit_record
                WHERE source_type != 1 AND deleted = 0
                """

                cursor.execute(sql)
                result = cursor.fetchone()
                total = result['total'] if result else 0

                self.logger.info(f"需要处理的总记录数: {total}")
                return total

        except Exception as e:
            self.logger.error(f"获取总记录数失败: {e}")
            raise
        finally:
            if connection:
                connection.close()

    def fetch_comments_batch(self, batch_size: int = 1000, start_id: int = 0) -> List[Dict[str, Any]]:
        """
        批量获取评论数据（基于ID范围，避免OFFSET性能问题）

        Args:
            batch_size: 批次大小
            start_id: 起始ID

        Returns:
            评论数据列表
        """
        connection = None
        try:
            connection = self.get_database_connection()

            with connection.cursor() as cursor:
                # 使用ID范围查询，避免OFFSET的性能问题
                sql = """
                SELECT id, comment_id, content_id, user_id, content, type
                FROM comment_audit_record
                WHERE source_type != 1 AND deleted = 0 AND id > %s
                ORDER BY id
                LIMIT %s
                """

                cursor.execute(sql, (start_id, batch_size))
                results = cursor.fetchall()

                return results

        except Exception as e:
            self.logger.error(f"批量获取评论数据失败: {e}")
            raise
        finally:
            if connection:
                connection.close()

    def fetch_comments_stream(self, batch_size: int = 1000):
        """
        流式获取评论数据生成器（内存友好）

        Args:
            batch_size: 每批处理的记录数

        Yields:
            评论数据批次
        """
        last_id = 0
        total_fetched = 0

        while True:
            batch = self.fetch_comments_batch(batch_size, last_id)

            if not batch:
                break

            total_fetched += len(batch)
            self.logger.info(f"获取批次数据: {len(batch)} 条，累计: {total_fetched} 条")

            yield batch

            # 更新last_id为当前批次的最大ID
            last_id = max(item['id'] for item in batch)

            # 如果这批数据少于batch_size，说明已经是最后一批
            if len(batch) < batch_size:
                break

    def fetch_comments(self, limit: Optional[int] = None, offset: int = 0) -> List[Dict[str, Any]]:
        """
        从数据库获取需要检测的评论数据（兼容性方法）
        注意：对于大数据量，建议使用fetch_comments_stream方法

        Args:
            limit: 限制获取的记录数
            offset: 偏移量（大数据量时性能差，不推荐使用）

        Returns:
            评论数据列表
        """
        if limit and limit <= 10000:  # 小数据量直接返回
            return self.fetch_comments_batch(limit, 0)
        else:
            # 大数据量时警告用户
            self.logger.warning("检测到大数据量查询，建议使用流式处理方法以避免内存问题")
            if limit:
                return self.fetch_comments_batch(limit, 0)
            else:
                # 如果没有限制，返回第一批数据并警告
                self.logger.warning("未指定limit，仅返回前1000条数据，建议使用run_stream方法处理全部数据")
                return self.fetch_comments_batch(1000, 0)

    def detect_spam_with_retry(self, text: str) -> Optional[Dict[str, Any]]:
        """
        调用API检测垃圾内容（带重试机制）

        Args:
            text: 要检测的文本内容

        Returns:
            API响应结果
        """
        retry_count = self.api_config.get('retry_count', 3)
        timeout = self.api_config.get('timeout', 10)

        payload = {
            "text": text,
            "check_sensitive": True,
            "use_multilingual": True
        }

        for attempt in range(retry_count):
            try:
                response = requests.post(
                    self.api_config['url'],
                    json=payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    self.logger.warning(f"API请求失败，状态码: {response.status_code}, 尝试 {attempt + 1}/{retry_count}")

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"API请求异常: {e}, 尝试 {attempt + 1}/{retry_count}")

            # 如果不是最后一次尝试，等待一段时间后重试
            if attempt < retry_count - 1:
                time.sleep(1 * (attempt + 1))  # 递增等待时间

        self.logger.error(f"API请求失败，已重试 {retry_count} 次")
        return None

    def delete_comment_with_retry(self, content_id: str, comment_id: str, comment_type: int, user_id: str) -> bool:
        """
        调用API删除评论（带重试机制）

        Args:
            content_id: 内容ID
            comment_id: 评论ID
            comment_type: 评论类型
            user_id: 用户ID

        Returns:
            删除是否成功
        """
        retry_count = self.delete_api_config.get('retry_count', 3)
        timeout = self.delete_api_config.get('timeout', 10)

        payload = {
            "content_id": str(content_id),
            "comment_id": str(comment_id),
            "type": comment_type
        }

        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': str(user_id)
        }

        self.logger.info(f"准备删除评论: payload={payload}, headers={headers}")

        for attempt in range(retry_count):
            try:
                response = requests.post(
                    self.delete_api_config['url'],
                    json=payload,
                    headers=headers,
                    timeout=timeout
                )

                if response.status_code == 200:
                    try:
                        response_data = response.json()
                        # 检查响应体中的success字段
                        if response_data.get('success', False):
                            self.logger.info(f"成功删除评论: comment_id={comment_id}, content_id={content_id}, response={response_data}")
                            return True
                        else:
                            self.logger.warning(f"删除评论失败，API返回错误: comment_id={comment_id}, response={response_data}, 尝试 {attempt + 1}/{retry_count}")
                    except:
                        response_text = response.text
                        self.logger.info(f"成功删除评论: comment_id={comment_id}, content_id={content_id}, response_text='{response_text}'")
                        return True
                else:
                    self.logger.warning(f"删除评论失败，状态码: {response.status_code}, 尝试 {attempt + 1}/{retry_count}")

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"删除评论API请求异常: {e}, 尝试 {attempt + 1}/{retry_count}")

            # 如果不是最后一次尝试，等待一段时间后重试
            if attempt < retry_count - 1:
                time.sleep(1 * (attempt + 1))  # 递增等待时间

        self.logger.error(f"删除评论失败，已重试 {retry_count} 次: comment_id={comment_id}")
        return False

    def update_comment_deleted_status(self, comment_id: str, content_id: str) -> bool:
        """
        在数据库中设置评论为已删除状态

        Args:
            comment_id: 评论ID
            content_id: 内容ID

        Returns:
            更新是否成功
        """
        connection = None
        try:
            connection = self.get_database_connection()

            with connection.cursor() as cursor:
                sql = """
                UPDATE comment_audit_record
                SET deleted = 1, updated_time = NOW()
                WHERE comment_id = %s AND content_id = %s AND deleted = 0
                """

                affected_rows = cursor.execute(sql, (comment_id, content_id))
                connection.commit()

                if affected_rows > 0:
                    self.logger.info(f"成功更新数据库记录: comment_id={comment_id}, content_id={content_id}")
                    return True
                else:
                    self.logger.warning(f"数据库中未找到对应记录: comment_id={comment_id}, content_id={content_id}")
                    return False

        except Exception as e:
            self.logger.error(f"更新数据库记录失败: comment_id={comment_id}, error={e}")
            if connection:
                connection.rollback()
            return False
        finally:
            if connection:
                connection.close()

    def process_single_comment(self, comment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理单条评论

        Args:
            comment: 评论数据

        Returns:
            垃圾评论信息或None
        """
        try:
            content = comment['content']
            if not content or not content.strip():
                return None

            # 调用API检测
            result = self.detect_spam_with_retry(content)

            if result and result.get('success'):
                result_data = result.get('result', {})
                is_spam = result_data.get('is_spam', False)
                has_sensitive = result_data.get('has_sensitive', False)

                # 如果检测到垃圾内容或敏感内容，调用删除API
                if is_spam or has_sensitive:
                    self.logger.info(f"发现垃圾内容: comment_id={comment['comment_id']}, content_id={comment['content_id']}, is_spam={is_spam}, has_sensitive={has_sensitive}, content='{content}'")

                    # 调用删除API
                    delete_success = self.delete_comment_with_retry(
                        content_id=comment['content_id'],
                        comment_id=comment['comment_id'],
                        comment_type=comment['type'],
                        user_id=comment['user_id']
                    )

                    if delete_success:
                        # API删除成功后，更新数据库记录
                        db_update_success = self.update_comment_deleted_status(
                            comment_id=comment['comment_id'],
                            content_id=comment['content_id']
                        )

                        if db_update_success:
                            self.delete_success_count += 1
                            # 记录删除成功的评论信息
                            deleted_info = {
                                'content_id': comment['content_id'],
                                'comment_id': comment['comment_id'],
                                'user_id': comment['user_id'],
                                'type': comment['type'],
                                'is_spam': is_spam,
                                'has_sensitive': has_sensitive,
                                'confidence': result_data.get('confidence', 0),
                                'risk_level': result_data.get('risk_level', ''),
                                'deleted_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            }
                            self.deleted_comments.append(deleted_info)
                            return deleted_info
                        else:
                            self.logger.error(f"API删除成功但数据库更新失败: comment_id={comment['comment_id']}")
                            self.delete_error_count += 1
                    else:
                        self.delete_error_count += 1
                        self.logger.error(f"删除评论失败: comment_id={comment['comment_id']}")

            return None

        except Exception as e:
            self.logger.error(f"处理评论失败 comment_id={comment.get('comment_id')}: {e}")
            self.error_count += 1
            return None

    def process_comments_batch(self, comments: List[Dict[str, Any]], max_workers: int = 5) -> int:
        """
        批量处理评论数据

        Args:
            comments: 评论数据列表
            max_workers: 最大并发数

        Returns:
            本批次删除的评论数量
        """
        if not comments:
            return 0

        batch_count = len(comments)
        batch_deleted_count = 0
        batch_start_time = time.time()

        self.logger.info(f"开始处理批次: {batch_count} 条评论，并发数: {max_workers}")

        # 使用线程池进行并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_comment = {
                executor.submit(self.process_single_comment, comment): comment
                for comment in comments
            }

            # 处理完成的任务
            for i, future in enumerate(as_completed(future_to_comment), 1):
                try:
                    deleted_info = future.result()
                    if deleted_info:
                        batch_deleted_count += 1

                    self.processed_count += 1

                    # 批次内进度显示
                    if i % 100 == 0 or i == batch_count:
                        batch_progress = i / batch_count * 100
                        elapsed = time.time() - batch_start_time
                        rate = i / elapsed if elapsed > 0 else 0
                        self.logger.info(f"批次进度: {i}/{batch_count} ({batch_progress:.1f}%), 处理速度: {rate:.1f}/s, 已删除: {batch_deleted_count}")

                    # 控制请求频率
                    time.sleep(self.api_config.get('delay_between_requests', 0.1))

                except Exception as e:
                    self.logger.error(f"处理任务时发生异常: {e}")
                    self.error_count += 1

        batch_time = time.time() - batch_start_time
        self.logger.info(f"批次处理完成！耗时: {batch_time:.1f}s, 处理: {batch_count}, 已删除: {batch_deleted_count}, 删除错误: {self.delete_error_count}, 处理错误: {self.error_count}")
        return batch_deleted_count



    def generate_summary_report(self) -> Dict[str, Any]:
        """
        生成处理摘要报告

        Returns:
            摘要报告
        """
        deleted_count = len(self.deleted_comments)
        delete_rate = deleted_count / self.processed_count * 100 if self.processed_count > 0 else 0

        # 统计各种类型的删除内容
        spam_only = sum(1 for item in self.deleted_comments if item['is_spam'] and not item['has_sensitive'])
        sensitive_only = sum(1 for item in self.deleted_comments if item['has_sensitive'] and not item['is_spam'])
        both = sum(1 for item in self.deleted_comments if item['is_spam'] and item['has_sensitive'])

        # 统计风险等级分布
        risk_levels = {}
        for item in self.deleted_comments:
            level = item.get('risk_level', 'unknown')
            risk_levels[level] = risk_levels.get(level, 0) + 1

        report = {
            'total_processed': self.processed_count,
            'total_deleted': deleted_count,
            'delete_success_count': self.delete_success_count,
            'delete_error_count': self.delete_error_count,
            'delete_rate': round(delete_rate, 2),
            'processing_error_count': self.error_count,
            'deleted_breakdown': {
                'spam_only': spam_only,
                'sensitive_only': sensitive_only,
                'both': both
            },
            'risk_level_distribution': risk_levels,
            'processing_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return report

    def run_stream(self, batch_size: int = 1000, max_workers: int = 5) -> bool:
        """
        流式处理大数据量（推荐用于2000万+数据）

        Args:
            batch_size: 每批处理的记录数
            max_workers: 最大并发数

        Returns:
            处理是否成功
        """
        self.logger.info(f"开始流式处理大数据量，批次大小: {batch_size}, 并发数: {max_workers}")

        start_time = time.time()
        total_deleted_count = 0

        try:
            # 获取总数（可选，用于进度显示）
            try:
                total_count = self.get_total_count()
                self.logger.info(f"预计需要处理 {total_count} 条记录")
            except:
                total_count = 0
                self.logger.warning("无法获取总记录数，将无法显示总体进度")

            # 流式处理每个批次
            for batch_num, batch_comments in enumerate(self.fetch_comments_stream(batch_size), 1):
                self.logger.info(f"\n=== 处理第 {batch_num} 批次 ===")

                # 处理当前批次
                batch_deleted_count = self.process_comments_batch(batch_comments, max_workers)
                total_deleted_count += batch_deleted_count

                # 显示总体进度
                if total_count > 0:
                    overall_progress = self.processed_count / total_count * 100
                    self.logger.info(f"总体进度: {self.processed_count}/{total_count} ({overall_progress:.2f}%)")



                # 显示处理统计
                elapsed = time.time() - start_time
                rate = self.processed_count / elapsed if elapsed > 0 else 0
                self.logger.info(f"累计统计: 已处理 {self.processed_count}, 已删除 {total_deleted_count}, 速度 {rate:.1f}/s")

            # 最终处理完成
            total_time = time.time() - start_time
            self.logger.info(f"\n=== 流式处理完成 ===")
            self.logger.info(f"总耗时: {total_time:.1f}s, 平均速度: {self.processed_count/total_time:.1f}/s")
            return True

        except KeyboardInterrupt:
            self.logger.info("\n用户中断处理，正在生成最终报告...")
        except Exception as e:
            self.logger.error(f"流式处理过程中发生错误: {e}")
            return False
        finally:
            # 生成最终报告
            report = self.generate_summary_report()
            self.logger.info(f"最终处理摘要: {json.dumps(report, ensure_ascii=False, indent=2)}")

    def run(self, limit: Optional[int] = None, max_workers: int = 5) -> bool:
        """
        运行完整的过滤流程（适用于小到中等数据量）

        Args:
            limit: 限制处理的记录数
            max_workers: 最大并发数

        Returns:
            处理是否成功
        """
        # 对于大数据量，建议使用流式处理
        if limit is None or limit > 100000:
            self.logger.warning("检测到大数据量处理需求，建议使用 run_stream() 方法")
            self.logger.warning("如果坚持使用当前方法，请设置合理的limit参数")

        self.logger.info("开始评论垃圾内容检测和删除")

        try:
            # 1. 获取评论数据
            comments = self.fetch_comments(limit=limit)

            if not comments:
                self.logger.warning("没有找到需要处理的评论数据")
                return False

            # 2. 处理评论
            self.process_comments_batch(comments, max_workers=max_workers)

            # 3. 生成摘要报告
            report = self.generate_summary_report()
            self.logger.info(f"处理摘要: {json.dumps(report, ensure_ascii=False, indent=2)}")

            return True

        except Exception as e:
            self.logger.error(f"处理过程中发生错误: {e}")
            return False

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='评论垃圾内容检测和删除工具')
    parser.add_argument('--limit', type=int, help='限制处理的记录数')
    parser.add_argument('--workers', type=int, default=5, help='最大并发数 (默认: 5)')
    parser.add_argument('--stream', action='store_true', help='使用流式处理（推荐大数据量）')
    parser.add_argument('--batch-size', type=int, default=1000, help='批次大小 (默认: 1000)')

    args = parser.parse_args()

    try:
        # 创建过滤器实例
        filter_instance = CommentFilterAdvanced()

        # 根据参数选择处理方式
        if args.stream:
            print("使用流式处理模式（适合大数据量）...")
            success = filter_instance.run_stream(
                batch_size=args.batch_size,
                max_workers=args.workers
            )
        else:
            print("使用标准处理模式...")
            success = filter_instance.run(
                limit=args.limit,
                max_workers=args.workers
            )

        if success:
            print(f"\n处理完成！已成功删除 {filter_instance.delete_success_count} 条垃圾评论")
        else:
            print("\n处理失败或没有发现需要删除的评论")
            sys.exit(1)

    except Exception as e:
        print(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
