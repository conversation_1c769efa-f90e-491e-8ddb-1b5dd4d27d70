#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件示例
请复制此文件为 config.py 并修改为实际的配置信息
"""

# 数据库配置
DB_CONFIG = {
    'host': 'your_database_host',      # 数据库主机地址，如: localhost 或 *************
    'user': 'your_username',           # 数据库用户名
    'password': 'your_password',       # 数据库密码
    'database': 'your_database_name',  # 数据库名
    'port': 3306                       # 数据库端口，默认3306
}

# API配置
API_CONFIG = {
    'url': 'http://api.x.me/textshield/api/v1/spam/detect',  # 垃圾检测API地址
    'timeout': 10,                     # 请求超时时间（秒）
    'retry_count': 3,                  # 重试次数
    'delay_between_requests': 0.1      # 请求间隔时间（秒）
}

# 输出配置
OUTPUT_CONFIG = {
    'excel_filename': None,            # Excel文件名，None表示自动生成时间戳文件名
    'log_filename': 'comment_filter.log'  # 日志文件名
}
