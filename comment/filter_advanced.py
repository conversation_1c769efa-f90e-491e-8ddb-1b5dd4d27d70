#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评论内容垃圾检测脚本（高级版本）
支持配置文件、批量处理、错误重试等功能
"""

import pymysql
import requests
import pandas as pd
import json
import time
from datetime import datetime
import logging
from typing import List, Dict, Any, Optional
import os
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse

# 尝试导入配置文件
try:
    from config import DB_CONFIG, API_CONFIG, OUTPUT_CONFIG
except ImportError:
    print("警告: 未找到config.py配置文件，使用默认配置")
    print("请复制config_example.py为config.py并修改配置信息")

    #DB_CONFIG = {
    #    'host': 'xme-prod-comment.cluster-ro-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
    #    'user': 'comment_readonly',
    #    'password': 'xYjU9sb4',
    #    'database': 'audit',
    #    'port': 3306
    #}

    DB_CONFIG = {
        'host': 'xme-prod-comment.cluster-ro-chkycqw22fzd.ap-southeast-1.rds.amazonaws.com',
        'user': 'comment_readonly',
        'password': 'xYjU9sb4',
        'database': 'audit',
        'port': 3306
    }

    API_CONFIG = {
        'url': 'http://api.x.me/textshield/api/v1/spam/detect',
        'timeout': 10,
        'retry_count': 3,
        'delay_between_requests': 0.01
    }

    OUTPUT_CONFIG = {
        'excel_filename': None,
        'log_filename': 'comment_filter.log'
    }

class CommentFilterAdvanced:
    def __init__(self, db_config: Dict[str, Any] = None, api_config: Dict[str, Any] = None):
        """
        初始化评论过滤器

        Args:
            db_config: 数据库配置
            api_config: API配置
        """
        self.db_config = db_config or DB_CONFIG
        self.api_config = api_config or API_CONFIG
        self.spam_comments = []
        self.processed_count = 0
        self.error_count = 0

        # 设置日志
        self.setup_logging()

    def setup_logging(self):
        """
        设置日志配置
        """
        log_filename = OUTPUT_CONFIG.get('log_filename', 'comment_filter.log')

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def get_database_connection(self):
        """
        获取数据库连接
        """
        try:
            connection = pymysql.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                port=self.db_config.get('port', 3306),
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise

    def get_total_count(self) -> int:
        """
        获取需要处理的总记录数

        Returns:
            总记录数
        """
        connection = None
        try:
            connection = self.get_database_connection()

            with connection.cursor() as cursor:
                sql = """
                SELECT COUNT(*) as total
                FROM comment_audit_record
                WHERE source_type != 1 AND deleted = 0
                """

                cursor.execute(sql)
                result = cursor.fetchone()
                total = result['total'] if result else 0

                self.logger.info(f"需要处理的总记录数: {total}")
                return total

        except Exception as e:
            self.logger.error(f"获取总记录数失败: {e}")
            raise
        finally:
            if connection:
                connection.close()

    def fetch_comments_batch(self, batch_size: int = 1000, start_id: int = 0) -> List[Dict[str, Any]]:
        """
        批量获取评论数据（基于ID范围，避免OFFSET性能问题）

        Args:
            batch_size: 批次大小
            start_id: 起始ID

        Returns:
            评论数据列表
        """
        connection = None
        try:
            connection = self.get_database_connection()

            with connection.cursor() as cursor:
                # 使用ID范围查询，避免OFFSET的性能问题
                sql = """
                SELECT id, comment_id, content_id, user_id, content, type
                FROM comment_audit_record
                WHERE source_type != 1 AND deleted = 0 AND id > %s
                ORDER BY id
                LIMIT %s
                """

                cursor.execute(sql, (start_id, batch_size))
                results = cursor.fetchall()

                return results

        except Exception as e:
            self.logger.error(f"批量获取评论数据失败: {e}")
            raise
        finally:
            if connection:
                connection.close()

    def fetch_comments_stream(self, batch_size: int = 1000):
        """
        流式获取评论数据生成器（内存友好）

        Args:
            batch_size: 每批处理的记录数

        Yields:
            评论数据批次
        """
        last_id = 0
        total_fetched = 0

        while True:
            batch = self.fetch_comments_batch(batch_size, last_id)

            if not batch:
                break

            total_fetched += len(batch)
            self.logger.info(f"获取批次数据: {len(batch)} 条，累计: {total_fetched} 条")

            yield batch

            # 更新last_id为当前批次的最大ID
            last_id = max(item['id'] for item in batch)

            # 如果这批数据少于batch_size，说明已经是最后一批
            if len(batch) < batch_size:
                break

    def fetch_comments(self, limit: Optional[int] = None, offset: int = 0) -> List[Dict[str, Any]]:
        """
        从数据库获取需要检测的评论数据（兼容性方法）
        注意：对于大数据量，建议使用fetch_comments_stream方法

        Args:
            limit: 限制获取的记录数
            offset: 偏移量（大数据量时性能差，不推荐使用）

        Returns:
            评论数据列表
        """
        if limit and limit <= 10000:  # 小数据量直接返回
            return self.fetch_comments_batch(limit, 0)
        else:
            # 大数据量时警告用户
            self.logger.warning("检测到大数据量查询，建议使用流式处理方法以避免内存问题")
            if limit:
                return self.fetch_comments_batch(limit, 0)
            else:
                # 如果没有限制，返回第一批数据并警告
                self.logger.warning("未指定limit，仅返回前1000条数据，建议使用run_stream方法处理全部数据")
                return self.fetch_comments_batch(1000, 0)

    def detect_spam_with_retry(self, text: str) -> Optional[Dict[str, Any]]:
        """
        调用API检测垃圾内容（带重试机制）

        Args:
            text: 要检测的文本内容

        Returns:
            API响应结果
        """
        retry_count = self.api_config.get('retry_count', 3)
        timeout = self.api_config.get('timeout', 10)

        payload = {
            "text": text,
            "check_sensitive": True,
            "use_multilingual": True
        }

        for attempt in range(retry_count):
            try:
                response = requests.post(
                    self.api_config['url'],
                    json=payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    self.logger.warning(f"API请求失败，状态码: {response.status_code}, 尝试 {attempt + 1}/{retry_count}")

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"API请求异常: {e}, 尝试 {attempt + 1}/{retry_count}")

            # 如果不是最后一次尝试，等待一段时间后重试
            if attempt < retry_count - 1:
                time.sleep(1 * (attempt + 1))  # 递增等待时间

        self.logger.error(f"API请求失败，已重试 {retry_count} 次")
        return None

    def process_single_comment(self, comment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理单条评论

        Args:
            comment: 评论数据

        Returns:
            垃圾评论信息或None
        """
        try:
            content = comment['content']
            if not content or not content.strip():
                return None

            # 调用API检测
            result = self.detect_spam_with_retry(content)

            if result and result.get('success'):
                result_data = result.get('result', {})
                is_spam = result_data.get('is_spam', False)
                has_sensitive = result_data.get('has_sensitive', False)

                # 如果检测到垃圾内容或敏感内容
                if is_spam or has_sensitive:
                    spam_info = {
                        'content_id': comment['content_id'],
                        'comment_id': comment['comment_id'],
                        'content': content,
                        'user_id': comment['user_id'],
                        'type': comment['type'],
                        'is_spam': is_spam,
                        'has_sensitive': has_sensitive,
                        'confidence': result_data.get('confidence', 0),
                        'risk_level': result_data.get('risk_level', ''),
                        'content_type': result_data.get('content_type', ''),
                        'detected_patterns': ', '.join(result_data.get('detected_patterns', [])),
                        'sensitive_words': ', '.join(result_data.get('sensitive_words', [])),
                        'sensitive_count': result_data.get('sensitive_count', 0),
                        'max_level': result_data.get('max_level', 0),
                        'processing_time': result_data.get('processing_time', 0)
                    }

                    self.logger.info(f"发现垃圾内容: comment_id={comment['comment_id']}, is_spam={is_spam}, has_sensitive={has_sensitive}")
                    return spam_info

            return None

        except Exception as e:
            self.logger.error(f"处理评论失败 comment_id={comment.get('comment_id')}: {e}")
            self.error_count += 1
            return None

    def process_comments_batch(self, comments: List[Dict[str, Any]], max_workers: int = 5) -> int:
        """
        批量处理评论数据

        Args:
            comments: 评论数据列表
            max_workers: 最大并发数

        Returns:
            本批次发现的垃圾内容数量
        """
        if not comments:
            return 0

        batch_count = len(comments)
        batch_spam_count = 0
        batch_start_time = time.time()

        self.logger.info(f"开始处理批次: {batch_count} 条评论，并发数: {max_workers}")

        # 使用线程池进行并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_comment = {
                executor.submit(self.process_single_comment, comment): comment
                for comment in comments
            }

            # 处理完成的任务
            for i, future in enumerate(as_completed(future_to_comment), 1):
                try:
                    spam_info = future.result()
                    if spam_info:
                        self.spam_comments.append(spam_info)
                        batch_spam_count += 1

                    self.processed_count += 1

                    # 批次内进度显示
                    if i % 100 == 0 or i == batch_count:
                        batch_progress = i / batch_count * 100
                        elapsed = time.time() - batch_start_time
                        rate = i / elapsed if elapsed > 0 else 0
                        self.logger.info(f"批次进度: {i}/{batch_count} ({batch_progress:.1f}%), 处理速度: {rate:.1f}/s, 发现垃圾: {batch_spam_count}")

                    # 控制请求频率
                    time.sleep(self.api_config.get('delay_between_requests', 0.1))

                except Exception as e:
                    self.logger.error(f"处理任务时发生异常: {e}")
                    self.error_count += 1

        batch_time = time.time() - batch_start_time
        self.logger.info(f"批次处理完成！耗时: {batch_time:.1f}s, 处理: {batch_count}, 发现垃圾: {batch_spam_count}, 错误: {self.error_count}")
        return batch_spam_count

    def export_to_excel(self, filename: str = None) -> Optional[str]:
        """
        导出垃圾评论到Excel文件

        Args:
            filename: 输出文件名

        Returns:
            导出的文件路径
        """
        if not self.spam_comments:
            self.logger.warning("没有发现垃圾评论，无需导出")
            return None

        if not filename:
            filename = OUTPUT_CONFIG.get('excel_filename')
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"spam_comments_{timestamp}.xlsx"

        try:
            # 创建DataFrame
            df = pd.DataFrame(self.spam_comments)

            # 重新排列列的顺序
            columns_order = [
                'content_id', 'comment_id', 'content', 'user_id', 'type',
                'is_spam', 'has_sensitive', 'confidence', 'risk_level', 'content_type',
                'detected_patterns', 'sensitive_words', 'sensitive_count', 'max_level', 'processing_time'
            ]

            df = df[columns_order]

            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='垃圾评论', index=False)

                # 调整列宽
                worksheet = writer.sheets['垃圾评论']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            self.logger.info(f"垃圾评论已导出到: {filename}")
            self.logger.info(f"导出记录数: {len(self.spam_comments)}")

            return filename

        except Exception as e:
            self.logger.error(f"导出Excel失败: {e}")
            raise

    def generate_summary_report(self) -> Dict[str, Any]:
        """
        生成处理摘要报告

        Returns:
            摘要报告
        """
        spam_count = len(self.spam_comments)
        spam_rate = spam_count / self.processed_count * 100 if self.processed_count > 0 else 0

        # 统计各种类型的垃圾内容
        spam_only = sum(1 for item in self.spam_comments if item['is_spam'] and not item['has_sensitive'])
        sensitive_only = sum(1 for item in self.spam_comments if item['has_sensitive'] and not item['is_spam'])
        both = sum(1 for item in self.spam_comments if item['is_spam'] and item['has_sensitive'])

        # 统计风险等级分布
        risk_levels = {}
        for item in self.spam_comments:
            level = item.get('risk_level', 'unknown')
            risk_levels[level] = risk_levels.get(level, 0) + 1

        report = {
            'total_processed': self.processed_count,
            'total_spam': spam_count,
            'spam_rate': round(spam_rate, 2),
            'error_count': self.error_count,
            'spam_breakdown': {
                'spam_only': spam_only,
                'sensitive_only': sensitive_only,
                'both': both
            },
            'risk_level_distribution': risk_levels,
            'processing_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return report

    def run_stream(self, batch_size: int = 1000, max_workers: int = 5, output_filename: str = None,
                   auto_save_interval: int = 10000) -> Optional[str]:
        """
        流式处理大数据量（推荐用于2000万+数据）

        Args:
            batch_size: 每批处理的记录数
            max_workers: 最大并发数
            output_filename: 输出文件名
            auto_save_interval: 自动保存间隔（处理多少条后保存一次）

        Returns:
            导出的文件路径
        """
        self.logger.info(f"开始流式处理大数据量，批次大小: {batch_size}, 并发数: {max_workers}")

        start_time = time.time()
        total_spam_count = 0
        last_save_count = 0

        try:
            # 获取总数（可选，用于进度显示）
            try:
                total_count = self.get_total_count()
                self.logger.info(f"预计需要处理 {total_count} 条记录")
            except:
                total_count = 0
                self.logger.warning("无法获取总记录数，将无法显示总体进度")

            # 流式处理每个批次
            for batch_num, batch_comments in enumerate(self.fetch_comments_stream(batch_size), 1):
                self.logger.info(f"\n=== 处理第 {batch_num} 批次 ===")

                # 处理当前批次
                batch_spam_count = self.process_comments_batch(batch_comments, max_workers)
                total_spam_count += batch_spam_count

                # 显示总体进度
                if total_count > 0:
                    overall_progress = self.processed_count / total_count * 100
                    self.logger.info(f"总体进度: {self.processed_count}/{total_count} ({overall_progress:.2f}%)")

                # 定期保存结果（避免数据丢失）
                if self.processed_count - last_save_count >= auto_save_interval:
                    if self.spam_comments:
                        temp_filename = f"temp_spam_comments_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                        self.export_to_excel(temp_filename)
                        self.logger.info(f"自动保存临时结果到: {temp_filename}")
                        last_save_count = self.processed_count

                # 显示处理统计
                elapsed = time.time() - start_time
                rate = self.processed_count / elapsed if elapsed > 0 else 0
                self.logger.info(f"累计统计: 已处理 {self.processed_count}, 发现垃圾 {total_spam_count}, 速度 {rate:.1f}/s")

                # 内存管理：定期清理已处理的数据（可选）
                if len(self.spam_comments) > 50000:  # 如果垃圾评论太多，可以分批保存
                    self.logger.info("检测到大量垃圾评论，进行分批保存...")
                    temp_filename = f"batch_spam_comments_{batch_num}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                    self.export_to_excel(temp_filename)
                    self.spam_comments.clear()  # 清空已保存的数据

            # 最终处理完成
            total_time = time.time() - start_time
            self.logger.info(f"\n=== 流式处理完成 ===")
            self.logger.info(f"总耗时: {total_time:.1f}s, 平均速度: {self.processed_count/total_time:.1f}/s")

        except KeyboardInterrupt:
            self.logger.info("\n用户中断处理，正在保存已处理的结果...")
        except Exception as e:
            self.logger.error(f"流式处理过程中发生错误: {e}")

        # 生成最终报告
        report = self.generate_summary_report()
        self.logger.info(f"最终处理摘要: {json.dumps(report, ensure_ascii=False, indent=2)}")

        # 导出最终结果
        if self.spam_comments:
            return self.export_to_excel(output_filename)
        else:
            self.logger.info("没有待导出的垃圾评论（可能已分批保存）")
            return None

    def run(self, limit: Optional[int] = None, max_workers: int = 5, output_filename: str = None) -> Optional[str]:
        """
        运行完整的过滤流程（适用于小到中等数据量）

        Args:
            limit: 限制处理的记录数
            max_workers: 最大并发数
            output_filename: 输出文件名

        Returns:
            导出的文件路径
        """
        # 对于大数据量，建议使用流式处理
        if limit is None or limit > 100000:
            self.logger.warning("检测到大数据量处理需求，建议使用 run_stream() 方法")
            self.logger.warning("如果坚持使用当前方法，请设置合理的limit参数")

        self.logger.info("开始评论垃圾内容检测")

        # 1. 获取评论数据
        comments = self.fetch_comments(limit=limit)

        if not comments:
            self.logger.warning("没有找到需要处理的评论数据")
            return None

        # 2. 处理评论
        self.process_comments_batch(comments, max_workers=max_workers)

        # 3. 生成摘要报告
        report = self.generate_summary_report()
        self.logger.info(f"处理摘要: {json.dumps(report, ensure_ascii=False, indent=2)}")

        # 4. 导出结果
        return self.export_to_excel(output_filename)

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='评论垃圾内容检测工具')
    parser.add_argument('--limit', type=int, help='限制处理的记录数')
    parser.add_argument('--workers', type=int, default=5, help='最大并发数 (默认: 5)')
    parser.add_argument('--output', type=str, help='输出文件名')
    parser.add_argument('--stream', action='store_true', help='使用流式处理（推荐大数据量）')
    parser.add_argument('--batch-size', type=int, default=1000, help='批次大小 (默认: 1000)')
    parser.add_argument('--auto-save', type=int, default=10000, help='自动保存间隔 (默认: 10000)')

    args = parser.parse_args()

    try:
        # 创建过滤器实例
        filter_instance = CommentFilterAdvanced()

        # 根据参数选择处理方式
        if args.stream:
            print("使用流式处理模式（适合大数据量）...")
            output_file = filter_instance.run_stream(
                batch_size=args.batch_size,
                max_workers=args.workers,
                output_filename=args.output,
                auto_save_interval=args.auto_save
            )
        else:
            print("使用标准处理模式...")
            output_file = filter_instance.run(
                limit=args.limit,
                max_workers=args.workers,
                output_filename=args.output
            )

        if output_file:
            print(f"\n处理完成！结果已保存到: {output_file}")
        else:
            print("\n没有发现垃圾内容或处理失败")

    except Exception as e:
        print(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
