#!/bin/bash
# 评论垃圾检测系统安装脚本

echo "🚀 评论垃圾检测系统安装脚本"
echo "================================"

# 检查Python版本
echo "📋 检查Python版本..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

# 检查pip
echo "📋 检查pip..."
pip3 --version
if [ $? -ne 0 ]; then
    echo "❌ pip3未安装，请先安装pip3"
    exit 1
fi

# 安装Python依赖
echo "📦 安装Python依赖包..."
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ 依赖包安装失败"
    exit 1
fi

# 创建配置文件
if [ ! -f "config.py" ]; then
    echo "📝 创建配置文件..."
    cp config_example.py config.py
    echo "✅ 已创建config.py，请编辑此文件配置数据库和API信息"
else
    echo "✅ config.py已存在"
fi

# 设置脚本执行权限
echo "🔧 设置脚本执行权限..."
chmod +x start.py
chmod +x monitor.py

# 检查数据库连接（可选）
echo ""
echo "🔍 系统检查完成！"
echo "================================"
echo "📋 安装清单："
echo "✅ Python3 已安装"
echo "✅ pip3 已安装"
echo "✅ Python依赖包已安装"
echo "✅ 配置文件已创建"
echo "✅ 脚本权限已设置"
echo ""
echo "📝 下一步操作："
echo "1. 编辑 config.py 文件，配置数据库连接和API信息"
echo "2. 运行 python3 start.py 开始使用"
echo "3. 可选：运行 mysql -u username -p database_name < optimize_database.sql 优化数据库"
echo ""
echo "🎉 安装完成！"
